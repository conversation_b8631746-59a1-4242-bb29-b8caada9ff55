{"@timestamp":"2025-07-28T00:49:07.037Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30632","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$57fa7fab] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-28T00:49:09.790Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30632","thread":"main","class":"o.s.b.t.j.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer","rest":"\r\n\r\nFound multiple occurrences of org.json.JSONObject on the class path:\r\n\r\n\tjar:file:/D:/maven/repository/org/json/json/20170516/json-20170516.jar!/org/json/JSONObject.class\r\n\tjar:file:/D:/maven/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class\r\n\r\nYou may wish to exclude one of them to ensure predictable runtime behavior\r\n"}
{"@timestamp":"2025-07-28T00:49:09.816Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30632","thread":"main","class":"com.xcwlkj.junit.MinimalDatabaseTest","rest":"The following profiles are active: database-test"}
{"@timestamp":"2025-07-28T00:49:20.462Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30632","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化####"}
{"@timestamp":"2025-07-28T00:49:20.571Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30632","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化成功####"}
{"@timestamp":"2025-07-28T00:49:21.905Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30632","thread":"main","class":"c.x.i.taskcenter.DefaultHandleFactory","rest":"配置线程池工作线程数量[16]"}
{"@timestamp":"2025-07-28T00:49:22.136Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30632","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"开始检查平台类型和初始化上级平台DFS客户端..."}
{"@timestamp":"2025-07-28T00:49:22.488Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30632","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"当前平台类型为：HISOME，开始初始化上级平台DFS客户端"}
{"@timestamp":"2025-07-28T00:49:22.533Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30632","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"上级平台DFS客户端初始化成功"}
{"@timestamp":"2025-07-28T00:49:22.534Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30632","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"上级平台DFS客户端自动初始化完成"}
{"@timestamp":"2025-07-28T00:49:34.562Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30632","thread":"main","class":"o.s.b.a.f.FreeMarkerAutoConfiguration","rest":"Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)"}
{"@timestamp":"2025-07-28T00:49:37.924Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30632","thread":"main","class":"com.xcwlkj.junit.MinimalDatabaseTest","rest":"Started MinimalDatabaseTest in 32.627 seconds (JVM running for 34.077)"}
{"@timestamp":"2025-07-28T00:49:38.006Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30632","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SnowflakeIdGenerator，init. workerId=0,datacenterId=0"}
{"@timestamp":"2025-07-28T00:49:38.006Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30632","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SnowflakeIdGenerator，finish<<<<<<<<<<<<<"}
{"@timestamp":"2025-07-28T00:49:38.007Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30632","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###ZookeeperInitRunner，init. HostAddress=**************:-1, applicationName=eeip-database-test"}
{"@timestamp":"2025-07-28T00:49:39.056Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30632","thread":"main","class":"o.springframework.boot.SpringApplication","rest":"Application run failed"}
{"@timestamp":"2025-07-28T00:49:39.059Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30632","thread":"main","class":"o.s.test.context.TestContextManager","rest":"Caught exception while allowing TestExecutionListener [org.springframework.test.context.web.ServletTestExecutionListener@571c5681] to prepare test instance [com.xcwlkj.junit.MinimalDatabaseTest@65ed2da6]"}
{"@timestamp":"2025-07-28T00:58:42.354Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"13948","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$61e96d89] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-28T00:58:46.098Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"13948","thread":"main","class":"o.s.b.t.j.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer","rest":"\r\n\r\nFound multiple occurrences of org.json.JSONObject on the class path:\r\n\r\n\tjar:file:/D:/maven/repository/org/json/json/20170516/json-20170516.jar!/org/json/JSONObject.class\r\n\tjar:file:/D:/maven/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class\r\n\r\nYou may wish to exclude one of them to ensure predictable runtime behavior\r\n"}
{"@timestamp":"2025-07-28T00:58:46.105Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"13948","thread":"main","class":"com.xcwlkj.junit.MinimalDatabaseTest","rest":"The following profiles are active: database-test"}
{"@timestamp":"2025-07-28T00:58:54.746Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"13948","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化####"}
{"@timestamp":"2025-07-28T00:59:55.114Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"13948","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"初始化兴畅DFS异常[发送 POST请求出现异常！]"}
{"@timestamp":"2025-07-28T00:59:55.117Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"13948","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化成功####"}
{"@timestamp":"2025-07-28T00:59:56.548Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"13948","thread":"main","class":"c.x.i.taskcenter.DefaultHandleFactory","rest":"配置线程池工作线程数量[16]"}
{"@timestamp":"2025-07-28T00:59:56.765Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"13948","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"开始检查平台类型和初始化上级平台DFS客户端..."}
{"@timestamp":"2025-07-28T01:00:35.177Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"13948","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"当前平台类型为：HISOME，开始初始化上级平台DFS客户端"}
{"@timestamp":"2025-07-28T01:00:35.322Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"13948","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"上级平台DFS客户端初始化成功"}
{"@timestamp":"2025-07-28T01:00:35.322Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"13948","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"上级平台DFS客户端自动初始化完成"}
{"@timestamp":"2025-07-28T01:00:46.960Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"13948","thread":"main","class":"o.s.b.a.f.FreeMarkerAutoConfiguration","rest":"Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)"}
{"@timestamp":"2025-07-28T01:00:50.057Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"13948","thread":"main","class":"com.xcwlkj.junit.MinimalDatabaseTest","rest":"Started MinimalDatabaseTest in 130.299 seconds (JVM running for 131.545)"}
{"@timestamp":"2025-07-28T01:00:50.128Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"13948","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SnowflakeIdGenerator，init. workerId=0,datacenterId=0"}
{"@timestamp":"2025-07-28T01:00:50.128Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"13948","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SnowflakeIdGenerator，finish<<<<<<<<<<<<<"}
{"@timestamp":"2025-07-28T01:00:50.130Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"13948","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###ZookeeperInitRunner，init. HostAddress=**************:-1, applicationName=eeip-database-test"}
{"@timestamp":"2025-07-28T01:00:51.174Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"13948","thread":"main","class":"o.springframework.boot.SpringApplication","rest":"Application run failed"}
{"@timestamp":"2025-07-28T01:00:51.176Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"13948","thread":"main","class":"o.s.test.context.TestContextManager","rest":"Caught exception while allowing TestExecutionListener [org.springframework.test.context.web.ServletTestExecutionListener@571c5681] to prepare test instance [com.xcwlkj.junit.MinimalDatabaseTest@12ebc4]"}
{"@timestamp":"2025-07-28T01:06:42.784Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"31540","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$10501d42] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-28T01:06:45.413Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"31540","thread":"main","class":"o.s.b.t.j.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer","rest":"\r\n\r\nFound multiple occurrences of org.json.JSONObject on the class path:\r\n\r\n\tjar:file:/D:/maven/repository/org/json/json/20170516/json-20170516.jar!/org/json/JSONObject.class\r\n\tjar:file:/D:/maven/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class\r\n\r\nYou may wish to exclude one of them to ensure predictable runtime behavior\r\n"}
{"@timestamp":"2025-07-28T01:06:45.420Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"31540","thread":"main","class":"com.xcwlkj.junit.MinimalDatabaseTest","rest":"The following profiles are active: database-test"}
{"@timestamp":"2025-07-28T01:06:53.927Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"31540","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化####"}
{"@timestamp":"2025-07-28T01:06:54.025Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"31540","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化成功####"}
{"@timestamp":"2025-07-28T01:06:55.297Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"31540","thread":"main","class":"c.x.i.taskcenter.DefaultHandleFactory","rest":"配置线程池工作线程数量[16]"}
{"@timestamp":"2025-07-28T01:06:55.489Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"31540","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"开始检查平台类型和初始化上级平台DFS客户端..."}
{"@timestamp":"2025-07-28T01:06:55.779Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"31540","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"当前平台类型为：HISOME，开始初始化上级平台DFS客户端"}
{"@timestamp":"2025-07-28T01:06:55.823Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"31540","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"上级平台DFS客户端初始化成功"}
{"@timestamp":"2025-07-28T01:06:55.823Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"31540","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"上级平台DFS客户端自动初始化完成"}
{"@timestamp":"2025-07-28T01:07:06.837Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"31540","thread":"main","class":"o.s.b.a.f.FreeMarkerAutoConfiguration","rest":"Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)"}
{"@timestamp":"2025-07-28T01:07:09.843Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"31540","thread":"main","class":"com.xcwlkj.junit.MinimalDatabaseTest","rest":"Started MinimalDatabaseTest in 28.627 seconds (JVM running for 29.813)"}
{"@timestamp":"2025-07-28T01:07:09.912Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"31540","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SnowflakeIdGenerator，init. workerId=0,datacenterId=0"}
{"@timestamp":"2025-07-28T01:07:09.912Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"31540","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SnowflakeIdGenerator，finish<<<<<<<<<<<<<"}
{"@timestamp":"2025-07-28T01:07:09.913Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"31540","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###ZookeeperInitRunner，init. HostAddress=**************:-1, applicationName=eeip-database-test"}
{"@timestamp":"2025-07-28T01:07:10.963Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"31540","thread":"main","class":"o.springframework.boot.SpringApplication","rest":"Application run failed"}
{"@timestamp":"2025-07-28T01:07:10.966Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"31540","thread":"main","class":"o.s.test.context.TestContextManager","rest":"Caught exception while allowing TestExecutionListener [org.springframework.test.context.web.ServletTestExecutionListener@488d1cd7] to prepare test instance [com.xcwlkj.junit.MinimalDatabaseTest@69176296]"}
{"@timestamp":"2025-07-28T01:15:49.063Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"13352","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$859ebebe] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-28T01:15:51.709Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"13352","thread":"main","class":"o.s.b.t.j.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer","rest":"\r\n\r\nFound multiple occurrences of org.json.JSONObject on the class path:\r\n\r\n\tjar:file:/D:/maven/repository/org/json/json/20170516/json-20170516.jar!/org/json/JSONObject.class\r\n\tjar:file:/D:/maven/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class\r\n\r\nYou may wish to exclude one of them to ensure predictable runtime behavior\r\n"}
{"@timestamp":"2025-07-28T01:15:51.722Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"13352","thread":"main","class":"com.xcwlkj.junit.MinimalDatabaseTest","rest":"The following profiles are active: database-test"}
{"@timestamp":"2025-07-28T01:16:00.969Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"13352","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化####"}
{"@timestamp":"2025-07-28T01:16:01.075Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"13352","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化成功####"}
{"@timestamp":"2025-07-28T01:16:02.447Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"13352","thread":"main","class":"c.x.i.taskcenter.DefaultHandleFactory","rest":"配置线程池工作线程数量[16]"}
{"@timestamp":"2025-07-28T01:16:02.662Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"13352","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"开始检查平台类型和初始化上级平台DFS客户端..."}
{"@timestamp":"2025-07-28T01:16:02.954Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"13352","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"当前平台类型为：HISOME，开始初始化上级平台DFS客户端"}
{"@timestamp":"2025-07-28T01:16:03.215Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"13352","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"上级平台DFS客户端初始化成功"}
{"@timestamp":"2025-07-28T01:16:03.216Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"13352","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"上级平台DFS客户端自动初始化完成"}
{"@timestamp":"2025-07-28T01:16:14.067Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"13352","thread":"main","class":"o.s.b.a.f.FreeMarkerAutoConfiguration","rest":"Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)"}
{"@timestamp":"2025-07-28T01:16:17.125Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"13352","thread":"main","class":"com.xcwlkj.junit.MinimalDatabaseTest","rest":"Started MinimalDatabaseTest in 29.607 seconds (JVM running for 30.894)"}
{"@timestamp":"2025-07-28T01:17:26.783Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"5936","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$6a877f9b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-28T01:17:27.239Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"5936","thread":"main","class":"o.s.b.t.j.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer","rest":"\r\n\r\nFound multiple occurrences of org.json.JSONObject on the class path:\r\n\r\n\tjar:file:/D:/maven/repository/org/json/json/20170516/json-20170516.jar!/org/json/JSONObject.class\r\n\tjar:file:/D:/maven/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class\r\n\r\nYou may wish to exclude one of them to ensure predictable runtime behavior\r\n"}
{"@timestamp":"2025-07-28T01:17:27.243Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"5936","thread":"main","class":"c.x.test.example.KsJkryJbxxServiceTest","rest":"The following profiles are active: database-test"}
{"@timestamp":"2025-07-28T01:17:34.068Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"5936","thread":"main","class":"o.s.boot.actuate.endpoint.EndpointId","rest":"Endpoint ID 'hystrix.stream' contains invalid characters, please migrate to a valid format."}
{"@timestamp":"2025-07-28T01:17:37.949Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"5936","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsServerUrl=http://************:8811"}
{"@timestamp":"2025-07-28T01:17:37.952Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"5936","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsChannel=ZJKSZHPT"}
{"@timestamp":"2025-07-28T01:17:37.952Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"5936","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsAppId=e046e7c7e7fbf1f1a4183e00c76e0182"}
{"@timestamp":"2025-07-28T01:17:37.952Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"5936","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsAppSecret=6d0dc6d02af7bab1f1ed39a7baadbb55"}
{"@timestamp":"2025-07-28T01:17:38.138Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"5936","thread":"main","class":"o.s.w.c.s.GenericWebApplicationContext","rest":"Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'deviceIdValidationAspect': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'com.xcwlkj.resourcecenter.service.BasicDeviceFeignApi': FactoryBean threw exception on object creation; nested exception is org.springframework.beans.factory.NoSuchBeanDefinitionException: No qualifying bean of type 'org.springframework.cloud.openfeign.FeignContext' available"}
{"@timestamp":"2025-07-28T01:17:38.622Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"5936","thread":"main","class":"o.s.b.d.LoggingFailureAnalysisReporter","rest":"\r\n\r\n***************************\r\nAPPLICATION FAILED TO START\r\n***************************\r\n\r\nDescription:\r\n\r\nA component required a bean of type 'org.springframework.cloud.openfeign.FeignContext' that could not be found.\r\n\r\nThe following candidates were found but could not be injected:\r\n\t- Bean method 'feignContext' in 'FeignAutoConfiguration' not loaded because auto-configuration 'FeignAutoConfiguration' was excluded\r\n\t- Bean method 'feignContext' in 'FeignAutoConfiguration' not loaded because auto-configuration 'FeignAutoConfiguration' was excluded\r\n\t- Bean method 'feignContext' in 'FeignAutoConfiguration' not loaded because auto-configuration 'FeignAutoConfiguration' was excluded\r\n\t- Bean method 'feignContext' in 'FeignAutoConfiguration' not loaded because auto-configuration 'FeignAutoConfiguration' was excluded\r\n\t- Bean method 'feignContext' in 'FeignAutoConfiguration' not loaded because auto-configuration 'FeignAutoConfiguration' was excluded\r\n\t- Bean method 'feignContext' in 'FeignAutoConfiguration' not loaded because auto-configuration 'FeignAutoConfiguration' was excluded\r\n\t- Bean method 'feignContext' in 'FeignAutoConfiguration' not loaded because auto-configuration 'FeignAutoConfiguration' was excluded\r\n\t- Bean method 'feignContext' in 'FeignAutoConfiguration' not loaded because auto-configuration 'FeignAutoConfiguration' was excluded\r\n\t- Bean method 'feignContext' in 'FeignAutoConfiguration' not loaded because auto-configuration 'FeignAutoConfiguration' was excluded\r\n\r\n\r\nAction:\r\n\r\nConsider revisiting the entries above or defining a bean of type 'org.springframework.cloud.openfeign.FeignContext' in your configuration.\r\n"}
{"@timestamp":"2025-07-28T01:17:38.626Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"5936","thread":"main","class":"o.s.test.context.TestContextManager","rest":"Caught exception while allowing TestExecutionListener [org.springframework.test.context.web.ServletTestExecutionListener@3c41ed1d] to prepare test instance [com.xcwlkj.test.example.KsJkryJbxxServiceTest@52c570f9]"}
{"@timestamp":"2025-07-28T01:20:05.043Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"1744","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$966e2f2] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-28T01:20:07.674Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"1744","thread":"main","class":"o.s.b.t.j.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer","rest":"\r\n\r\nFound multiple occurrences of org.json.JSONObject on the class path:\r\n\r\n\tjar:file:/D:/maven/repository/org/json/json/20170516/json-20170516.jar!/org/json/JSONObject.class\r\n\tjar:file:/D:/maven/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class\r\n\r\nYou may wish to exclude one of them to ensure predictable runtime behavior\r\n"}
{"@timestamp":"2025-07-28T01:20:07.682Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"1744","thread":"main","class":"com.xcwlkj.junit.MinimalDatabaseTest","rest":"The following profiles are active: database-test"}
{"@timestamp":"2025-07-28T01:20:16.670Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"1744","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化####"}
{"@timestamp":"2025-07-28T01:20:16.774Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"1744","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化成功####"}
{"@timestamp":"2025-07-28T01:20:18.225Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"1744","thread":"main","class":"c.x.i.taskcenter.DefaultHandleFactory","rest":"配置线程池工作线程数量[16]"}
{"@timestamp":"2025-07-28T01:20:18.417Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"1744","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"开始检查平台类型和初始化上级平台DFS客户端..."}
{"@timestamp":"2025-07-28T01:20:18.744Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"1744","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"当前平台类型为：HISOME，开始初始化上级平台DFS客户端"}
{"@timestamp":"2025-07-28T01:20:18.788Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"1744","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"上级平台DFS客户端初始化成功"}
{"@timestamp":"2025-07-28T01:20:18.790Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"1744","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"上级平台DFS客户端自动初始化完成"}
{"@timestamp":"2025-07-28T01:20:30.255Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"1744","thread":"main","class":"o.s.b.a.f.FreeMarkerAutoConfiguration","rest":"Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)"}
{"@timestamp":"2025-07-28T01:20:33.212Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"1744","thread":"main","class":"com.xcwlkj.junit.MinimalDatabaseTest","rest":"Started MinimalDatabaseTest in 29.742 seconds (JVM running for 30.944)"}
{"@timestamp":"2025-07-28T01:20:42.685Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"2272","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$966e2f2] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-28T01:20:45.294Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"2272","thread":"main","class":"o.s.b.t.j.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer","rest":"\r\n\r\nFound multiple occurrences of org.json.JSONObject on the class path:\r\n\r\n\tjar:file:/D:/maven/repository/org/json/json/20170516/json-20170516.jar!/org/json/JSONObject.class\r\n\tjar:file:/D:/maven/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class\r\n\r\nYou may wish to exclude one of them to ensure predictable runtime behavior\r\n"}
{"@timestamp":"2025-07-28T01:20:45.300Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"2272","thread":"main","class":"com.xcwlkj.junit.MinimalDatabaseTest","rest":"The following profiles are active: database-test"}
{"@timestamp":"2025-07-28T01:20:53.647Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"2272","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化####"}
{"@timestamp":"2025-07-28T01:20:53.743Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"2272","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化成功####"}
{"@timestamp":"2025-07-28T01:20:55.103Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"2272","thread":"main","class":"c.x.i.taskcenter.DefaultHandleFactory","rest":"配置线程池工作线程数量[16]"}
{"@timestamp":"2025-07-28T01:20:55.313Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"2272","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"开始检查平台类型和初始化上级平台DFS客户端..."}
{"@timestamp":"2025-07-28T01:20:55.610Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"2272","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"当前平台类型为：HISOME，开始初始化上级平台DFS客户端"}
{"@timestamp":"2025-07-28T01:20:55.654Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"2272","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"上级平台DFS客户端初始化成功"}
{"@timestamp":"2025-07-28T01:20:55.654Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"2272","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"上级平台DFS客户端自动初始化完成"}
{"@timestamp":"2025-07-28T01:21:06.537Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"2272","thread":"main","class":"o.s.b.a.f.FreeMarkerAutoConfiguration","rest":"Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)"}
{"@timestamp":"2025-07-28T01:21:09.537Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"2272","thread":"main","class":"com.xcwlkj.junit.MinimalDatabaseTest","rest":"Started MinimalDatabaseTest in 28.366 seconds (JVM running for 29.556)"}
{"@timestamp":"2025-07-28T01:21:38.978Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"13088","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$a239605] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-28T01:21:41.745Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"13088","thread":"main","class":"o.s.b.t.j.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer","rest":"\r\n\r\nFound multiple occurrences of org.json.JSONObject on the class path:\r\n\r\n\tjar:file:/D:/maven/repository/org/json/json/20170516/json-20170516.jar!/org/json/JSONObject.class\r\n\tjar:file:/D:/maven/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class\r\n\r\nYou may wish to exclude one of them to ensure predictable runtime behavior\r\n"}
{"@timestamp":"2025-07-28T01:21:41.756Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"13088","thread":"main","class":"com.xcwlkj.junit.MinimalDatabaseTest","rest":"The following profiles are active: database-test"}
{"@timestamp":"2025-07-28T01:21:53.108Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"13088","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化####"}
{"@timestamp":"2025-07-28T01:21:53.269Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"13088","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化成功####"}
{"@timestamp":"2025-07-28T01:21:54.769Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"13088","thread":"main","class":"c.x.i.taskcenter.DefaultHandleFactory","rest":"配置线程池工作线程数量[16]"}
{"@timestamp":"2025-07-28T01:21:55.269Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"13088","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"开始检查平台类型和初始化上级平台DFS客户端..."}
{"@timestamp":"2025-07-28T01:21:55.856Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"13088","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"当前平台类型为：HISOME，开始初始化上级平台DFS客户端"}
{"@timestamp":"2025-07-28T01:21:55.901Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"13088","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"上级平台DFS客户端初始化成功"}
{"@timestamp":"2025-07-28T01:21:55.901Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"13088","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"上级平台DFS客户端自动初始化完成"}
{"@timestamp":"2025-07-28T01:22:09.310Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"13088","thread":"main","class":"o.s.b.a.f.FreeMarkerAutoConfiguration","rest":"Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)"}
{"@timestamp":"2025-07-28T01:22:13.333Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"13088","thread":"main","class":"com.xcwlkj.junit.MinimalDatabaseTest","rest":"Started MinimalDatabaseTest in 36.367 seconds (JVM running for 38.049)"}
{"@timestamp":"2025-07-28T03:37:59.803Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15252","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$10501d42] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-28T03:38:02.471Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15252","thread":"main","class":"o.s.b.t.j.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer","rest":"\r\n\r\nFound multiple occurrences of org.json.JSONObject on the class path:\r\n\r\n\tjar:file:/D:/maven/repository/org/json/json/20170516/json-20170516.jar!/org/json/JSONObject.class\r\n\tjar:file:/D:/maven/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class\r\n\r\nYou may wish to exclude one of them to ensure predictable runtime behavior\r\n"}
{"@timestamp":"2025-07-28T03:38:02.479Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15252","thread":"main","class":"com.xcwlkj.junit.MinimalDatabaseTest","rest":"The following profiles are active: database-test"}
{"@timestamp":"2025-07-28T03:38:11.118Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15252","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化####"}
{"@timestamp":"2025-07-28T03:38:11.219Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15252","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化成功####"}
{"@timestamp":"2025-07-28T03:38:12.518Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15252","thread":"main","class":"c.x.i.taskcenter.DefaultHandleFactory","rest":"配置线程池工作线程数量[16]"}
{"@timestamp":"2025-07-28T03:38:12.709Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15252","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"开始检查平台类型和初始化上级平台DFS客户端..."}
{"@timestamp":"2025-07-28T03:38:13.012Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15252","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"当前平台类型为：HISOME，开始初始化上级平台DFS客户端"}
{"@timestamp":"2025-07-28T03:38:13.054Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15252","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"上级平台DFS客户端初始化成功"}
{"@timestamp":"2025-07-28T03:38:13.054Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15252","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"上级平台DFS客户端自动初始化完成"}
{"@timestamp":"2025-07-28T03:38:24.123Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15252","thread":"main","class":"o.s.b.a.f.FreeMarkerAutoConfiguration","rest":"Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)"}
{"@timestamp":"2025-07-28T03:38:27.675Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15252","thread":"main","class":"com.xcwlkj.junit.MinimalDatabaseTest","rest":"Started MinimalDatabaseTest in 29.461 seconds (JVM running for 30.683)"}
{"@timestamp":"2025-07-28T03:46:02.452Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"31124","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$b157a467] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-28T03:46:05.070Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"31124","thread":"main","class":"o.s.b.t.j.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer","rest":"\r\n\r\nFound multiple occurrences of org.json.JSONObject on the class path:\r\n\r\n\tjar:file:/D:/maven/repository/org/json/json/20170516/json-20170516.jar!/org/json/JSONObject.class\r\n\tjar:file:/D:/maven/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class\r\n\r\nYou may wish to exclude one of them to ensure predictable runtime behavior\r\n"}
{"@timestamp":"2025-07-28T03:46:05.077Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"31124","thread":"main","class":"c.x.test.example.KsJkryJbxxServiceTest","rest":"The following profiles are active: database-test"}
{"@timestamp":"2025-07-28T03:46:13.844Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"31124","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化####"}
{"@timestamp":"2025-07-28T03:46:13.944Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"31124","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化成功####"}
{"@timestamp":"2025-07-28T03:46:15.299Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"31124","thread":"main","class":"c.x.i.taskcenter.DefaultHandleFactory","rest":"配置线程池工作线程数量[16]"}
{"@timestamp":"2025-07-28T03:46:15.499Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"31124","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"开始检查平台类型和初始化上级平台DFS客户端..."}
{"@timestamp":"2025-07-28T03:46:15.821Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"31124","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"当前平台类型为：HISOME，开始初始化上级平台DFS客户端"}
{"@timestamp":"2025-07-28T03:46:15.867Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"31124","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"上级平台DFS客户端初始化成功"}
{"@timestamp":"2025-07-28T03:46:15.868Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"31124","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"上级平台DFS客户端自动初始化完成"}
{"@timestamp":"2025-07-28T03:46:28.700Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"31124","thread":"main","class":"o.s.b.a.f.FreeMarkerAutoConfiguration","rest":"Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)"}
{"@timestamp":"2025-07-28T03:46:32.090Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"31124","thread":"main","class":"c.x.test.example.KsJkryJbxxServiceTest","rest":"Started KsJkryJbxxServiceTest in 31.165 seconds (JVM running for 32.403)"}
{"@timestamp":"2025-07-28T03:46:32.235Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"31124","thread":"main","class":"c.x.exceptions.IdentityVerifyException","rest":"<== KsywBusiException, message:考试计划不存在"}
{"@timestamp":"2025-07-28T03:48:19.678Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"14616","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$b157a467] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-28T03:48:22.290Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"14616","thread":"main","class":"o.s.b.t.j.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer","rest":"\r\n\r\nFound multiple occurrences of org.json.JSONObject on the class path:\r\n\r\n\tjar:file:/D:/maven/repository/org/json/json/20170516/json-20170516.jar!/org/json/JSONObject.class\r\n\tjar:file:/D:/maven/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class\r\n\r\nYou may wish to exclude one of them to ensure predictable runtime behavior\r\n"}
{"@timestamp":"2025-07-28T03:48:22.297Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"14616","thread":"main","class":"c.x.test.example.KsJkryJbxxServiceTest","rest":"The following profiles are active: database-test"}
{"@timestamp":"2025-07-28T03:48:30.684Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"14616","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化####"}
{"@timestamp":"2025-07-28T03:48:30.781Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"14616","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化成功####"}
{"@timestamp":"2025-07-28T03:48:32.112Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"14616","thread":"main","class":"c.x.i.taskcenter.DefaultHandleFactory","rest":"配置线程池工作线程数量[16]"}
{"@timestamp":"2025-07-28T03:48:32.316Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"14616","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"开始检查平台类型和初始化上级平台DFS客户端..."}
{"@timestamp":"2025-07-28T03:48:32.666Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"14616","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"当前平台类型为：HISOME，开始初始化上级平台DFS客户端"}
{"@timestamp":"2025-07-28T03:48:32.712Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"14616","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"上级平台DFS客户端初始化成功"}
{"@timestamp":"2025-07-28T03:48:32.712Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"14616","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"上级平台DFS客户端自动初始化完成"}
{"@timestamp":"2025-07-28T03:48:43.663Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"14616","thread":"main","class":"o.s.b.a.f.FreeMarkerAutoConfiguration","rest":"Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)"}
{"@timestamp":"2025-07-28T03:48:46.730Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"14616","thread":"main","class":"c.x.test.example.KsJkryJbxxServiceTest","rest":"Started KsJkryJbxxServiceTest in 28.596 seconds (JVM running for 29.816)"}
