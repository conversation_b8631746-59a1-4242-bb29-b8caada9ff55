package com.xcwlkj.basicinfo.aop;

import com.alibaba.fastjson.JSONObject;
import com.xcwlkj.biz.core.event.CommonEvent;
import com.xcwlkj.resourcecenter.model.domain.SbCommandYwRelation;
import com.xcwlkj.resourcecenter.service.SbCommandLogService;
import com.xcwlkj.resourcecenter.service.SbCommandYwRelationService;
import com.xcwlkj.util.IdGenerateUtil;
import com.xcwlkj.util.ReflectUtil;
import com.xcwlkj.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Aspect
@Component("deviceCommandAspect")
@Slf4j
public class DeviceCommandAspect {

    @Resource
    private SbCommandLogService sbCommandLogService;
    @Resource
    private SbCommandYwRelationService sbCommandYwRelationService;


    @Around("execution(* com.xcwlkj.biz.core.event.DefaultEventSender.publish(..))")
    public Object aroundCommand(ProceedingJoinPoint joinPoint){
        // 获取方法参数
        Object[] args = joinPoint.getArgs();
        CommonEvent arg = (CommonEvent) args[0];
        if (StringUtil.isBlank(arg.getMsgId())){
            arg.setMsgId(IdGenerateUtil.generateId());
        }

        List<String> devices = (List<String>) ReflectUtil.getFieldValue(arg.getMessage(), "devices");

        if (CollectionUtils.isEmpty(devices)){
            log.info("没有需要处理的设备");
            return null;
        }


        String ywlx = null;
        try {
            ywlx = (String) ReflectUtil.getFieldValue(arg.getMessage(), "ywlx");
            if (ywlx != null) {
                List<String> ywIds = (List<String>) ReflectUtil.getFieldValue(arg.getMessage(), "ywIds");
                List<SbCommandYwRelation> sbCommandYwRelations = new ArrayList<>();
                for (String ywId : ywIds) {
                    SbCommandYwRelation sbCommandYwRelation = new SbCommandYwRelation();
                    sbCommandYwRelation.setYwbh(ywId);
                    sbCommandYwRelation.setYwlx(ywlx);
                    sbCommandYwRelation.setMsgId(arg.getMsgId());
                    sbCommandYwRelations.add(sbCommandYwRelation);
                }
                sbCommandYwRelationService.batchInsertOrUpdate(sbCommandYwRelations);
            }
        }catch (Exception e){
            log.error("命令下发与业务关联信息插入失败", e);
        }

        String message = JSONObject.toJSONString(ReflectUtil.getFieldValue(arg.getMessage(), "body"));
        // 入库
        sbCommandLogService.batchInit(devices, arg.getMsgId(), arg.getTopic(), ywlx, StringUtil.substring(message, 0, 1999));

        // 调用原方法
        Object result = null;
        try {
            result = joinPoint.proceed();
        } catch (Throwable throwable) {
            throwable.printStackTrace();
        }

        // 查询结果
        if (devices.size() == 1){
            sbCommandLogService.batchUpdate(devices, arg.getMsgId(), arg.getTopic(), 8);
        }else {
            new Thread(() -> {
                sbCommandLogService.batchUpdate(devices, arg.getMsgId(), arg.getTopic(), devices.size() * 4);
            }).start();
        }

        return result;
    }
}
