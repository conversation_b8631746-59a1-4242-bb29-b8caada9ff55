[2m2025-07-28 09:25:40.197[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$daf13b78] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2m2025-07-28 09:25:40.730[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.c.c.ConfigServicePropertySourceLocator[0;39m [2m:[0;39m Fetching config from server at : http://localhost:8888
[2m2025-07-28 09:25:42.865[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.c.c.ConfigServicePropertySourceLocator[0;39m [2m:[0;39m Connect Timeout Exception on Url - http://localhost:8888. Will be trying the next url if available
[2m2025-07-28 09:25:42.866[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.c.c.ConfigServicePropertySourceLocator[0;39m [2m:[0;39m Could not locate PropertySource: I/O error on GET request for "http://localhost:8888/eeip-standalone-service/default": Connection refused: connect; nested exception is java.net.ConnectException: Connection refused: connect
[2m2025-07-28 09:25:42.869[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.standalone.StandaloneApplication    [0;39m [2m:[0;39m The following profiles are active: alone
[2m2025-07-28 09:25:48.853[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Multiple Spring Data modules found, entering strict repository configuration mode!
[2m2025-07-28 09:25:48.857[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[2m2025-07-28 09:25:49.229[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 340ms. Found 0 Redis repository interfaces.
[2m2025-07-28 09:25:49.480[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.boot.actuate.endpoint.EndpointId    [0;39m [2m:[0;39m Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
[2m2025-07-28 09:25:49.761[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.boot.actuate.endpoint.EndpointId    [0;39m [2m:[0;39m Endpoint ID 'hystrix.stream' contains invalid characters, please migrate to a valid format.
[2m2025-07-28 09:25:50.384[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.cloud.context.scope.GenericScope    [0;39m [2m:[0;39m BeanFactory id=5b395cf1-ddbf-331b-876f-aa2c7020b748
[2m2025-07-28 09:25:50.416[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mfaultConfiguringBeanFactoryPostProcessor[0;39m [2m:[0;39m No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
[2m2025-07-28 09:25:50.426[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mfaultConfiguringBeanFactoryPostProcessor[0;39m [2m:[0;39m No bean named 'taskScheduler' has been explicitly defined. Therefore, a default ThreadPoolTaskScheduler will be created.
[2m2025-07-28 09:25:50.446[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mfaultConfiguringBeanFactoryPostProcessor[0;39m [2m:[0;39m No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
[2m2025-07-28 09:25:50.592[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'configProperties' of type [com.xcwlkj.core.config.properties.ConfigProperties$$EnhancerBySpringCGLIB$$1bcf1811] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2m2025-07-28 09:25:50.592[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'asyncTaskExecutorConfiguration' of type [com.xcwlkj.core.config.AsyncTaskExecutorConfiguration$$EnhancerBySpringCGLIB$$d5015d41] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2m2025-07-28 09:25:50.699[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$b49ad0a0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2m2025-07-28 09:25:51.074[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$bed7387b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2m2025-07-28 09:25:51.139[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'redisConfig' of type [com.xcwlkj.standalone.config.RedisConfig$$EnhancerBySpringCGLIB$$9a7a15f8] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2m2025-07-28 09:25:51.188[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'appConfig' of type [com.xcwlkj.standalone.config.AppConfig$$EnhancerBySpringCGLIB$$d69b479e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2m2025-07-28 09:25:51.224[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'integrationDisposableAutoCreatedBeans' of type [org.springframework.integration.config.annotation.Disposables] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2m2025-07-28 09:25:51.287[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'com.xcwlkj.biz.core.feignclient.FeignClientConfig' of type [com.xcwlkj.biz.core.feignclient.FeignClientConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2m2025-07-28 09:25:51.297[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'org.springframework.integration.config.IntegrationManagementConfiguration' of type [org.springframework.integration.config.IntegrationManagementConfiguration$$EnhancerBySpringCGLIB$$29c8f3aa] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2m2025-07-28 09:25:51.346[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$daf13b78] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2m2025-07-28 09:25:52.109[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat initialized with port(s): 8888 (http)
[2m2025-07-28 09:25:52.309[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.web.context.ContextLoader           [0;39m [2m:[0;39m Root WebApplicationContext: initialization completed in 9416 ms
[2m2025-07-28 09:25:56.580[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.a.e.web.ServletEndpointRegistrar  [0;39m [2m:[0;39m Registered '/actuator/hystrix.stream' to hystrix.stream-actuator-endpoint
[2m2025-07-28 09:25:56.718[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.s.concurrent.ThreadPoolTaskExecutor [0;39m [2m:[0;39m Initializing ExecutorService
[2m2025-07-28 09:25:56.846[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.s.concurrent.ThreadPoolTaskExecutor [0;39m [2m:[0;39m Initializing ExecutorService
[2m2025-07-28 09:25:56.969[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.s.concurrent.ThreadPoolTaskExecutor [0;39m [2m:[0;39m Initializing ExecutorService
[2m2025-07-28 09:25:56.977[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.xcwlkj.standalone.config.XcDfsConfig  [0;39m [2m:[0;39m ###dfsServerUrl=http://************:8811
[2m2025-07-28 09:25:56.977[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.xcwlkj.standalone.config.XcDfsConfig  [0;39m [2m:[0;39m ###dfsChannel=ZJKSZHPT
[2m2025-07-28 09:25:56.977[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.xcwlkj.standalone.config.XcDfsConfig  [0;39m [2m:[0;39m ###dfsAppId=e046e7c7e7fbf1f1a4183e00c76e0182
[2m2025-07-28 09:25:56.977[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.xcwlkj.standalone.config.XcDfsConfig  [0;39m [2m:[0;39m ###dfsAppSecret=6d0dc6d02af7bab1f1ed39a7baadbb55
[2m2025-07-28 09:25:58.234[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.interceptor.SqlLogInterceptor  [0;39m [2m:[0;39m [打印SQL拦截器创建]noticeTime=5.0秒
[2m2025-07-28 09:26:01.055[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.c.c.AsyncTaskExecutorConfiguration  [0;39m [2m:[0;39m Creating Async Task Executor
[2m2025-07-28 09:26:01.056[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.s.concurrent.ThreadPoolTaskExecutor [0;39m [2m:[0;39m Initializing ExecutorService
[2m2025-07-28 09:26:02.675[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.xcwlkj.pubc.config.SmsConfig        [0;39m [2m:[0;39m SMS Bean IAcsClient Start
[2m2025-07-28 09:26:02.690[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.xcwlkj.pubc.config.SmsConfig        [0;39m [2m:[0;39m 加载SMS Bean IAcsClient OK
[2m2025-07-28 09:26:11.869[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.identityverify.config.XcDfsConfig   [0;39m [2m:[0;39m ####文件系统初始化####
[2m2025-07-28 09:26:11.877[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.identityverify.config.XcDfsConfig   [0;39m [2m:[0;39m ####文件系统初始化成功####
[2m2025-07-28 09:26:14.015[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.taskcenter.DefaultHandleFactory   [0;39m [2m:[0;39m 配置线程池工作线程数量[16]
[2m2025-07-28 09:26:14.159[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.handler.UpperHsDfsHandler         [0;39m [2m:[0;39m 开始检查平台类型和初始化上级平台DFS客户端...
[2m2025-07-28 09:26:14.511[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.J.selectOneByExample            [0;39m [2m:[0;39m ==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) 
[2m2025-07-28 09:26:14.529[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.J.selectOneByExample            [0;39m [2m:[0;39m ==> Parameters: defaultPlat(String)
[2m2025-07-28 09:26:14.552[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.J.selectOneByExample            [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 09:26:14.559[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.handler.UpperHsDfsHandler         [0;39m [2m:[0;39m 当前平台类型为：HISOME，开始初始化上级平台DFS客户端
[2m2025-07-28 09:26:14.559[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.J.selectOneByExample            [0;39m [2m:[0;39m ==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) 
[2m2025-07-28 09:26:14.559[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.J.selectOneByExample            [0;39m [2m:[0;39m ==> Parameters: HISOME_fileServerUrl(String)
[2m2025-07-28 09:26:14.569[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.J.selectOneByExample            [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 09:26:14.571[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.J.selectOneByExample            [0;39m [2m:[0;39m ==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) 
[2m2025-07-28 09:26:14.571[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.J.selectOneByExample            [0;39m [2m:[0;39m ==> Parameters: HISOME_fileServerChannel(String)
[2m2025-07-28 09:26:14.578[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.J.selectOneByExample            [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 09:26:14.582[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.J.selectOneByExample            [0;39m [2m:[0;39m ==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) 
[2m2025-07-28 09:26:14.582[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.J.selectOneByExample            [0;39m [2m:[0;39m ==> Parameters: HISOME_fileServiceAppId(String)
[2m2025-07-28 09:26:14.588[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.J.selectOneByExample            [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 09:26:14.588[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.J.selectOneByExample            [0;39m [2m:[0;39m ==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) 
[2m2025-07-28 09:26:14.592[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.J.selectOneByExample            [0;39m [2m:[0;39m ==> Parameters: HISOME_fileServiceAppSecret(String)
[2m2025-07-28 09:26:14.599[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.J.selectOneByExample            [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 09:26:14.608[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.handler.UpperHsDfsHandler         [0;39m [2m:[0;39m 上级平台DFS客户端初始化成功
[2m2025-07-28 09:26:14.608[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.handler.UpperHsDfsHandler         [0;39m [2m:[0;39m 上级平台DFS客户端自动初始化完成
[2m2025-07-28 09:26:16.771[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.s.impl.ScheduleJobServiceImpl     [0;39m [2m:[0;39m ###定时任务初始化启动中....
[2m2025-07-28 09:26:16.771[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.ScheduleJobMapper.selectAll     [0;39m [2m:[0;39m ==>  Preparing: SELECT job_id,job_name,bean_name,params,cron_expression,status,remark,create_time,update_time FROM schedule_job 
[2m2025-07-28 09:26:16.771[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.ScheduleJobMapper.selectAll     [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-28 09:26:16.781[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.ScheduleJobMapper.selectAll     [0;39m [2m:[0;39m <==      Total: 10
[2m2025-07-28 09:26:16.802[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.s.impl.ScheduleJobServiceImpl     [0;39m [2m:[0;39m ###定时任务初始化启动[teskTask-jobTest111]
[2m2025-07-28 09:26:16.802[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.s.impl.ScheduleJobServiceImpl     [0;39m [2m:[0;39m ###定时任务初始化启动[sbzxztCxAndGxTaskService-jobSbzxztGx]
[2m2025-07-28 09:26:16.804[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.s.impl.ScheduleJobServiceImpl     [0;39m [2m:[0;39m ###定时任务初始化启动[onlineStatusTaskService-jobOnlineStatus]
[2m2025-07-28 09:26:16.804[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.s.impl.ScheduleJobServiceImpl     [0;39m [2m:[0;39m ###定时任务初始化启动[subscriberDevInfoToMqttTaskService-jobSubscribeMqtt]
[2m2025-07-28 09:26:16.804[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.s.impl.ScheduleJobServiceImpl     [0;39m [2m:[0;39m ###定时任务初始化启动[sjxfsbCxxfTaskService-sjxfsbCxxf]
[2m2025-07-28 09:26:16.804[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.s.impl.ScheduleJobServiceImpl     [0;39m [2m:[0;39m ###定时任务初始化启动[sbxxReportTaskService-设备信息上报]
[2m2025-07-28 09:26:16.804[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.s.impl.ScheduleJobServiceImpl     [0;39m [2m:[0;39m ###定时任务初始化启动[ksjhSjdsxfTaskService-ksjhSjdsxf]
[2m2025-07-28 09:26:16.804[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.s.impl.ScheduleJobServiceImpl     [0;39m [2m:[0;39m ###定时任务初始化启动[deviceVersionUpdateTaskService-设备固件版本更新]
[2m2025-07-28 09:26:16.804[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.s.impl.ScheduleJobServiceImpl     [0;39m [2m:[0;39m ###定时任务初始化启动[deviceEventDeleteTaskService-设备事件删除]
[2m2025-07-28 09:26:16.804[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.s.impl.ScheduleJobServiceImpl     [0;39m [2m:[0;39m ###定时任务初始化启动[kssjDsscsjptTaskService-考试数据定时上传上级平台]
[2m2025-07-28 09:26:16.809[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.s.impl.ScheduleJobServiceImpl     [0;39m [2m:[0;39m ###定时任务初始化启动完成
[2m2025-07-28 09:26:19.084[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.s.c.ThreadPoolTaskScheduler         [0;39m [2m:[0;39m Initializing ExecutorService 'taskScheduler'
[2m2025-07-28 09:26:19.541[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36m.FeignHystrixConcurrencyStrategyIntellif[0;39m [2m:[0;39m Current Hystrix plugins configuration is [concurrencyStrategy [com.netflix.hystrix.strategy.concurrency.HystrixConcurrencyStrategyDefault@52355243],eventNotifier [com.netflix.hystrix.strategy.eventnotifier.HystrixEventNotifierDefault@2544f6f1],metricPublisher [com.netflix.hystrix.strategy.metrics.HystrixMetricsPublisherDefault@12710aa4],propertiesStrategy [com.netflix.hystrix.strategy.properties.HystrixPropertiesStrategyDefault@52991723],]
[2m2025-07-28 09:26:19.541[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36m.FeignHystrixConcurrencyStrategyIntellif[0;39m [2m:[0;39m Registering Sleuth Hystrix Concurrency Strategy.
[2m2025-07-28 09:26:24.488[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.xcwlkj.standalone.config.MqttConfig [0;39m [2m:[0;39m ######监听MQTT主题[/CENCGW100_S/CENCGW100_SIV1c9f717de4acfc99/function/invoke]
[2m2025-07-28 09:26:24.490[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.xcwlkj.standalone.config.MqttConfig [0;39m [2m:[0;39m ######监听MQTT主题[/CENCGW100_S/CENCGW100_SIV1c9f717de4acfc99/token/get/reply]
[2m2025-07-28 09:26:24.490[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.xcwlkj.standalone.config.MqttConfig [0;39m [2m:[0;39m ######监听MQTT主题[/+/+/event/+]
[2m2025-07-28 09:26:24.521[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.xcwlkj.standalone.config.XxlJobConfig [0;39m [2m:[0;39m >>>>>>>>>>> xxl-job config init.
[2m2025-07-28 09:26:25.715[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.a.f.FreeMarkerAutoConfiguration   [0;39m [2m:[0;39m Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
[2m2025-07-28 09:26:27.039[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.a.e.web.EndpointLinksResolver     [0;39m [2m:[0;39m Exposing 21 endpoint(s) beneath base path '/actuator'
[2m2025-07-28 09:26:28.510[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.i.endpoint.EventDrivenConsumer      [0;39m [2m:[0;39m Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
[2m2025-07-28 09:26:28.511[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.i.channel.PublishSubscribeChannel   [0;39m [2m:[0;39m Channel 'application-1.errorChannel' has 1 subscriber(s).
[2m2025-07-28 09:26:28.511[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.i.endpoint.EventDrivenConsumer      [0;39m [2m:[0;39m started _org.springframework.integration.errorLogger
[2m2025-07-28 09:26:28.511[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.i.endpoint.EventDrivenConsumer      [0;39m [2m:[0;39m Adding {message-handler:mqttConfig.handler.serviceActivator} as a subscriber to the 'mqttInputChannel' channel
[2m2025-07-28 09:26:28.511[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.integration.channel.ExecutorChannel [0;39m [2m:[0;39m Channel 'application-1.mqttInputChannel' has 1 subscriber(s).
[2m2025-07-28 09:26:28.511[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.i.endpoint.EventDrivenConsumer      [0;39m [2m:[0;39m started mqttConfig.handler.serviceActivator
[2m2025-07-28 09:26:28.511[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.i.endpoint.EventDrivenConsumer      [0;39m [2m:[0;39m Adding {message-handler:mqttConfig.mqttOutbound.serviceActivator} as a subscriber to the 'mqttOutboundChannel' channel
[2m2025-07-28 09:26:28.511[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.integration.channel.ExecutorChannel [0;39m [2m:[0;39m Channel 'application-1.mqttOutboundChannel' has 1 subscriber(s).
[2m2025-07-28 09:26:28.512[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.i.endpoint.EventDrivenConsumer      [0;39m [2m:[0;39m started mqttConfig.mqttOutbound.serviceActivator
[2m2025-07-28 09:26:28.512[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mProxyFactoryBean$MethodInvocationGateway[0;39m [2m:[0;39m started mqttGateway
[2m2025-07-28 09:26:28.512[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.i.gateway.GatewayProxyFactoryBean   [0;39m [2m:[0;39m started mqttGateway
[2m2025-07-28 09:26:28.544[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.c.n.eureka.InstanceInfoFactory      [0;39m [2m:[0;39m Setting initial instance status as: STARTING
[2m2025-07-28 09:26:31.411[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.c.n.e.s.EurekaServiceRegistry       [0;39m [2m:[0;39m Registering application EEIP-STANDALONE-SERVICE with eureka with status UP
[2m2025-07-28 09:26:31.800[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36m.m.i.MqttPahoMessageDrivenChannelAdapter[0;39m [2m:[0;39m started inbound
[2m2025-07-28 09:26:31.800[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.s.quartz.SchedulerFactoryBean       [0;39m [2m:[0;39m Starting Quartz Scheduler now
[2m2025-07-28 09:26:31.903[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat started on port(s): 8888 (http) with context path ''
[2m2025-07-28 09:26:31.903[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.c.n.e.s.EurekaAutoServiceRegistration[0;39m [2m:[0;39m Updating port to 8888
[2m2025-07-28 09:26:32.187[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.standalone.StandaloneApplication    [0;39m [2m:[0;39m Started StandaloneApplication in 53.635 seconds (JVM running for 54.403)
[2m2025-07-28 09:26:32.216[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.xcwlkj.util.YmlUtil                 [0;39m [2m:[0;39m 开始从application-alone.yml加载配置信息
[2m2025-07-28 09:26:32.217[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.xcwlkj.util.YmlUtil                 [0;39m [2m:[0;39m 读取外部文件失败D:\code\HS_CUEEIP\trunk\provider/config/application-alone.yml
[2m2025-07-28 09:26:32.221[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.xcwlkj.util.YmlUtil                 [0;39m [2m:[0;39m 开始从application-alone.yml加载配置信息【成功】
[2m2025-07-28 09:26:32.266[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.xcwlkj.cache.AbstractRedisDataCache [0;39m [2m:[0;39m [PkgDataFileServices]cleanup cache finished
[2m2025-07-28 09:26:32.270[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.K.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE ks_kssj_pkg_task SET complete = ? WHERE ( ( complete in ( ? , ? ) ) ) 
[2m2025-07-28 09:26:32.270[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.K.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: -1(Integer), 0(Integer), 1(Integer)
[2m2025-07-28 09:26:32.300[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.K.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-28 09:26:32.301[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.K.clearDoing                    [0;39m [2m:[0;39m ==>  Preparing: UPDATE ks_kssj_pkg_status SET kssjb_qk = case when kssjb_qk = 1 then -10 else kssjb_qk end, jkryjbsjb_qk = case when jkryjbsjb_qk = 1 then -10 else jkryjbsjb_qk end, jkrybpsjb_qk = case when jkrybpsjb_qk = 1 then -10 else jkrybpsjb_qk end, pzsjb_qk = case when pzsjb_qk = 1 then -10 else pzsjb_qk end, kszpsjb_qk = case when kszpsjb_qk = 1 then -10 else kszpsjb_qk end, jkryzpsjb_qk = case when jkryzpsjb_qk = 1 then -10 else jkryzpsjb_qk end 
[2m2025-07-28 09:26:32.302[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.K.clearDoing                    [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-28 09:26:32.315[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.K.clearDoing                    [0;39m [2m:[0;39m <==    Updates: 295
[2m2025-07-28 09:26:32.318[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.config.ZookeeperInitRunner     [0;39m [2m:[0;39m ###SnowflakeIdGenerator，init. workerId=0,datacenterId=0
[2m2025-07-28 09:26:32.319[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.config.ZookeeperInitRunner     [0;39m [2m:[0;39m ###SnowflakeIdGenerator，finish<<<<<<<<<<<<<
[2m2025-07-28 09:26:32.319[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.config.ZookeeperInitRunner     [0;39m [2m:[0;39m ###ZookeeperInitRunner，init. HostAddress=**************:8888, applicationName=eeip-standalone-service
[2m2025-07-28 09:26:32.329[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.c.z.r.z.ZookeeperRegistryCenter     [0;39m [2m:[0;39m Elastic job: zookeeper registry center init, server lists is: **************:2181.
[2m2025-07-28 09:26:32.607[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.config.ZookeeperInitRunner     [0;39m [2m:[0;39m ###ZookeeperInitRunner，finish<<<<<<<<<<<<<
[2m2025-07-28 09:26:32.612[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.config.ZookeeperInitRunner     [0;39m [2m:[0;39m ###SystemCache Init##
[2m2025-07-28 09:26:32.612[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.b.c.BasicinfoBusiCacheInitConfig    [0;39m [2m:[0;39m ####开始缓存商户配置信息####
[2m2025-07-28 09:26:32.612[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.b.c.BasicinfoBusiCacheInitConfig    [0;39m [2m:[0;39m ####缓存商户配置信息缓存成功####
[2m2025-07-28 09:26:32.612[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.config.ZookeeperInitRunner     [0;39m [2m:[0;39m ###SystemCache Succ<<<<<<<<<<<<<##
[2m2025-07-28 09:26:32.612[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.config.ZookeeperInitRunner     [0;39m [2m:[0;39m ###SystemCache Init##
[2m2025-07-28 09:26:32.612[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.xcwlkj.biz.config.BizInitConfig     [0;39m [2m:[0;39m ####开始缓存配置信息####
[2m2025-07-28 09:26:32.612[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.xcwlkj.biz.config.BizInitConfig     [0;39m [2m:[0;39m ####缓存配置信息缓存成功####
[2m2025-07-28 09:26:32.612[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.xcwlkj.biz.config.BizInitConfig     [0;39m [2m:[0;39m ####消息队列初始化####
[2m2025-07-28 09:26:32.612[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.xcwlkj.biz.config.BizInitConfig     [0;39m [2m:[0;39m ####消息队列初始化成功####
[2m2025-07-28 09:26:32.612[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.config.ZookeeperInitRunner     [0;39m [2m:[0;39m ###SystemCache Succ<<<<<<<<<<<<<##
[2m2025-07-28 09:26:32.613[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.config.ZookeeperInitRunner     [0;39m [2m:[0;39m ###SystemCache Init##
[2m2025-07-28 09:26:32.613[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.pubc.config.PubcBusiCacheInitConfig [0;39m [2m:[0;39m ####开始缓存公共服务配置信息####
[2m2025-07-28 09:26:32.613[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.pubc.config.PubcBusiCacheInitConfig [0;39m [2m:[0;39m ####缓存公共服务配置信息缓存成功####
[2m2025-07-28 09:26:32.614[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.config.ZookeeperInitRunner     [0;39m [2m:[0;39m ###SystemCache Succ<<<<<<<<<<<<<##
[2m2025-07-28 09:26:32.614[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.config.ZookeeperInitRunner     [0;39m [2m:[0;39m ###SystemCache Init##
[2m2025-07-28 09:26:32.614[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.e.c.EvaluationBusiCacheInitConfig   [0;39m [2m:[0;39m ####开始缓存听评课配置信息####
[2m2025-07-28 09:26:32.614[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.e.c.EvaluationBusiCacheInitConfig   [0;39m [2m:[0;39m ####缓存听评课配置信息缓存成功####
[2m2025-07-28 09:26:32.614[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.e.c.EvaluationBusiCacheInitConfig   [0;39m [2m:[0;39m ####消息队列初始化####
[2m2025-07-28 09:26:32.614[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.e.c.EvaluationBusiCacheInitConfig   [0;39m [2m:[0;39m ####消息队列初始化成功####
[2m2025-07-28 09:26:32.614[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.config.ZookeeperInitRunner     [0;39m [2m:[0;39m ###SystemCache Succ<<<<<<<<<<<<<##
[2m2025-07-28 09:26:32.614[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.config.ZookeeperInitRunner     [0;39m [2m:[0;39m ###SystemCache Init##
[2m2025-07-28 09:26:32.628[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36m.x.i.c.IdentityverifyBusiCacheInitConfig[0;39m [2m:[0;39m ####开始缓存校级身份核验平台配置信息####
[2m2025-07-28 09:26:33.423[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36m.x.i.c.IdentityverifyBusiCacheInitConfig[0;39m [2m:[0;39m ####服务器序列号为CENCGW100_SIV1c9f717de4acfc99
[2m2025-07-28 09:26:33.423[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36m.x.i.c.IdentityverifyBusiCacheInitConfig[0;39m [2m:[0;39m ####码表数据缓存初始化####
[2m2025-07-28 09:26:33.427[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbPzbMapper.selectByExample     [0;39m [2m:[0;39m ==>  Preparing: SELECT mbywmc , zjzd , pxzd FROM mb_pzb WHERE ( ( sfqy = ? ) ) 
[2m2025-07-28 09:26:33.427[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbPzbMapper.selectByExample     [0;39m [2m:[0;39m ==> Parameters: 1(String)
[2m2025-07-28 09:26:33.436[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbPzbMapper.selectByExample     [0;39m [2m:[0;39m <==      Total: 10
[2m2025-07-28 09:26:33.439[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m ==>  Preparing: SELECT * from gj_mzdmb order by DM asc 
[2m2025-07-28 09:26:33.440[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-28 09:26:33.449[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m <==      Total: 58
[2m2025-07-28 09:26:34.117[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m ==>  Preparing: SELECT * from gj_sfzjdmb order by DM asc 
[2m2025-07-28 09:26:34.117[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-28 09:26:34.124[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m <==      Total: 12
[2m2025-07-28 09:26:34.269[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m ==>  Preparing: SELECT * from jy_kskmdmb order by XM_DM,KM_DM asc 
[2m2025-07-28 09:26:34.269[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-28 09:26:34.279[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m <==      Total: 106
[2m2025-07-28 09:26:35.568[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m ==>  Preparing: SELECT * from jy_rygwlxmb order by DM asc 
[2m2025-07-28 09:26:35.568[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-28 09:26:35.575[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m <==      Total: 3
[2m2025-07-28 09:26:35.618[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m ==>  Preparing: SELECT * from jy_rygwzzmb order by SSGWM,DM asc 
[2m2025-07-28 09:26:35.618[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-28 09:26:35.625[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m <==      Total: 5
[2m2025-07-28 09:26:35.695[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m ==>  Preparing: SELECT * from jy_wjlxmb order by DM asc 
[2m2025-07-28 09:26:35.695[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-28 09:26:35.701[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m <==      Total: 3
[2m2025-07-28 09:26:35.755[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m ==>  Preparing: SELECT * from jy_wjxmmb order by LX_DM,XM_DM asc 
[2m2025-07-28 09:26:35.756[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-28 09:26:35.764[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m <==      Total: 20
[2m2025-07-28 09:26:36.007[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m ==>  Preparing: SELECT * from jy_zcqswzdmb order by DM asc 
[2m2025-07-28 09:26:36.008[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-28 09:26:36.015[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m <==      Total: 4
[2m2025-07-28 09:26:36.067[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m ==>  Preparing: SELECT * from jy_zwbjfsdmb order by DM asc 
[2m2025-07-28 09:26:36.069[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-28 09:26:36.076[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m <==      Total: 8
[2m2025-07-28 09:26:36.177[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m ==>  Preparing: SELECT * from jy_zwplfsdmb order by DM asc 
[2m2025-07-28 09:26:36.178[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-28 09:26:36.184[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m <==      Total: 2
[2m2025-07-28 09:26:36.215[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36m.x.i.c.IdentityverifyBusiCacheInitConfig[0;39m [2m:[0;39m ####码表数据缓存初始化成功####
[2m2025-07-28 09:26:36.215[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36m.x.i.c.IdentityverifyBusiCacheInitConfig[0;39m [2m:[0;39m ####延迟队列初始化####
[2m2025-07-28 09:26:36.215[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.s.concurrent.ThreadPoolTaskExecutor [0;39m [2m:[0;39m Initializing ExecutorService
[2m2025-07-28 09:26:36.216[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36m.x.i.c.IdentityverifyBusiCacheInitConfig[0;39m [2m:[0;39m ####延迟队列初始化成功####
[2m2025-07-28 09:26:36.216[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36m.x.i.c.IdentityverifyBusiCacheInitConfig[0;39m [2m:[0;39m ####数据下发任务初始化####
[2m2025-07-28 09:26:36.221[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.K.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE ks_kssj_distribute_task SET complete = ?,complete_time = ? WHERE ( ( complete = ? ) ) 
[2m2025-07-28 09:26:36.222[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.K.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: 2(Integer), 2025-07-28 09:26:36.217(Timestamp), 1(Integer)
[2m2025-07-28 09:26:36.235[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.K.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-28 09:26:36.236[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36m.x.i.c.IdentityverifyBusiCacheInitConfig[0;39m [2m:[0;39m ####数据下发任务初始化成功####
[2m2025-07-28 09:26:36.236[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36m.x.i.c.IdentityverifyBusiCacheInitConfig[0;39m [2m:[0;39m ####数据库变更初始化####
[2m2025-07-28 09:26:36.236[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.service.impl.UtilsServiceImpl     [0;39m [2m:[0;39m dbName:[eeip_alone]
[2m2025-07-28 09:26:36.316[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-28 09:26:36.316[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone(String), ks_ksjh(String), cjlx(String)
[2m2025-07-28 09:26:36.324[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 09:26:36.325[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-28 09:26:36.325[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone(String), ks_ksjh(String), qydsxf(String)
[2m2025-07-28 09:26:36.332[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 09:26:36.333[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-28 09:26:36.333[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone(String), ks_ksjh(String), dsxfsj(String)
[2m2025-07-28 09:26:36.339[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 09:26:36.340[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-28 09:26:36.340[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone(String), ks_ksjh(String), dsxfzt(String)
[2m2025-07-28 09:26:36.347[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 09:26:36.349[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-28 09:26:36.349[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone(String), ks_ksjh(String), pack_jkrybpsj(String)
[2m2025-07-28 09:26:36.356[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 09:26:36.357[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-28 09:26:36.358[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone(String), ks_ksjh(String), pack_jkryjcsj(String)
[2m2025-07-28 09:26:36.364[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 09:26:36.364[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-28 09:26:36.365[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone(String), ks_ksjh(String), jkqdrs(String)
[2m2025-07-28 09:26:36.372[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 09:26:36.372[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-28 09:26:36.372[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone(String), ks_ksjh(String), dbms(String)
[2m2025-07-28 09:26:36.380[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 09:26:36.380[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-28 09:26:36.380[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone(String), ks_jkry_rcxx(String), jklsxh(String)
[2m2025-07-28 09:26:36.387[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 09:26:36.387[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-28 09:26:36.387[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone(String), ks_jkry_rcxx(String), xsd(String)
[2m2025-07-28 09:26:36.394[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 09:26:36.395[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-28 09:26:36.396[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone(String), ks_jkry_rcxx(String), sbzt(String)
[2m2025-07-28 09:26:36.403[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 09:26:36.403[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-28 09:26:36.403[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone(String), ks_jkry_rcxx(String), sbsj(String)
[2m2025-07-28 09:26:36.410[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 09:26:36.410[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-28 09:26:36.410[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone(String), ks_jkry_rcxx(String), tbzt(String)
[2m2025-07-28 09:26:36.417[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 09:26:36.417[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-28 09:26:36.418[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone(String), ks_jkry_rcxx(String), rgyzjg(String)
[2m2025-07-28 09:26:36.425[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 09:26:36.426[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.createCsDzgh   [0;39m [2m:[0;39m ==>  Preparing: CREATE TABLE IF NOT EXISTS `cs_dzgh` ( `id` varchar(32) NOT NULL, `cslx` varchar(32) DEFAULT NULL COMMENT 'KD(考点) KC(考场)', `type` varchar(32) DEFAULT NULL COMMENT '类型KCWG(考场网关)', `classify` varchar(32) DEFAULT NULL COMMENT '分类DEVCFG(设备配置)', `key` varchar(32) DEFAULT NULL COMMENT 'key值（IP:ip地址,NETMASK:掩码,GATEWAY:网关IP地址,IPRANGE:ip范围,PCNUM:主机数）', `value` varchar(128) DEFAULT NULL COMMENT 'value值', `create_time` datetime DEFAULT NULL COMMENT '创建时间', `update_time` datetime DEFAULT NULL COMMENT '更新时间', `status` decimal(10,0) DEFAULT NULL COMMENT '1-启用 0-不启用', `sort` decimal(10,0) DEFAULT NULL COMMENT '排序', `jsh` varchar(32) DEFAULT NULL COMMENT '教室号', PRIMARY KEY (`id`), KEY `index_dzgh_1` (`jsh`,`value`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8 
[2m2025-07-28 09:26:36.427[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.createCsDzgh   [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-28 09:26:36.439[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.createCsDzgh   [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-28 09:26:36.440[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.createSbIpdfp  [0;39m [2m:[0;39m ==>  Preparing: CREATE TABLE IF NOT EXISTS `sb_ipdfp` ( `id` varchar(32) NOT NULL, `ipstart` varchar(32) DEFAULT NULL COMMENT 'IP起', `ipend` varchar(32) DEFAULT NULL COMMENT 'IP尾', `zwym` varchar(32) DEFAULT NULL COMMENT '子网掩码', `wg` varchar(32) DEFAULT NULL COMMENT '网关', `create_time` datetime DEFAULT NULL, `update_time` datetime DEFAULT NULL, PRIMARY KEY (`id`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 
[2m2025-07-28 09:26:36.440[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.createSbIpdfp  [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-28 09:26:36.453[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.createSbIpdfp  [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-28 09:26:36.454[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.createSbSbjcb  [0;39m [2m:[0;39m ==>  Preparing: CREATE TABLE IF NOT EXISTS `sb_sbjcb` ( `id` varchar(32) NOT NULL, `sbxxbh` varchar(32) DEFAULT NULL COMMENT '设备信息编号', `ip` varchar(32) DEFAULT NULL COMMENT 'ip', `xlh` varchar(128) DEFAULT NULL COMMENT '序列号', `detect_type` varchar(10) DEFAULT NULL COMMENT '检测类型1-ping 2-mqtt', `detect_result` varchar(10) DEFAULT NULL COMMENT '检测结果1-成功 -1001-ping失败 -2001-mqtt超时 -9999-其他错误', `detect_desc` varchar(255) DEFAULT NULL COMMENT '检测描述', `detect_exception` text COMMENT '检测异常', `detect_time` timestamp NULL DEFAULT NULL COMMENT '检测时间', PRIMARY KEY (`id`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 
[2m2025-07-28 09:26:36.454[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.createSbSbjcb  [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-28 09:26:36.468[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.createSbSbjcb  [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-28 09:26:36.469[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createKsBmxxKstzz   [0;39m [2m:[0;39m ==>  Preparing: CREATE TABLE IF NOT EXISTS `ks_bmxx_kstzz` ( `ksid` varchar(32) NOT NULL COMMENT '考生唯一标志号', `ksjhbh` varchar(32) NOT NULL COMMENT '考试计划标志号', `ksh` varchar(20) DEFAULT NULL COMMENT '考生号', `sfzjhm` varchar(20) NOT NULL COMMENT '身份证件号', `kszpid` varchar(128) DEFAULT NULL COMMENT '考生照片ID', `tzzzt` varchar(2) DEFAULT NULL COMMENT '0-未获取到特征值；1-已获取到特征值；2-获取特征值失败', `tzzid` varchar(128) DEFAULT NULL COMMENT '特征值ID', `tzzhqsj` datetime DEFAULT NULL COMMENT '特征值获取时间', `create_time` datetime DEFAULT NULL COMMENT '创建时间', `update_time` datetime DEFAULT NULL COMMENT '更新时间', `tzzfs` varchar(1) DEFAULT NULL COMMENT '特征值方式', `bmh` varchar(32) DEFAULT NULL COMMENT '报名号', PRIMARY KEY (`ksid`) USING BTREE ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='考生报名特征值' 
[2m2025-07-28 09:26:36.469[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createKsBmxxKstzz   [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-28 09:26:36.482[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createKsBmxxKstzz   [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-28 09:26:36.482[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createKsYdsbSbkszs  [0;39m [2m:[0;39m ==>  Preparing: CREATE TABLE IF NOT EXISTS `ks_ydsb_sbkszs` ( `id` varchar(32) NOT NULL, `ksjhbh` varchar(32) NOT NULL COMMENT '考试计划编号', `sbxlh` varchar(64) NOT NULL COMMENT '设备序列号', `sbsj` datetime DEFAULT NULL COMMENT '上报时间', `kdmc` varchar(128) DEFAULT NULL COMMENT '考点名称', `sbkszs` int(10) DEFAULT NULL COMMENT '上报考生总数=入场人数+缺考人数', `create_time` datetime DEFAULT NULL COMMENT '创建时间', `update_time` datetime DEFAULT NULL COMMENT '更新时间', `ccm` varchar(10) DEFAULT NULL COMMENT '场次码', `kcbh` varchar(32) DEFAULT NULL COMMENT '考场编号', `ljkcbh` varchar(32) DEFAULT NULL COMMENT '逻辑考场编号', `sblx` varchar(10) DEFAULT NULL COMMENT '上报类型 BZ-标准 FB-非标', PRIMARY KEY (`id`), KEY `ks_ydsb_sbkszs__index_1` (`ksjhbh`,`ccm`,`sbxlh`,`kcbh`,`ljkcbh`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8 
[2m2025-07-28 09:26:36.483[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createKsYdsbSbkszs  [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-28 09:26:36.495[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createKsYdsbSbkszs  [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-28 09:26:36.496[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createBizCsxxExt    [0;39m [2m:[0;39m ==>  Preparing: create table if not exists biz_csxx_ext ( CSBH varchar(64) not null comment '场所编号' primary key, SBXLH varchar(128) null comment '设备序列号', YDZD_APP_VERSION varchar(128) null comment '移动终端app版本', ZHSBSJ datetime null comment '最后上报时间', XFZT int null comment '0-失败 1-下发中 2-成功' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 
[2m2025-07-28 09:26:36.496[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createBizCsxxExt    [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-28 09:26:36.509[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createBizCsxxExt    [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-28 09:26:36.510[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createBizFsgjTask   [0;39m [2m:[0;39m ==>  Preparing: create table if not exists biz_fsgj_task ( ID varchar(32) not null primary key, NAME varchar(128) null comment '任务名称', T_TYPE varchar(10) null comment '任务类型 1-移动终端APP', T_PROGRESS varchar(10) null comment '进度', COMPLETE int(10) null comment '完成状态 -1-失败 0-开始 1-进行中 2-完成', T_PROGRESS_DESC varchar(255) null comment '任务描述', T_PARAM longtext null comment '任务参数' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 
[2m2025-07-28 09:26:36.510[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createBizFsgjTask   [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-28 09:26:36.523[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createBizFsgjTask   [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-28 09:26:36.523[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createBizFsgjTaskJg [0;39m [2m:[0;39m ==>  Preparing: create table if not exists biz_fsgj_task_jg ( ID varchar(32) not null primary key, TASK_ID varchar(32) null comment '任务id', CSBH varchar(64) null comment '场所编号', SBXLH varchar(64) null comment '设备序列号' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 
[2m2025-07-28 09:26:36.523[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createBizFsgjTaskJg [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-28 09:26:36.536[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createBizFsgjTaskJg [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-28 09:26:36.538[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.filterTables   [0;39m [2m:[0;39m ==>  Preparing: select table_name from information_schema.tables where table_schema= ? AND table_name in ( ? , ? ) 
[2m2025-07-28 09:26:36.538[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.filterTables   [0;39m [2m:[0;39m ==> Parameters: eeip_alone(String), biz_send_command(String), biz_send_command_dev(String)
[2m2025-07-28 09:26:36.547[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.filterTables   [0;39m [2m:[0;39m <==      Total: 2
[2m2025-07-28 09:26:36.547[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-28 09:26:36.547[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone(String), sb_sbxx(String), osbb(String)
[2m2025-07-28 09:26:36.554[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 09:26:36.555[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-28 09:26:36.555[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone(String), sb_sbxx(String), ntp_enable(String)
[2m2025-07-28 09:26:36.562[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 09:26:36.562[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-28 09:26:36.562[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone(String), sb_sbxx(String), ntp_source(String)
[2m2025-07-28 09:26:36.570[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 09:26:36.570[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-28 09:26:36.571[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone(String), sb_sbxx(String), ntp_interval(String)
[2m2025-07-28 09:26:36.577[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 09:26:36.578[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.createSbsj     [0;39m [2m:[0;39m ==>  Preparing: create table if not exists sb_sbsj ( id varchar(32) charset utf8 not null primary key, sbxlh varchar(128) charset utf8 not null comment '设备序列号', sblx varchar(32) charset utf8 not null comment 'kdwg考点网关 kcwg考场网关 ydzd移动终端', event_type varchar(16) charset utf8 not null comment 'ONLINE-上线事件 USB-usb事件 WIFI-wifi事件 YDZDBATTERY-移动终端电池事件', event_time datetime not null comment '事件时间', csbh varchar(32) charset utf8 null comment '场所编号', event_detail text charset utf8 null comment '事件详情', create_time datetime null comment '创建时间', update_time datetime null comment '更新时间', status varchar(4) charset utf8 null comment '状态', `repeat` varchar(4) charset utf8 null comment '是否重复', repeat_count int(10) null comment '重复次数 ', `desc` text charset utf8 null comment '描述', param1 varchar(64) charset utf8 null comment '扩展1', param2 varchar(64) charset utf8 null comment '扩展2', param3 varchar(64) charset utf8 null comment '扩展3', event_time_start datetime null comment '事件开始时间', event_time_end datetime null comment '事件结束时间' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='设备事件' 
[2m2025-07-28 09:26:36.578[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.createSbsj     [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-28 09:26:36.591[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.createSbsj     [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-28 09:26:36.592[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-28 09:26:36.592[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone(String), ks_ksrcxx(String), tbzt(String)
[2m2025-07-28 09:26:36.599[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 09:26:36.600[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.alterColumn    [0;39m [2m:[0;39m ==>  Preparing: ALTER TABLE ks_ksrcxx modify COLUMN tbzt varchar(2) COMMENT ?; 
[2m2025-07-28 09:26:36.600[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.alterColumn    [0;39m [2m:[0;39m ==> Parameters: 同步状态(String)
[2m2025-07-28 09:26:36.617[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.alterColumn    [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-28 09:26:36.617[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.service.impl.UtilsServiceImpl     [0;39m [2m:[0;39m 数据库dbName:[eeip_alone] tbName:[ks_ksrcxx] columName:[tbzt]变更
[2m2025-07-28 09:26:36.617[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-28 09:26:36.618[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone(String), ks_ksrcxx(String), xsd(String)
[2m2025-07-28 09:26:36.625[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 09:26:36.626[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-28 09:26:36.626[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone(String), ks_ksrcxx(String), sc_sfzp(String)
[2m2025-07-28 09:26:36.634[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 09:26:36.634[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-28 09:26:36.635[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone(String), ks_ksrcxx(String), sc_rlzp(String)
[2m2025-07-28 09:26:36.641[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 09:26:36.642[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36m.x.i.c.IdentityverifyBusiCacheInitConfig[0;39m [2m:[0;39m ####数据库变更初始化成功####
[2m2025-07-28 09:26:36.642[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36m.x.i.c.IdentityverifyBusiCacheInitConfig[0;39m [2m:[0;39m ####缓存校级身份核验平台配置信息缓存成功####
[2m2025-07-28 09:26:36.642[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.config.ZookeeperInitRunner     [0;39m [2m:[0;39m ###SystemCache Succ<<<<<<<<<<<<<##
[2m2025-07-28 09:26:37.415[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[      Thread-70][0;39m [36mc.xcwlkj.msgque.que.XcRocektMqConsumer  [0;39m [2m:[0;39m 监听：JKYT_ATTENDANCE_JTXX,启动成功！
[2m2025-07-28 09:26:37.493[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[-**************][0;39m [36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Initializing Servlet 'dispatcherServlet'
[2m2025-07-28 09:26:37.524[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[-**************][0;39m [36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Completed initialization in 31 ms
[2m2025-07-28 09:26:37.919[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[-**************][0;39m [36mc.c.c.ConfigServicePropertySourceLocator[0;39m [2m:[0;39m Fetching config from server at : http://localhost:8888
[2m2025-07-28 09:26:38.186[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[-**************][0;39m [36mc.c.c.ConfigServicePropertySourceLocator[0;39m [2m:[0;39m Could not locate PropertySource: label not found
[2m2025-07-28 09:26:40.425[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[      Thread-71][0;39m [36mc.xcwlkj.msgque.que.XcRocektMqConsumer  [0;39m [2m:[0;39m 监听：JKYT_RSTJ_XXTS,启动成功！
[2m2025-07-28 09:26:47.811[0;39m [32m INFO [,a7ae5b29a77621a5,67e83dac82d6fb5d,true][0;39m [33m[eeip-standalone-service,a7ae5b29a77621a5,67e83dac82d6fb5d,a75963183f75eccb,true][0;39m [35m12424[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.b.u.m.s.impl.MqttMsgRevHandler      [0;39m [2m:[0;39m GenericMessage [payload={"data":{"OPERATION":"NOTIFY_DEVEVENT_YDZDBATTERY","TIMESTAMP":1753666005596,"Data":{"devType":"172","sbcsbh":"K3307820091100001","eventTime":"1753666005596","batteryVal":100,"eventType":"YDZDBATTERY","sn":"KEP20240707001"}},"sign":"","messageId":"batteryVal_1753666005596_KEP20240707001","timestamp":1753666005596,"token":"ee98d86d3ccc48a2a366770e8ffb7567"}, headers={mqtt_receivedRetained=false, spanTraceId=a7ae5b29a77621a5, spanId=a7ae5b29a77621a5, nativeHeaders={spanTraceId=[a7ae5b29a77621a5], spanId=[a7ae5b29a77621a5], spanSampled=[1]}, mqtt_duplicate=false, id=5db73daf-1663-0fa8-7a9b-eeba4a2aaf48, spanSampled=1, mqtt_receivedTopic=/HISOME_ANDROID/KEP20240707001/event/REPORT_DEVEVENT, mqtt_receivedQos=1}]
[2m2025-07-28 09:26:47.817[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.r.s.impl.BasicMqttMsgRevHandler     [0;39m [2m:[0;39m topic[/HISOME_ANDROID/KEP20240707001/event/REPORT_DEVEVENT] 没有方法可以处理这个主题...
[2m2025-07-28 09:26:47.817[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.service.impl.BizMqttMsgRevHandler [0;39m [2m:[0;39m topic[/HISOME_ANDROID/KEP20240707001/event/REPORT_DEVEVENT] 没有方法可以处理这个主题...
[2m2025-07-28 09:26:47.818[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m judgeDeviceIdExist:KEP20240707001
[2m2025-07-28 09:26:47.824[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mx.i.s.i.m.MqttReportDeveventEvantHandler[0;39m [2m:[0;39m 处理mqtt上报设备事件消息:BaseOperationReq(operation=NOTIFY_DEVEVENT_YDZDBATTERY, timestamp=1753666005596, data={"devType":"172","sbcsbh":"K3307820091100001","eventTime":"1753666005596","batteryVal":100,"eventType":"YDZDBATTERY","sn":"KEP20240707001"})
[2m2025-07-28 09:26:47.844[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.i.m.S.updateZxztAndNtpByXlh         [0;39m [2m:[0;39m ==>  Preparing: update sb_sbxx SET zxzt = ?, battery = ?, update_time = now() where xlh = ? 
[2m2025-07-28 09:26:47.845[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.i.m.S.updateZxztAndNtpByXlh         [0;39m [2m:[0;39m ==> Parameters: 1(String), 100(String), KEP20240707001(String)
[2m2025-07-28 09:26:47.861[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.i.m.S.updateZxztAndNtpByXlh         [0;39m [2m:[0;39m <==    Updates: 6
[2m2025-07-28 09:26:47.863[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.i.m.SbSbxxMapper.selectByExample    [0;39m [2m:[0;39m ==>  Preparing: SELECT sbxxbh,sbmc,sbxh,sbbm,sbpp,sbcs,sblx,sblxmc,jspc,jssj,sbccrq,sbazrq,sbzbq,sbwldz,macdz,ipdz,dkh,czxt,kzdkh,appbb,firmwarebb,xlh,ramdiskbb,sbzt,jhzt,sbmy,sczt,zxzt,sbzpid,sbqm,sbjcjg,zhzxsj,create_time,update_time,sbztw,sfydsb,osbb,battery,ntp_enable,ntp_source,ntp_interval,ext_status FROM sb_sbxx WHERE ( ( sczt = ? and xlh = ? ) ) 
[2m2025-07-28 09:26:47.864[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.i.m.SbSbxxMapper.selectByExample    [0;39m [2m:[0;39m ==> Parameters: 0(String), KEP20240707001(String)
[2m2025-07-28 09:26:47.874[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.i.m.SbSbxxMapper.selectByExample    [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 09:26:47.876[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mx.i.s.i.m.MqttReportDeveventEvantHandler[0;39m [2m:[0;39m 上报电量大于阈值, 不记录日志
[2m2025-07-28 09:26:58.452[0;39m [32m INFO [,360144c01d7bb313,360144c01d7bb313,false][0;39m [33m[eeip-standalone-service,360144c01d7bb313,360144c01d7bb313,,false][0;39m [35m12424[0;39m [2m---[0;39m [2m[nio-8888-exec-2][0;39m [36mc.x.i.facade.manager.CxtjController     [0;39m [2m:[0;39m 收到请求开始：[紧急呼叫查询][/manager/identityverify/cxtj/jjhjcx]reqModel:JjhjCxReq[ksjhbh=2507211439231396864138952572928,kcbh=<null>,clzt=<null>,startTime=<null>,endTime=<null>,pageNum=1,pageSize=10,msgId=<null>,transChannel=<null>,channelSerialNo=<null>,channelDate=<null>,channelTime=<null>,token=<null>,loginToken=<null>,transCode=<null>,clientIp=<null>,merchantId=<null>,appPackage=<null>]
[2m2025-07-28 09:26:58.489[0;39m [32m INFO [,360144c01d7bb313,360144c01d7bb313,false][0;39m [33m[eeip-standalone-service,360144c01d7bb313,360144c01d7bb313,,false][0;39m [35m12424[0;39m [2m---[0;39m [2m[nio-8888-exec-2][0;39m [36mc.x.i.s.impl.KsKwMessageServiceImpl     [0;39m [2m:[0;39m 紧急呼叫查询开始，查询条件：JjhjCxDTO[ksjhbh=2507211439231396864138952572928,kcbh=<null>,clzt=<null>,startTime=<null>,endTime=<null>,pageNum=1,pageSize=10]
[2m2025-07-28 09:26:58.545[0;39m [32mDEBUG [,360144c01d7bb313,360144c01d7bb313,false][0;39m [33m[eeip-standalone-service,360144c01d7bb313,360144c01d7bb313,,false][0;39m [35m12424[0;39m [2m---[0;39m [2m[nio-8888-exec-2][0;39m [36mc.x.i.m.K.selectJjhjList_COUNT          [0;39m [2m:[0;39m ==>  Preparing: SELECT count(0) FROM ks_kw_message msg LEFT JOIN ks_kw_message reply ON msg.ksjhbh = reply.ksjhbh AND msg.id = reply.replay_id AND reply.sczt = '0' WHERE msg.sczt = '0' AND msg.msg_type = '2000' AND msg.origin_app_type = 'HYAPP' AND msg.operate = '0' AND msg.ksjhbh = ? 
[2m2025-07-28 09:26:58.545[0;39m [32mDEBUG [,360144c01d7bb313,360144c01d7bb313,false][0;39m [33m[eeip-standalone-service,360144c01d7bb313,360144c01d7bb313,,false][0;39m [35m12424[0;39m [2m---[0;39m [2m[nio-8888-exec-2][0;39m [36mc.x.i.m.K.selectJjhjList_COUNT          [0;39m [2m:[0;39m ==> Parameters: 2507211439231396864138952572928(String)
[2m2025-07-28 09:26:58.552[0;39m [32mDEBUG [,360144c01d7bb313,360144c01d7bb313,false][0;39m [33m[eeip-standalone-service,360144c01d7bb313,360144c01d7bb313,,false][0;39m [35m12424[0;39m [2m---[0;39m [2m[nio-8888-exec-2][0;39m [36mc.x.i.m.K.selectJjhjList_COUNT          [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 09:26:58.555[0;39m [32m INFO [,360144c01d7bb313,360144c01d7bb313,false][0;39m [33m[eeip-standalone-service,360144c01d7bb313,360144c01d7bb313,,false][0;39m [35m12424[0;39m [2m---[0;39m [2m[nio-8888-exec-2][0;39m [36mc.x.i.s.impl.KsKwMessageServiceImpl     [0;39m [2m:[0;39m 紧急呼叫查询结束，共查询到0条记录
[2m2025-07-28 09:26:58.582[0;39m [32m INFO [,360144c01d7bb313,360144c01d7bb313,false][0;39m [33m[eeip-standalone-service,360144c01d7bb313,360144c01d7bb313,,false][0;39m [35m12424[0;39m [2m---[0;39m [2m[nio-8888-exec-2][0;39m [36mc.x.i.facade.manager.CxtjController     [0;39m [2m:[0;39m 处理请求结束：[紧急呼叫查询][/manager/identityverify/cxtj/jjhjcx]reqModel:JjhjCxReq[ksjhbh=2507211439231396864138952572928,kcbh=<null>,clzt=<null>,startTime=<null>,endTime=<null>,pageNum=1,pageSize=10,msgId=<null>,transChannel=<null>,channelSerialNo=<null>,channelDate=<null>,channelTime=<null>,token=<null>,loginToken=<null>,transCode=<null>,clientIp=<null>,merchantId=<null>,appPackage=<null>],respModel:JjhjCxResp[data=Page{count=true, pageNum=1, pageSize=10, startRow=0, endRow=10, total=0, pages=0, reasonable=true, pageSizeZero=false}[],totalRows=0,pageNum=1,pageSize=10,msgId=<null>,transChannel=<null>,channelSerialNo=<null>,transCode=<null>,respDate=<null>,respTime=<null>,ext=<null>]
[2m2025-07-28 09:27:21.855[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[duleTask-pool-0][0;39m [36mc.x.i.m.A.selectByExample               [0;39m [2m:[0;39m ==>  Preparing: SELECT id,version,created_by,creator_id,created_time,last_operator,last_operator_id,last_operator_time,ref_no,bucket_name,attachment_define_id,original_name,new_file_name,thumb_file_name,path,thumb_path,type,description,expire_time FROM attachment WHERE ( ( expire_time <= ? ) ) 
[2m2025-07-28 09:27:21.855[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[duleTask-pool-0][0;39m [36mc.x.i.m.A.selectByExample               [0;39m [2m:[0;39m ==> Parameters: 2025-07-28 09:27:21(String)
[2m2025-07-28 09:27:21.867[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[duleTask-pool-0][0;39m [36mc.x.i.m.A.selectByExample               [0;39m [2m:[0;39m <==      Total: 0
[2m2025-07-28 09:28:00.006[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[eduler_Worker-8][0;39m [36mc.x.i.util.schedule.ScheduleJobExecutor [0;39m [2m:[0;39m 任务准备执行，任务ID：23110110740401808639620095703040
[2m2025-07-28 09:28:00.023[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[eduler_Worker-8][0;39m [36mc.x.i.util.schedule.ScheduleJobExecutor [0;39m [2m:[0;39m 任务执行完毕，任务ID：23110110740401808639620095703040  总共耗时：17毫秒
[2m2025-07-28 09:28:00.026[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[eduler_Worker-8][0;39m [36mc.x.i.m.S.insertSelective               [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO schedule_job_log ( log_id,job_id,job_name,bean_name,result,times,create_time ) VALUES( ?,?,?,?,?,?,? ) 
[2m2025-07-28 09:28:00.026[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[eduler_Worker-8][0;39m [36mc.x.i.m.S.insertSelective               [0;39m [2m:[0;39m ==> Parameters: 25072809280002268833922467001344(String), 23110110740401808639620095703040(String), jobOnlineStatus(String), onlineStatusTaskService(String), 0(Integer), 17(Integer), 2025-07-28 09:28:00.006(Timestamp)
[2m2025-07-28 09:28:01.467[0;39m [31mERROR [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[tyVerify_test01][0;39m [36mo.s.i.m.outbound.MqttPahoMessageHandler [0;39m [2m:[0;39m Lost connection; will attempt reconnect on next request
[2m2025-07-28 09:30:00.003[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[eduler_Worker-9][0;39m [36mc.x.i.util.schedule.ScheduleJobExecutor [0;39m [2m:[0;39m 任务准备执行，任务ID：23110111540401808639620095703040
[2m2025-07-28 09:30:00.004[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[duler_Worker-10][0;39m [36mc.x.i.util.schedule.ScheduleJobExecutor [0;39m [2m:[0;39m 任务准备执行，任务ID：23112811035901828218591246338048
[2m2025-07-28 09:30:00.004[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[eduler_Worker-3][0;39m [36mc.x.i.util.schedule.ScheduleJobExecutor [0;39m [2m:[0;39m 任务准备执行，任务ID：24081610292802018092458168076288
[2m2025-07-28 09:30:00.019[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[duler_Worker-10][0;39m [36mc.x.i.m.J.selectOneByExample            [0;39m [2m:[0;39m ==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) 
[2m2025-07-28 09:30:00.019[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[duler_Worker-10][0;39m [36mc.x.i.m.J.selectOneByExample            [0;39m [2m:[0;39m ==> Parameters: HISOME_subDevType(String)
[2m2025-07-28 09:30:00.029[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[duler_Worker-10][0;39m [36mc.x.i.m.J.selectOneByExample            [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 09:30:00.029[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[eduler_Worker-9][0;39m [36mc.x.i.m.SbSbxxMapper.selectByExample    [0;39m [2m:[0;39m ==>  Preparing: SELECT sbxxbh,sbmc,sbxh,sbbm,sbpp,sbcs,sblx,sblxmc,jspc,jssj,sbccrq,sbazrq,sbzbq,sbwldz,macdz,ipdz,dkh,czxt,kzdkh,appbb,firmwarebb,xlh,ramdiskbb,sbzt,jhzt,sbmy,sczt,zxzt,sbzpid,sbqm,sbjcjg,zhzxsj,create_time,update_time,sbztw,sfydsb,osbb,battery,ntp_enable,ntp_source,ntp_interval,ext_status FROM sb_sbxx WHERE ( ( sczt = ? ) ) 
[2m2025-07-28 09:30:00.029[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[eduler_Worker-9][0;39m [36mc.x.i.m.SbSbxxMapper.selectByExample    [0;39m [2m:[0;39m ==> Parameters: 0(String)
[2m2025-07-28 09:30:00.045[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[eduler_Worker-9][0;39m [36mc.x.i.m.SbSbxxMapper.selectByExample    [0;39m [2m:[0;39m <==      Total: 16
[2m2025-07-28 09:30:00.045[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[eduler_Worker-3][0;39m [36mc.x.i.m.KsKsjhMapper.selectByExample    [0;39m [2m:[0;39m ==>  Preparing: SELECT ksjhbh,mc,kssj,jssj,xn,xq,sfmr,kszt,sbjm,sbkqsj,sbgbsj,cjlx,kslx,qysbbmd,wifiqy,cjsj,xgsj,scztw,kzqy,qydsxf,dsxfsj,dsxfzt,jkqdrs,dbms FROM ks_ksjh WHERE ( ( scztw = ? and qydsxf = ? and dsxfzt = ? ) ) 
[2m2025-07-28 09:30:00.045[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[eduler_Worker-3][0;39m [36mc.x.i.m.KsKsjhMapper.selectByExample    [0;39m [2m:[0;39m ==> Parameters: 0(String), 1(Integer), 0(Integer)
[2m2025-07-28 09:30:00.059[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[eduler_Worker-9][0;39m [36mc.x.i.t.u.h.s.i.UnifyAccessServiceImpl  [0;39m [2m:[0;39m url http://*************:8844/device/category?terms[0].column=parentId&terms[0].value=-5-
[2m2025-07-28 09:30:00.269[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[eduler_Worker-3][0;39m [36mc.x.i.m.KsKsjhMapper.selectByExample    [0;39m [2m:[0;39m <==      Total: 0
[2m2025-07-28 09:30:00.269[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[eduler_Worker-3][0;39m [36mc.x.i.s.i.s.KsjhSjdsxfTaskServiceImpl   [0;39m [2m:[0;39m 当前无下发任务！
[2m2025-07-28 09:30:00.269[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[duler_Worker-10][0;39m [36mc.x.i.m.S.selectBySubStatusAndType      [0;39m [2m:[0;39m ==>  Preparing: SELECT xlh FROM sb_sbxx WHERE sczt = '0' AND sbztw = '0' AND sblx IN ( ? , ? ) 
[2m2025-07-28 09:30:00.269[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[eduler_Worker-3][0;39m [36mc.x.i.util.schedule.ScheduleJobExecutor [0;39m [2m:[0;39m 任务执行完毕，任务ID：24081610292802018092458168076288  总共耗时：265毫秒
[2m2025-07-28 09:30:00.274[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[duler_Worker-10][0;39m [36mc.x.i.m.S.selectBySubStatusAndType      [0;39m [2m:[0;39m ==> Parameters: kcdzbp(String), ydzd(String)
[2m2025-07-28 09:30:00.280[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[duler_Worker-10][0;39m [36mc.x.i.m.S.selectBySubStatusAndType      [0;39m [2m:[0;39m <==      Total: 3
[2m2025-07-28 09:30:00.280[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[duler_Worker-10][0;39m [36mc.x.i.s.i.s.SbxxReportTaskServiceImpl   [0;39m [2m:[0;39m 设备信息上报，上报类型：[kcdzbp, ydzd]，上报设备序列号：[testYdzdXlh001, testYdzdXlh002, testYdzd004]
[2m2025-07-28 09:30:00.280[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[eduler_Worker-3][0;39m [36mc.x.i.m.S.insertSelective               [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO schedule_job_log ( log_id,job_id,job_name,bean_name,params,result,times,create_time ) VALUES( ?,?,?,?,?,?,?,? ) 
[2m2025-07-28 09:30:00.280[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[eduler_Worker-3][0;39m [36mc.x.i.m.S.insertSelective               [0;39m [2m:[0;39m ==> Parameters: 25072809300002268834929083184129(String), 24081610292802018092458168076288(String), ksjhSjdsxf(String), ksjhSjdsxfTaskService(String), (String), 0(Integer), 265(Integer), 2025-07-28 09:30:00.004(Timestamp)
[2m2025-07-28 09:30:00.285[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[duler_Worker-10][0;39m [36mc.x.i.m.S.getDevicePlaceRelation        [0;39m [2m:[0;39m ==>  Preparing: SELECT sb_sbxx.xlh AS sbxlh, sb_sbxx.sbmc AS sbmc, sb_sbxx.sblx AS sblb, sb_sbxx.sbzbq AS sbzbjzsj, sb_sbxx.jssj AS cgsj, sb_sbxx.sbcs AS sbcsm, sb_sbxx.sbpp AS sbpp, sb_sbxx.sbzt AS ywzt, sb_sbxx.ipdz AS sbip, sb_sbxx.sfydsb AS sfydsb, csgx.csbh AS csdm, sb_sbxx.sbmy AS sbmy FROM sb_sbxx INNER JOIN sb_sbcsgx csgx ON sb_sbxx.sbxxbh = csgx.sbbh WHERE sb_sbxx.sczt = '0' AND sb_sbxx.sbzt != '3' AND sb_sbxx.xlh IN ( ? , ? , ? ) 
[2m2025-07-28 09:30:00.285[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[duler_Worker-10][0;39m [36mc.x.i.m.S.getDevicePlaceRelation        [0;39m [2m:[0;39m ==> Parameters: testYdzdXlh001(String), testYdzdXlh002(String), testYdzd004(String)
[2m2025-07-28 09:30:00.295[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[duler_Worker-10][0;39m [36mc.x.i.m.S.getDevicePlaceRelation        [0;39m [2m:[0;39m <==      Total: 0
[2m2025-07-28 09:30:00.295[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[duler_Worker-10][0;39m [36mc.x.i.m.CsXxjbxxMapper.selectByExample  [0;39m [2m:[0;39m ==>  Preparing: SELECT xxdm,xxmc,zzjgm,xxdz,xzqhm FROM cs_xxjbxx 
[2m2025-07-28 09:30:00.295[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[duler_Worker-10][0;39m [36mc.x.i.m.CsXxjbxxMapper.selectByExample  [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-28 09:30:00.300[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[duler_Worker-10][0;39m [36mc.x.i.m.CsXxjbxxMapper.selectByExample  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 09:30:00.300[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[duler_Worker-10][0;39m [36mc.x.i.service.impl.SbSbxxServiceImpl    [0;39m [2m:[0;39m 没有需要上报的设备
[2m2025-07-28 09:30:00.300[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[duler_Worker-10][0;39m [36mc.x.i.util.schedule.ScheduleJobExecutor [0;39m [2m:[0;39m 任务执行完毕，任务ID：23112811035901828218591246338048  总共耗时：296毫秒
[2m2025-07-28 09:30:00.305[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[duler_Worker-10][0;39m [36mc.x.i.m.S.insertSelective               [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO schedule_job_log ( log_id,job_id,job_name,bean_name,params,result,times,create_time ) VALUES( ?,?,?,?,?,?,?,? ) 
[2m2025-07-28 09:30:00.305[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[duler_Worker-10][0;39m [36mc.x.i.m.S.insertSelective               [0;39m [2m:[0;39m ==> Parameters: 25072809300002268834929083184128(String), 23112811035901828218591246338048(String), 设备信息上报(String), sbxxReportTaskService(String), (String), 0(Integer), 296(Integer), 2025-07-28 09:30:00.004(Timestamp)
[2m2025-07-28 09:30:00.406[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[eduler_Worker-9][0;39m [36mc.x.i.t.u.h.s.i.UnifyAccessServiceImpl  [0;39m [2m:[0;39m 发送给接入返回结果result=JetlinksWrapper(message=success, status=200, code=200, timestamp=1753666199247, result=[DeviceCategoryRespModel(id=AIEdgeServer, parentId=-5-, key=AIEdgeServer, name=边缘计算服务器, productId=HISOME-DX000, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1747805356879, productName=汉尚多模态超脑), DeviceCategoryRespModel(id=cheating_prevention, parentId=-5-, key=CheatingPrevention, name=作弊防控设备, productId=HISOME-RRS3000, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1655261812143, productName=3536侦测服务器), DeviceCategoryRespModel(id=digital_hd_matrix, parentId=-5-, key=digital_hd_matrix, name=数字高清矩阵, productId=null, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1692077761801, productName=null), DeviceCategoryRespModel(id=ejjksb, parentId=-5-, key=ejjksb, name=耳机监考设备, productId=CENC_HEADPHONE, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1739411557430, productName=头戴式耳机), DeviceCategoryRespModel(id=hdd_box, parentId=-5-, key=hdd_box, name=视频存储盒子, productId=HISOME-HDD4000, sortIndex=1, level=2, creatorId=1199596756811550720, createTime=1680154724527, productName=视频数据存储终端), DeviceCategoryRespModel(id=hskpjmwxpb, parentId=-5-, key=hskpjmwxpb, name=汉尚考培加密无线平板, productId=null, sortIndex=1, level=1, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1740365256141, productName=null), DeviceCategoryRespModel(id=ipc, parentId=-5-, key=ipc, name=网络摄像机, productId=HISOME-IPC, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1691550353555, productName=IPC通用摄像机), DeviceCategoryRespModel(id=kcdzbp, parentId=-5-, key=kcdzbp, name=考场电子班牌, productId=PROD-DZBP, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1697418220328, productName=电子班牌), DeviceCategoryRespModel(id=kcwg, parentId=-5-, key=kcwg, name=考场网关, productId=CENCGW100_C, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1661844529888, productName=智能教室网关), DeviceCategoryRespModel(id=kdwg, parentId=-5-, key=kdwg, name=考点网关, productId=CENCGW100_S, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1661844519215, productName=智能校级网关), DeviceCategoryRespModel(id=lhsjkydzd, parentId=-5-, key=lhsjkydzd, name=理化生机考移动终端, productId=HISOME-PAD-101, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1740365218610, productName=汉尚理化生机考移动终端), DeviceCategoryRespModel(id=nvr, parentId=-5-, key=nvr, name=数字硬盘录像机, productId=HISOME-NVR4000, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1687656114753, productName=数字硬盘录像机), DeviceCategoryRespModel(id=sip, parentId=-5-, key=sip, name=SIP路由分发服务器, productId=SIP8000, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1689045772782, productName=SIP路由分发服务器), DeviceCategoryRespModel(id=tzsxj, parentId=-5-, key=tzsxj, name=同轴摄像机, productId=null, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1691552091707, productName=null), DeviceCategoryRespModel(id=ydzd, parentId=-5-, key=ydzd, name=移动终端, productId=HISOME_ANDROID, sortIndex=1, level=1, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1661844790208, productName=身份核验移动终端)])
[2m2025-07-28 09:30:00.409[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[eduler_Worker-9][0;39m [36mc.x.i.service.impl.SbSbxxServiceImpl    [0;39m [2m:[0;39m 
[2m2025-07-28 09:30:00.418[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[eduler_Worker-9][0;39m [36mc.x.i.t.u.h.s.i.UnifyAccessServiceImpl  [0;39m [2m:[0;39m Emqx消息桥接查询订阅 url=http://*************:8844/remote/access/emqx/subscribers/list
[2m2025-07-28 09:30:00.459[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[eduler_Worker-9][0;39m [36mc.x.i.t.u.h.s.i.UnifyAccessServiceImpl  [0;39m [2m:[0;39m Emqx消息桥接查询订阅 result=Wrapper(code=200, message=success, result=EmqxSubscribersListRespModel(subscribers=[/HISOME_ANDROID/testYdzdXlh001/function/invoke, /CENCGW100_C/DENCGW829_C04MB10BDJ027590/function/invoke, /HISOME_ANDROID/testYdzd004/function/invoke, /PROD-DZBP/test0012/function/invoke, /PROD-DZBP/FC1836988/function/invoke, /HISOME_ANDROID/ddcdb3ac5810283f/function/invoke, /PROD-DZBP/G04754406/properties/read, /PROD-DZBP/FC1836989/function/invoke, /HISOME_ANDROID/KEP20240707001/function/invoke, /PROD-DZBP/AB1602853/function/invoke, /HISOME_ANDROID/testYdzdXlh002/function/invoke, /CENCGW100_S/CENCGW100_SIV1c9f717de4acfc99/function/invoke, /CENCGW100_S/CENCGW100_SIVb33ad3ede4acfc99/function/invoke, /PROD-DZBP/AB1602853/properties/read, /HISOME_ANDROID/ATN240303000100YT55YBF/function/invoke, /PROD-DZBP/G16733836/function/invoke, /HISOME_ANDROID/S30SZA2023140181/function/invoke, /CENCGW100_C/DENCGW829_C04MB10BDJ07548e/function/invoke, /PROD-DZBP/G04754406/function/invoke, /CENCGW100_S/CENCGW100_SIVb770d1a6a1ec5b44/function/invoke]))
[2m2025-07-28 09:30:00.465[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[eduler_Worker-9][0;39m [36mc.x.i.util.schedule.ScheduleJobExecutor [0;39m [2m:[0;39m 任务执行完毕，任务ID：23110111540401808639620095703040  总共耗时：462毫秒
[2m2025-07-28 09:30:00.469[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[eduler_Worker-9][0;39m [36mc.x.i.m.S.insertSelective               [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO schedule_job_log ( log_id,job_id,job_name,bean_name,result,times,create_time ) VALUES( ?,?,?,?,?,?,? ) 
[2m2025-07-28 09:30:00.469[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[eduler_Worker-9][0;39m [36mc.x.i.m.S.insertSelective               [0;39m [2m:[0;39m ==> Parameters: 25072809300002268834929074795520(String), 23110111540401808639620095703040(String), jobSubscribeMqtt(String), subscriberDevInfoToMqttTaskService(String), 0(Integer), 462(Integer), 2025-07-28 09:30:00.003(Timestamp)
[2m2025-07-28 09:33:34.419[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[     Thread-129][0;39m [36mo.s.c.n.e.s.EurekaServiceRegistry       [0;39m [2m:[0;39m Unregistering application EEIP-STANDALONE-SERVICE with eureka with status DOWN
[2m2025-07-28 09:33:38.449[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[     Thread-129][0;39m [36m.m.i.MqttPahoMessageDrivenChannelAdapter[0;39m [2m:[0;39m stopped inbound
[2m2025-07-28 09:33:38.450[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[     Thread-129][0;39m [36mProxyFactoryBean$MethodInvocationGateway[0;39m [2m:[0;39m stopped mqttGateway
[2m2025-07-28 09:33:38.450[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[     Thread-129][0;39m [36mo.s.i.gateway.GatewayProxyFactoryBean   [0;39m [2m:[0;39m stopped mqttGateway
[2m2025-07-28 09:33:38.450[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[     Thread-129][0;39m [36mo.s.i.endpoint.EventDrivenConsumer      [0;39m [2m:[0;39m Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
[2m2025-07-28 09:33:38.451[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[     Thread-129][0;39m [36mo.s.i.channel.PublishSubscribeChannel   [0;39m [2m:[0;39m Channel 'application-1.errorChannel' has 0 subscriber(s).
[2m2025-07-28 09:33:38.451[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[     Thread-129][0;39m [36mo.s.i.endpoint.EventDrivenConsumer      [0;39m [2m:[0;39m stopped _org.springframework.integration.errorLogger
[2m2025-07-28 09:33:38.451[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[     Thread-129][0;39m [36mo.s.i.endpoint.EventDrivenConsumer      [0;39m [2m:[0;39m Removing {message-handler:mqttConfig.handler.serviceActivator} as a subscriber to the 'mqttInputChannel' channel
[2m2025-07-28 09:33:38.451[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[     Thread-129][0;39m [36mo.s.integration.channel.ExecutorChannel [0;39m [2m:[0;39m Channel 'application-1.mqttInputChannel' has 0 subscriber(s).
[2m2025-07-28 09:33:38.451[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[     Thread-129][0;39m [36mo.s.i.endpoint.EventDrivenConsumer      [0;39m [2m:[0;39m stopped mqttConfig.handler.serviceActivator
[2m2025-07-28 09:33:38.451[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[     Thread-129][0;39m [36mo.s.i.endpoint.EventDrivenConsumer      [0;39m [2m:[0;39m Removing {message-handler:mqttConfig.mqttOutbound.serviceActivator} as a subscriber to the 'mqttOutboundChannel' channel
[2m2025-07-28 09:33:38.451[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[     Thread-129][0;39m [36mo.s.integration.channel.ExecutorChannel [0;39m [2m:[0;39m Channel 'application-1.mqttOutboundChannel' has 0 subscriber(s).
[2m2025-07-28 09:33:38.451[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[     Thread-129][0;39m [36mo.s.i.endpoint.EventDrivenConsumer      [0;39m [2m:[0;39m stopped mqttConfig.mqttOutbound.serviceActivator
[2m2025-07-28 09:33:40.510[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[     Thread-129][0;39m [36mo.s.s.c.ThreadPoolTaskScheduler         [0;39m [2m:[0;39m Shutting down ExecutorService 'taskScheduler'
[2m2025-07-28 09:33:40.510[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[     Thread-129][0;39m [36mo.s.s.quartz.SchedulerFactoryBean       [0;39m [2m:[0;39m Shutting down Quartz Scheduler
[2m2025-07-28 09:33:40.512[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[     Thread-129][0;39m [36mo.s.s.concurrent.ThreadPoolTaskExecutor [0;39m [2m:[0;39m Shutting down ExecutorService
[2m2025-07-28 09:33:42.330[0;39m [31mERROR [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m12424[0;39m [2m---[0;39m [2m[ool-11-thread-2][0;39m [36mc.x.i.t.d.DistributeRedisRateLimiter    [0;39m [2m:[0;39m applicaitonContext属性未注入, 请在applicationContext.xml中定义SpringBeanUtil

java.lang.IllegalStateException: applicaitonContext属性未注入, 请在applicationContext.xml中定义SpringBeanUtil
	at org.apache.commons.lang3.Validate.validState(Validate.java:829)
	at com.xcwlkj.core.util.SpringBeanUtil.assertContextInjected(SpringBeanUtil.java:91)
	at com.xcwlkj.core.util.SpringBeanUtil.getBean(SpringBeanUtil.java:47)
	at com.xcwlkj.core.util.RedisUtil.getRedisCacheClient(RedisUtil.java:61)
	at com.xcwlkj.core.util.RedisUtil.redisKeys(RedisUtil.java:116)
	at com.xcwlkj.identityverify.taskcenter.distributeTask.DistributeRedisRateLimiter.refreshBucket(DistributeRedisRateLimiter.java:73)
	at com.xcwlkj.identityverify.taskcenter.distributeTask.DistributeRedisRateLimiter.lambda$0(DistributeRedisRateLimiter.java:136)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[2m2025-07-28 09:33:53.806[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$bd2e7cfa] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2m2025-07-28 09:33:54.280[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.c.c.ConfigServicePropertySourceLocator[0;39m [2m:[0;39m Fetching config from server at : http://localhost:8888
[2m2025-07-28 09:33:56.425[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.c.c.ConfigServicePropertySourceLocator[0;39m [2m:[0;39m Connect Timeout Exception on Url - http://localhost:8888. Will be trying the next url if available
[2m2025-07-28 09:33:56.426[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.c.c.ConfigServicePropertySourceLocator[0;39m [2m:[0;39m Could not locate PropertySource: I/O error on GET request for "http://localhost:8888/eeip-standalone-service/default": Connection refused: connect; nested exception is java.net.ConnectException: Connection refused: connect
[2m2025-07-28 09:33:56.429[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.standalone.StandaloneApplication    [0;39m [2m:[0;39m The following profiles are active: alone
[2m2025-07-28 09:34:01.511[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Multiple Spring Data modules found, entering strict repository configuration mode!
[2m2025-07-28 09:34:01.515[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[2m2025-07-28 09:34:01.784[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 247ms. Found 0 Redis repository interfaces.
[2m2025-07-28 09:34:01.962[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.boot.actuate.endpoint.EndpointId    [0;39m [2m:[0;39m Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
[2m2025-07-28 09:34:02.193[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.boot.actuate.endpoint.EndpointId    [0;39m [2m:[0;39m Endpoint ID 'hystrix.stream' contains invalid characters, please migrate to a valid format.
[2m2025-07-28 09:34:02.613[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.cloud.context.scope.GenericScope    [0;39m [2m:[0;39m BeanFactory id=5b395cf1-ddbf-331b-876f-aa2c7020b748
[2m2025-07-28 09:34:02.636[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mfaultConfiguringBeanFactoryPostProcessor[0;39m [2m:[0;39m No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
[2m2025-07-28 09:34:02.645[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mfaultConfiguringBeanFactoryPostProcessor[0;39m [2m:[0;39m No bean named 'taskScheduler' has been explicitly defined. Therefore, a default ThreadPoolTaskScheduler will be created.
[2m2025-07-28 09:34:02.657[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mfaultConfiguringBeanFactoryPostProcessor[0;39m [2m:[0;39m No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
[2m2025-07-28 09:34:02.747[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'configProperties' of type [com.xcwlkj.core.config.properties.ConfigProperties$$EnhancerBySpringCGLIB$$fe0c5993] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2m2025-07-28 09:34:02.748[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'asyncTaskExecutorConfiguration' of type [com.xcwlkj.core.config.AsyncTaskExecutorConfiguration$$EnhancerBySpringCGLIB$$b73e9ec3] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2m2025-07-28 09:34:02.815[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$96d81222] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2m2025-07-28 09:34:03.091[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$a11479fd] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2m2025-07-28 09:34:03.145[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'redisConfig' of type [com.xcwlkj.standalone.config.RedisConfig$$EnhancerBySpringCGLIB$$7cb7577a] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2m2025-07-28 09:34:03.187[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'appConfig' of type [com.xcwlkj.standalone.config.AppConfig$$EnhancerBySpringCGLIB$$b8d88920] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2m2025-07-28 09:34:03.215[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'integrationDisposableAutoCreatedBeans' of type [org.springframework.integration.config.annotation.Disposables] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2m2025-07-28 09:34:03.258[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'com.xcwlkj.biz.core.feignclient.FeignClientConfig' of type [com.xcwlkj.biz.core.feignclient.FeignClientConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2m2025-07-28 09:34:03.266[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'org.springframework.integration.config.IntegrationManagementConfiguration' of type [org.springframework.integration.config.IntegrationManagementConfiguration$$EnhancerBySpringCGLIB$$c06352c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2m2025-07-28 09:34:03.299[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$bd2e7cfa] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2m2025-07-28 09:34:03.885[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat initialized with port(s): 8888 (http)
[2m2025-07-28 09:34:04.027[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.web.context.ContextLoader           [0;39m [2m:[0;39m Root WebApplicationContext: initialization completed in 7574 ms
[2m2025-07-28 09:34:07.278[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.a.e.web.ServletEndpointRegistrar  [0;39m [2m:[0;39m Registered '/actuator/hystrix.stream' to hystrix.stream-actuator-endpoint
[2m2025-07-28 09:34:07.372[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.s.concurrent.ThreadPoolTaskExecutor [0;39m [2m:[0;39m Initializing ExecutorService
[2m2025-07-28 09:34:07.514[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.s.concurrent.ThreadPoolTaskExecutor [0;39m [2m:[0;39m Initializing ExecutorService
[2m2025-07-28 09:34:07.564[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.s.concurrent.ThreadPoolTaskExecutor [0;39m [2m:[0;39m Initializing ExecutorService
[2m2025-07-28 09:34:07.571[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.xcwlkj.standalone.config.XcDfsConfig  [0;39m [2m:[0;39m ###dfsServerUrl=http://************:8811
[2m2025-07-28 09:34:07.571[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.xcwlkj.standalone.config.XcDfsConfig  [0;39m [2m:[0;39m ###dfsChannel=ZJKSZHPT
[2m2025-07-28 09:34:07.571[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.xcwlkj.standalone.config.XcDfsConfig  [0;39m [2m:[0;39m ###dfsAppId=e046e7c7e7fbf1f1a4183e00c76e0182
[2m2025-07-28 09:34:07.571[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.xcwlkj.standalone.config.XcDfsConfig  [0;39m [2m:[0;39m ###dfsAppSecret=6d0dc6d02af7bab1f1ed39a7baadbb55
[2m2025-07-28 09:34:08.471[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.interceptor.SqlLogInterceptor  [0;39m [2m:[0;39m [打印SQL拦截器创建]noticeTime=5.0秒
[2m2025-07-28 09:34:10.576[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.c.c.AsyncTaskExecutorConfiguration  [0;39m [2m:[0;39m Creating Async Task Executor
[2m2025-07-28 09:34:10.577[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.s.concurrent.ThreadPoolTaskExecutor [0;39m [2m:[0;39m Initializing ExecutorService
[2m2025-07-28 09:34:12.093[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.xcwlkj.pubc.config.SmsConfig        [0;39m [2m:[0;39m SMS Bean IAcsClient Start
[2m2025-07-28 09:34:12.104[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.xcwlkj.pubc.config.SmsConfig        [0;39m [2m:[0;39m 加载SMS Bean IAcsClient OK
[2m2025-07-28 09:34:19.796[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.identityverify.config.XcDfsConfig   [0;39m [2m:[0;39m ####文件系统初始化####
[2m2025-07-28 09:34:19.804[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.identityverify.config.XcDfsConfig   [0;39m [2m:[0;39m ####文件系统初始化成功####
[2m2025-07-28 09:34:21.252[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.taskcenter.DefaultHandleFactory   [0;39m [2m:[0;39m 配置线程池工作线程数量[16]
[2m2025-07-28 09:34:21.348[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.handler.UpperHsDfsHandler         [0;39m [2m:[0;39m 开始检查平台类型和初始化上级平台DFS客户端...
[2m2025-07-28 09:34:21.574[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.J.selectOneByExample            [0;39m [2m:[0;39m ==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) 
[2m2025-07-28 09:34:21.586[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.J.selectOneByExample            [0;39m [2m:[0;39m ==> Parameters: defaultPlat(String)
[2m2025-07-28 09:34:21.603[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.J.selectOneByExample            [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 09:34:21.607[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.handler.UpperHsDfsHandler         [0;39m [2m:[0;39m 当前平台类型为：HISOME，开始初始化上级平台DFS客户端
[2m2025-07-28 09:34:21.608[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.J.selectOneByExample            [0;39m [2m:[0;39m ==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) 
[2m2025-07-28 09:34:21.609[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.J.selectOneByExample            [0;39m [2m:[0;39m ==> Parameters: HISOME_fileServerUrl(String)
[2m2025-07-28 09:34:21.617[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.J.selectOneByExample            [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 09:34:21.618[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.J.selectOneByExample            [0;39m [2m:[0;39m ==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) 
[2m2025-07-28 09:34:21.619[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.J.selectOneByExample            [0;39m [2m:[0;39m ==> Parameters: HISOME_fileServerChannel(String)
[2m2025-07-28 09:34:21.625[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.J.selectOneByExample            [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 09:34:21.627[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.J.selectOneByExample            [0;39m [2m:[0;39m ==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) 
[2m2025-07-28 09:34:21.627[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.J.selectOneByExample            [0;39m [2m:[0;39m ==> Parameters: HISOME_fileServiceAppId(String)
[2m2025-07-28 09:34:21.633[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.J.selectOneByExample            [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 09:34:21.634[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.J.selectOneByExample            [0;39m [2m:[0;39m ==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) 
[2m2025-07-28 09:34:21.634[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.J.selectOneByExample            [0;39m [2m:[0;39m ==> Parameters: HISOME_fileServiceAppSecret(String)
[2m2025-07-28 09:34:21.642[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.J.selectOneByExample            [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 09:34:21.652[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.handler.UpperHsDfsHandler         [0;39m [2m:[0;39m 上级平台DFS客户端初始化成功
[2m2025-07-28 09:34:21.652[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.handler.UpperHsDfsHandler         [0;39m [2m:[0;39m 上级平台DFS客户端自动初始化完成
[2m2025-07-28 09:34:23.082[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.s.impl.ScheduleJobServiceImpl     [0;39m [2m:[0;39m ###定时任务初始化启动中....
[2m2025-07-28 09:34:23.082[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.ScheduleJobMapper.selectAll     [0;39m [2m:[0;39m ==>  Preparing: SELECT job_id,job_name,bean_name,params,cron_expression,status,remark,create_time,update_time FROM schedule_job 
[2m2025-07-28 09:34:23.083[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.ScheduleJobMapper.selectAll     [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-28 09:34:23.091[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.ScheduleJobMapper.selectAll     [0;39m [2m:[0;39m <==      Total: 10
[2m2025-07-28 09:34:23.101[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.s.impl.ScheduleJobServiceImpl     [0;39m [2m:[0;39m ###定时任务初始化启动[teskTask-jobTest111]
[2m2025-07-28 09:34:23.102[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.s.impl.ScheduleJobServiceImpl     [0;39m [2m:[0;39m ###定时任务初始化启动[sbzxztCxAndGxTaskService-jobSbzxztGx]
[2m2025-07-28 09:34:23.102[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.s.impl.ScheduleJobServiceImpl     [0;39m [2m:[0;39m ###定时任务初始化启动[onlineStatusTaskService-jobOnlineStatus]
[2m2025-07-28 09:34:23.102[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.s.impl.ScheduleJobServiceImpl     [0;39m [2m:[0;39m ###定时任务初始化启动[subscriberDevInfoToMqttTaskService-jobSubscribeMqtt]
[2m2025-07-28 09:34:23.102[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.s.impl.ScheduleJobServiceImpl     [0;39m [2m:[0;39m ###定时任务初始化启动[sjxfsbCxxfTaskService-sjxfsbCxxf]
[2m2025-07-28 09:34:23.103[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.s.impl.ScheduleJobServiceImpl     [0;39m [2m:[0;39m ###定时任务初始化启动[sbxxReportTaskService-设备信息上报]
[2m2025-07-28 09:34:23.103[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.s.impl.ScheduleJobServiceImpl     [0;39m [2m:[0;39m ###定时任务初始化启动[ksjhSjdsxfTaskService-ksjhSjdsxf]
[2m2025-07-28 09:34:23.103[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.s.impl.ScheduleJobServiceImpl     [0;39m [2m:[0;39m ###定时任务初始化启动[deviceVersionUpdateTaskService-设备固件版本更新]
[2m2025-07-28 09:34:23.103[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.s.impl.ScheduleJobServiceImpl     [0;39m [2m:[0;39m ###定时任务初始化启动[deviceEventDeleteTaskService-设备事件删除]
[2m2025-07-28 09:34:23.104[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.s.impl.ScheduleJobServiceImpl     [0;39m [2m:[0;39m ###定时任务初始化启动[kssjDsscsjptTaskService-考试数据定时上传上级平台]
[2m2025-07-28 09:34:23.106[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.s.impl.ScheduleJobServiceImpl     [0;39m [2m:[0;39m ###定时任务初始化启动完成
[2m2025-07-28 09:34:24.738[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.s.c.ThreadPoolTaskScheduler         [0;39m [2m:[0;39m Initializing ExecutorService 'taskScheduler'
[2m2025-07-28 09:34:25.089[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36m.FeignHystrixConcurrencyStrategyIntellif[0;39m [2m:[0;39m Current Hystrix plugins configuration is [concurrencyStrategy [com.netflix.hystrix.strategy.concurrency.HystrixConcurrencyStrategyDefault@3898ee5b],eventNotifier [com.netflix.hystrix.strategy.eventnotifier.HystrixEventNotifierDefault@623c43bb],metricPublisher [com.netflix.hystrix.strategy.metrics.HystrixMetricsPublisherDefault@2a8068ad],propertiesStrategy [com.netflix.hystrix.strategy.properties.HystrixPropertiesStrategyDefault@68b512c2],]
[2m2025-07-28 09:34:25.089[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36m.FeignHystrixConcurrencyStrategyIntellif[0;39m [2m:[0;39m Registering Sleuth Hystrix Concurrency Strategy.
[2m2025-07-28 09:34:29.548[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.xcwlkj.standalone.config.MqttConfig [0;39m [2m:[0;39m ######监听MQTT主题[/CENCGW100_S/CENCGW100_SIV1c9f717de4acfc99/function/invoke]
[2m2025-07-28 09:34:29.548[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.xcwlkj.standalone.config.MqttConfig [0;39m [2m:[0;39m ######监听MQTT主题[/CENCGW100_S/CENCGW100_SIV1c9f717de4acfc99/token/get/reply]
[2m2025-07-28 09:34:29.548[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.xcwlkj.standalone.config.MqttConfig [0;39m [2m:[0;39m ######监听MQTT主题[/+/+/event/+]
[2m2025-07-28 09:34:29.575[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.xcwlkj.standalone.config.XxlJobConfig [0;39m [2m:[0;39m >>>>>>>>>>> xxl-job config init.
[2m2025-07-28 09:34:30.757[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.a.f.FreeMarkerAutoConfiguration   [0;39m [2m:[0;39m Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
[2m2025-07-28 09:34:32.055[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.a.e.web.EndpointLinksResolver     [0;39m [2m:[0;39m Exposing 21 endpoint(s) beneath base path '/actuator'
[2m2025-07-28 09:34:33.464[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.i.endpoint.EventDrivenConsumer      [0;39m [2m:[0;39m Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
[2m2025-07-28 09:34:33.465[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.i.channel.PublishSubscribeChannel   [0;39m [2m:[0;39m Channel 'application-1.errorChannel' has 1 subscriber(s).
[2m2025-07-28 09:34:33.465[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.i.endpoint.EventDrivenConsumer      [0;39m [2m:[0;39m started _org.springframework.integration.errorLogger
[2m2025-07-28 09:34:33.465[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.i.endpoint.EventDrivenConsumer      [0;39m [2m:[0;39m Adding {message-handler:mqttConfig.mqttOutbound.serviceActivator} as a subscriber to the 'mqttOutboundChannel' channel
[2m2025-07-28 09:34:33.466[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.integration.channel.ExecutorChannel [0;39m [2m:[0;39m Channel 'application-1.mqttOutboundChannel' has 1 subscriber(s).
[2m2025-07-28 09:34:33.466[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.i.endpoint.EventDrivenConsumer      [0;39m [2m:[0;39m started mqttConfig.mqttOutbound.serviceActivator
[2m2025-07-28 09:34:33.466[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.i.endpoint.EventDrivenConsumer      [0;39m [2m:[0;39m Adding {message-handler:mqttConfig.handler.serviceActivator} as a subscriber to the 'mqttInputChannel' channel
[2m2025-07-28 09:34:33.466[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.integration.channel.ExecutorChannel [0;39m [2m:[0;39m Channel 'application-1.mqttInputChannel' has 1 subscriber(s).
[2m2025-07-28 09:34:33.466[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.i.endpoint.EventDrivenConsumer      [0;39m [2m:[0;39m started mqttConfig.handler.serviceActivator
[2m2025-07-28 09:34:33.466[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mProxyFactoryBean$MethodInvocationGateway[0;39m [2m:[0;39m started mqttGateway
[2m2025-07-28 09:34:33.466[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.i.gateway.GatewayProxyFactoryBean   [0;39m [2m:[0;39m started mqttGateway
[2m2025-07-28 09:34:33.492[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.c.n.eureka.InstanceInfoFactory      [0;39m [2m:[0;39m Setting initial instance status as: STARTING
[2m2025-07-28 09:34:36.278[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.c.n.e.s.EurekaServiceRegistry       [0;39m [2m:[0;39m Registering application EEIP-STANDALONE-SERVICE with eureka with status UP
[2m2025-07-28 09:34:36.672[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36m.m.i.MqttPahoMessageDrivenChannelAdapter[0;39m [2m:[0;39m started inbound
[2m2025-07-28 09:34:36.672[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.s.quartz.SchedulerFactoryBean       [0;39m [2m:[0;39m Starting Quartz Scheduler now
[2m2025-07-28 09:34:36.774[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat started on port(s): 8888 (http) with context path ''
[2m2025-07-28 09:34:36.775[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.c.n.e.s.EurekaAutoServiceRegistration[0;39m [2m:[0;39m Updating port to 8888
[2m2025-07-28 09:34:37.064[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.standalone.StandaloneApplication    [0;39m [2m:[0;39m Started StandaloneApplication in 44.957 seconds (JVM running for 45.74)
[2m2025-07-28 09:34:37.092[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.xcwlkj.util.YmlUtil                 [0;39m [2m:[0;39m 开始从application-alone.yml加载配置信息
[2m2025-07-28 09:34:37.092[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.xcwlkj.util.YmlUtil                 [0;39m [2m:[0;39m 读取外部文件失败D:\code\HS_CUEEIP\trunk\provider/config/application-alone.yml
[2m2025-07-28 09:34:37.096[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.xcwlkj.util.YmlUtil                 [0;39m [2m:[0;39m 开始从application-alone.yml加载配置信息【成功】
[2m2025-07-28 09:34:37.146[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.xcwlkj.cache.AbstractRedisDataCache [0;39m [2m:[0;39m [PkgDataFileServices]cleanup cache finished
[2m2025-07-28 09:34:37.150[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.K.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE ks_kssj_pkg_task SET complete = ? WHERE ( ( complete in ( ? , ? ) ) ) 
[2m2025-07-28 09:34:37.151[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.K.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: -1(Integer), 0(Integer), 1(Integer)
[2m2025-07-28 09:34:37.187[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.K.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-28 09:34:37.187[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.K.clearDoing                    [0;39m [2m:[0;39m ==>  Preparing: UPDATE ks_kssj_pkg_status SET kssjb_qk = case when kssjb_qk = 1 then -10 else kssjb_qk end, jkryjbsjb_qk = case when jkryjbsjb_qk = 1 then -10 else jkryjbsjb_qk end, jkrybpsjb_qk = case when jkrybpsjb_qk = 1 then -10 else jkrybpsjb_qk end, pzsjb_qk = case when pzsjb_qk = 1 then -10 else pzsjb_qk end, kszpsjb_qk = case when kszpsjb_qk = 1 then -10 else kszpsjb_qk end, jkryzpsjb_qk = case when jkryzpsjb_qk = 1 then -10 else jkryzpsjb_qk end 
[2m2025-07-28 09:34:37.188[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.K.clearDoing                    [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-28 09:34:37.201[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.K.clearDoing                    [0;39m [2m:[0;39m <==    Updates: 295
[2m2025-07-28 09:34:37.204[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.config.ZookeeperInitRunner     [0;39m [2m:[0;39m ###SnowflakeIdGenerator，init. workerId=0,datacenterId=0
[2m2025-07-28 09:34:37.204[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.config.ZookeeperInitRunner     [0;39m [2m:[0;39m ###SnowflakeIdGenerator，finish<<<<<<<<<<<<<
[2m2025-07-28 09:34:37.205[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.config.ZookeeperInitRunner     [0;39m [2m:[0;39m ###ZookeeperInitRunner，init. HostAddress=**************:8888, applicationName=eeip-standalone-service
[2m2025-07-28 09:34:37.214[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.c.z.r.z.ZookeeperRegistryCenter     [0;39m [2m:[0;39m Elastic job: zookeeper registry center init, server lists is: **************:2181.
[2m2025-07-28 09:34:37.495[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.config.ZookeeperInitRunner     [0;39m [2m:[0;39m ###ZookeeperInitRunner，finish<<<<<<<<<<<<<
[2m2025-07-28 09:34:37.499[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.config.ZookeeperInitRunner     [0;39m [2m:[0;39m ###SystemCache Init##
[2m2025-07-28 09:34:37.499[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.b.c.BasicinfoBusiCacheInitConfig    [0;39m [2m:[0;39m ####开始缓存商户配置信息####
[2m2025-07-28 09:34:37.499[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.b.c.BasicinfoBusiCacheInitConfig    [0;39m [2m:[0;39m ####缓存商户配置信息缓存成功####
[2m2025-07-28 09:34:37.499[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.config.ZookeeperInitRunner     [0;39m [2m:[0;39m ###SystemCache Succ<<<<<<<<<<<<<##
[2m2025-07-28 09:34:37.499[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.config.ZookeeperInitRunner     [0;39m [2m:[0;39m ###SystemCache Init##
[2m2025-07-28 09:34:37.499[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.xcwlkj.biz.config.BizInitConfig     [0;39m [2m:[0;39m ####开始缓存配置信息####
[2m2025-07-28 09:34:37.499[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.xcwlkj.biz.config.BizInitConfig     [0;39m [2m:[0;39m ####缓存配置信息缓存成功####
[2m2025-07-28 09:34:37.500[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.xcwlkj.biz.config.BizInitConfig     [0;39m [2m:[0;39m ####消息队列初始化####
[2m2025-07-28 09:34:37.500[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.xcwlkj.biz.config.BizInitConfig     [0;39m [2m:[0;39m ####消息队列初始化成功####
[2m2025-07-28 09:34:37.500[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.config.ZookeeperInitRunner     [0;39m [2m:[0;39m ###SystemCache Succ<<<<<<<<<<<<<##
[2m2025-07-28 09:34:37.500[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.config.ZookeeperInitRunner     [0;39m [2m:[0;39m ###SystemCache Init##
[2m2025-07-28 09:34:37.500[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.pubc.config.PubcBusiCacheInitConfig [0;39m [2m:[0;39m ####开始缓存公共服务配置信息####
[2m2025-07-28 09:34:37.500[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.pubc.config.PubcBusiCacheInitConfig [0;39m [2m:[0;39m ####缓存公共服务配置信息缓存成功####
[2m2025-07-28 09:34:37.500[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.config.ZookeeperInitRunner     [0;39m [2m:[0;39m ###SystemCache Succ<<<<<<<<<<<<<##
[2m2025-07-28 09:34:37.500[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.config.ZookeeperInitRunner     [0;39m [2m:[0;39m ###SystemCache Init##
[2m2025-07-28 09:34:37.500[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.e.c.EvaluationBusiCacheInitConfig   [0;39m [2m:[0;39m ####开始缓存听评课配置信息####
[2m2025-07-28 09:34:37.500[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.e.c.EvaluationBusiCacheInitConfig   [0;39m [2m:[0;39m ####缓存听评课配置信息缓存成功####
[2m2025-07-28 09:34:37.501[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.e.c.EvaluationBusiCacheInitConfig   [0;39m [2m:[0;39m ####消息队列初始化####
[2m2025-07-28 09:34:37.501[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.e.c.EvaluationBusiCacheInitConfig   [0;39m [2m:[0;39m ####消息队列初始化成功####
[2m2025-07-28 09:34:37.501[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.config.ZookeeperInitRunner     [0;39m [2m:[0;39m ###SystemCache Succ<<<<<<<<<<<<<##
[2m2025-07-28 09:34:37.501[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.config.ZookeeperInitRunner     [0;39m [2m:[0;39m ###SystemCache Init##
[2m2025-07-28 09:34:37.515[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36m.x.i.c.IdentityverifyBusiCacheInitConfig[0;39m [2m:[0;39m ####开始缓存校级身份核验平台配置信息####
[2m2025-07-28 09:34:38.293[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36m.x.i.c.IdentityverifyBusiCacheInitConfig[0;39m [2m:[0;39m ####服务器序列号为CENCGW100_SIV1c9f717de4acfc99
[2m2025-07-28 09:34:38.293[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36m.x.i.c.IdentityverifyBusiCacheInitConfig[0;39m [2m:[0;39m ####码表数据缓存初始化####
[2m2025-07-28 09:34:38.298[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbPzbMapper.selectByExample     [0;39m [2m:[0;39m ==>  Preparing: SELECT mbywmc , zjzd , pxzd FROM mb_pzb WHERE ( ( sfqy = ? ) ) 
[2m2025-07-28 09:34:38.299[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbPzbMapper.selectByExample     [0;39m [2m:[0;39m ==> Parameters: 1(String)
[2m2025-07-28 09:34:38.306[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbPzbMapper.selectByExample     [0;39m [2m:[0;39m <==      Total: 10
[2m2025-07-28 09:34:38.309[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m ==>  Preparing: SELECT * from gj_mzdmb order by DM asc 
[2m2025-07-28 09:34:38.310[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-28 09:34:38.318[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m <==      Total: 58
[2m2025-07-28 09:34:38.965[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m ==>  Preparing: SELECT * from gj_sfzjdmb order by DM asc 
[2m2025-07-28 09:34:38.965[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-28 09:34:38.971[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m <==      Total: 12
[2m2025-07-28 09:34:39.112[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m ==>  Preparing: SELECT * from jy_kskmdmb order by XM_DM,KM_DM asc 
[2m2025-07-28 09:34:39.112[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-28 09:34:39.121[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m <==      Total: 106
[2m2025-07-28 09:34:40.368[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m ==>  Preparing: SELECT * from jy_rygwlxmb order by DM asc 
[2m2025-07-28 09:34:40.368[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-28 09:34:40.375[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m <==      Total: 3
[2m2025-07-28 09:34:40.416[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m ==>  Preparing: SELECT * from jy_rygwzzmb order by SSGWM,DM asc 
[2m2025-07-28 09:34:40.416[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-28 09:34:40.423[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m <==      Total: 5
[2m2025-07-28 09:34:40.485[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m ==>  Preparing: SELECT * from jy_wjlxmb order by DM asc 
[2m2025-07-28 09:34:40.485[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-28 09:34:40.491[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m <==      Total: 3
[2m2025-07-28 09:34:40.545[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m ==>  Preparing: SELECT * from jy_wjxmmb order by LX_DM,XM_DM asc 
[2m2025-07-28 09:34:40.546[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-28 09:34:40.553[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m <==      Total: 20
[2m2025-07-28 09:34:40.782[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m ==>  Preparing: SELECT * from jy_zcqswzdmb order by DM asc 
[2m2025-07-28 09:34:40.782[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-28 09:34:40.790[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m <==      Total: 4
[2m2025-07-28 09:34:40.842[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m ==>  Preparing: SELECT * from jy_zwbjfsdmb order by DM asc 
[2m2025-07-28 09:34:40.843[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-28 09:34:40.851[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m <==      Total: 8
[2m2025-07-28 09:34:40.946[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m ==>  Preparing: SELECT * from jy_zwplfsdmb order by DM asc 
[2m2025-07-28 09:34:40.947[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-28 09:34:40.954[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbxxsjMapper.queryByTableName   [0;39m [2m:[0;39m <==      Total: 2
[2m2025-07-28 09:34:40.983[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36m.x.i.c.IdentityverifyBusiCacheInitConfig[0;39m [2m:[0;39m ####码表数据缓存初始化成功####
[2m2025-07-28 09:34:40.983[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36m.x.i.c.IdentityverifyBusiCacheInitConfig[0;39m [2m:[0;39m ####延迟队列初始化####
[2m2025-07-28 09:34:40.983[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.s.concurrent.ThreadPoolTaskExecutor [0;39m [2m:[0;39m Initializing ExecutorService
[2m2025-07-28 09:34:40.984[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36m.x.i.c.IdentityverifyBusiCacheInitConfig[0;39m [2m:[0;39m ####延迟队列初始化成功####
[2m2025-07-28 09:34:40.984[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36m.x.i.c.IdentityverifyBusiCacheInitConfig[0;39m [2m:[0;39m ####数据下发任务初始化####
[2m2025-07-28 09:34:40.988[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.K.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE ks_kssj_distribute_task SET complete = ?,complete_time = ? WHERE ( ( complete = ? ) ) 
[2m2025-07-28 09:34:40.989[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.K.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: 2(Integer), 2025-07-28 09:34:40.985(Timestamp), 1(Integer)
[2m2025-07-28 09:34:41.004[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.K.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-28 09:34:41.004[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36m.x.i.c.IdentityverifyBusiCacheInitConfig[0;39m [2m:[0;39m ####数据下发任务初始化成功####
[2m2025-07-28 09:34:41.004[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36m.x.i.c.IdentityverifyBusiCacheInitConfig[0;39m [2m:[0;39m ####数据库变更初始化####
[2m2025-07-28 09:34:41.004[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.service.impl.UtilsServiceImpl     [0;39m [2m:[0;39m dbName:[eeip_alone]
[2m2025-07-28 09:34:41.071[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-28 09:34:41.072[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone(String), ks_ksjh(String), cjlx(String)
[2m2025-07-28 09:34:41.079[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 09:34:41.080[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-28 09:34:41.080[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone(String), ks_ksjh(String), qydsxf(String)
[2m2025-07-28 09:34:41.087[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 09:34:41.087[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-28 09:34:41.087[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone(String), ks_ksjh(String), dsxfsj(String)
[2m2025-07-28 09:34:41.095[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 09:34:41.095[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-28 09:34:41.096[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone(String), ks_ksjh(String), dsxfzt(String)
[2m2025-07-28 09:34:41.103[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 09:34:41.104[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-28 09:34:41.104[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone(String), ks_ksjh(String), pack_jkrybpsj(String)
[2m2025-07-28 09:34:41.111[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 09:34:41.111[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-28 09:34:41.112[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone(String), ks_ksjh(String), pack_jkryjcsj(String)
[2m2025-07-28 09:34:41.119[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 09:34:41.119[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-28 09:34:41.120[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone(String), ks_ksjh(String), jkqdrs(String)
[2m2025-07-28 09:34:41.127[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 09:34:41.127[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-28 09:34:41.127[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone(String), ks_ksjh(String), dbms(String)
[2m2025-07-28 09:34:41.134[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 09:34:41.135[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-28 09:34:41.135[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone(String), ks_jkry_rcxx(String), jklsxh(String)
[2m2025-07-28 09:34:41.143[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 09:34:41.143[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-28 09:34:41.143[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone(String), ks_jkry_rcxx(String), xsd(String)
[2m2025-07-28 09:34:41.183[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 09:34:41.184[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-28 09:34:41.184[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone(String), ks_jkry_rcxx(String), sbzt(String)
[2m2025-07-28 09:34:41.192[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 09:34:41.192[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-28 09:34:41.192[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone(String), ks_jkry_rcxx(String), sbsj(String)
[2m2025-07-28 09:34:41.199[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 09:34:41.200[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-28 09:34:41.200[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone(String), ks_jkry_rcxx(String), tbzt(String)
[2m2025-07-28 09:34:41.207[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 09:34:41.208[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-28 09:34:41.208[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone(String), ks_jkry_rcxx(String), rgyzjg(String)
[2m2025-07-28 09:34:41.214[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 09:34:41.215[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.createCsDzgh   [0;39m [2m:[0;39m ==>  Preparing: CREATE TABLE IF NOT EXISTS `cs_dzgh` ( `id` varchar(32) NOT NULL, `cslx` varchar(32) DEFAULT NULL COMMENT 'KD(考点) KC(考场)', `type` varchar(32) DEFAULT NULL COMMENT '类型KCWG(考场网关)', `classify` varchar(32) DEFAULT NULL COMMENT '分类DEVCFG(设备配置)', `key` varchar(32) DEFAULT NULL COMMENT 'key值（IP:ip地址,NETMASK:掩码,GATEWAY:网关IP地址,IPRANGE:ip范围,PCNUM:主机数）', `value` varchar(128) DEFAULT NULL COMMENT 'value值', `create_time` datetime DEFAULT NULL COMMENT '创建时间', `update_time` datetime DEFAULT NULL COMMENT '更新时间', `status` decimal(10,0) DEFAULT NULL COMMENT '1-启用 0-不启用', `sort` decimal(10,0) DEFAULT NULL COMMENT '排序', `jsh` varchar(32) DEFAULT NULL COMMENT '教室号', PRIMARY KEY (`id`), KEY `index_dzgh_1` (`jsh`,`value`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8 
[2m2025-07-28 09:34:41.216[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.createCsDzgh   [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-28 09:34:41.228[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.createCsDzgh   [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-28 09:34:41.229[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.createSbIpdfp  [0;39m [2m:[0;39m ==>  Preparing: CREATE TABLE IF NOT EXISTS `sb_ipdfp` ( `id` varchar(32) NOT NULL, `ipstart` varchar(32) DEFAULT NULL COMMENT 'IP起', `ipend` varchar(32) DEFAULT NULL COMMENT 'IP尾', `zwym` varchar(32) DEFAULT NULL COMMENT '子网掩码', `wg` varchar(32) DEFAULT NULL COMMENT '网关', `create_time` datetime DEFAULT NULL, `update_time` datetime DEFAULT NULL, PRIMARY KEY (`id`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 
[2m2025-07-28 09:34:41.229[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.createSbIpdfp  [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-28 09:34:41.242[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.createSbIpdfp  [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-28 09:34:41.242[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.createSbSbjcb  [0;39m [2m:[0;39m ==>  Preparing: CREATE TABLE IF NOT EXISTS `sb_sbjcb` ( `id` varchar(32) NOT NULL, `sbxxbh` varchar(32) DEFAULT NULL COMMENT '设备信息编号', `ip` varchar(32) DEFAULT NULL COMMENT 'ip', `xlh` varchar(128) DEFAULT NULL COMMENT '序列号', `detect_type` varchar(10) DEFAULT NULL COMMENT '检测类型1-ping 2-mqtt', `detect_result` varchar(10) DEFAULT NULL COMMENT '检测结果1-成功 -1001-ping失败 -2001-mqtt超时 -9999-其他错误', `detect_desc` varchar(255) DEFAULT NULL COMMENT '检测描述', `detect_exception` text COMMENT '检测异常', `detect_time` timestamp NULL DEFAULT NULL COMMENT '检测时间', PRIMARY KEY (`id`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 
[2m2025-07-28 09:34:41.242[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.createSbSbjcb  [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-28 09:34:41.256[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.createSbSbjcb  [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-28 09:34:41.256[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createKsBmxxKstzz   [0;39m [2m:[0;39m ==>  Preparing: CREATE TABLE IF NOT EXISTS `ks_bmxx_kstzz` ( `ksid` varchar(32) NOT NULL COMMENT '考生唯一标志号', `ksjhbh` varchar(32) NOT NULL COMMENT '考试计划标志号', `ksh` varchar(20) DEFAULT NULL COMMENT '考生号', `sfzjhm` varchar(20) NOT NULL COMMENT '身份证件号', `kszpid` varchar(128) DEFAULT NULL COMMENT '考生照片ID', `tzzzt` varchar(2) DEFAULT NULL COMMENT '0-未获取到特征值；1-已获取到特征值；2-获取特征值失败', `tzzid` varchar(128) DEFAULT NULL COMMENT '特征值ID', `tzzhqsj` datetime DEFAULT NULL COMMENT '特征值获取时间', `create_time` datetime DEFAULT NULL COMMENT '创建时间', `update_time` datetime DEFAULT NULL COMMENT '更新时间', `tzzfs` varchar(1) DEFAULT NULL COMMENT '特征值方式', `bmh` varchar(32) DEFAULT NULL COMMENT '报名号', PRIMARY KEY (`ksid`) USING BTREE ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='考生报名特征值' 
[2m2025-07-28 09:34:41.256[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createKsBmxxKstzz   [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-28 09:34:41.271[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createKsBmxxKstzz   [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-28 09:34:41.272[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createKsYdsbSbkszs  [0;39m [2m:[0;39m ==>  Preparing: CREATE TABLE IF NOT EXISTS `ks_ydsb_sbkszs` ( `id` varchar(32) NOT NULL, `ksjhbh` varchar(32) NOT NULL COMMENT '考试计划编号', `sbxlh` varchar(64) NOT NULL COMMENT '设备序列号', `sbsj` datetime DEFAULT NULL COMMENT '上报时间', `kdmc` varchar(128) DEFAULT NULL COMMENT '考点名称', `sbkszs` int(10) DEFAULT NULL COMMENT '上报考生总数=入场人数+缺考人数', `create_time` datetime DEFAULT NULL COMMENT '创建时间', `update_time` datetime DEFAULT NULL COMMENT '更新时间', `ccm` varchar(10) DEFAULT NULL COMMENT '场次码', `kcbh` varchar(32) DEFAULT NULL COMMENT '考场编号', `ljkcbh` varchar(32) DEFAULT NULL COMMENT '逻辑考场编号', `sblx` varchar(10) DEFAULT NULL COMMENT '上报类型 BZ-标准 FB-非标', PRIMARY KEY (`id`), KEY `ks_ydsb_sbkszs__index_1` (`ksjhbh`,`ccm`,`sbxlh`,`kcbh`,`ljkcbh`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8 
[2m2025-07-28 09:34:41.272[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createKsYdsbSbkszs  [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-28 09:34:41.284[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createKsYdsbSbkszs  [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-28 09:34:41.286[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createBizCsxxExt    [0;39m [2m:[0;39m ==>  Preparing: create table if not exists biz_csxx_ext ( CSBH varchar(64) not null comment '场所编号' primary key, SBXLH varchar(128) null comment '设备序列号', YDZD_APP_VERSION varchar(128) null comment '移动终端app版本', ZHSBSJ datetime null comment '最后上报时间', XFZT int null comment '0-失败 1-下发中 2-成功' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 
[2m2025-07-28 09:34:41.286[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createBizCsxxExt    [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-28 09:34:41.298[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createBizCsxxExt    [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-28 09:34:41.299[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createBizFsgjTask   [0;39m [2m:[0;39m ==>  Preparing: create table if not exists biz_fsgj_task ( ID varchar(32) not null primary key, NAME varchar(128) null comment '任务名称', T_TYPE varchar(10) null comment '任务类型 1-移动终端APP', T_PROGRESS varchar(10) null comment '进度', COMPLETE int(10) null comment '完成状态 -1-失败 0-开始 1-进行中 2-完成', T_PROGRESS_DESC varchar(255) null comment '任务描述', T_PARAM longtext null comment '任务参数' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 
[2m2025-07-28 09:34:41.299[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createBizFsgjTask   [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-28 09:34:41.312[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createBizFsgjTask   [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-28 09:34:41.313[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createBizFsgjTaskJg [0;39m [2m:[0;39m ==>  Preparing: create table if not exists biz_fsgj_task_jg ( ID varchar(32) not null primary key, TASK_ID varchar(32) null comment '任务id', CSBH varchar(64) null comment '场所编号', SBXLH varchar(64) null comment '设备序列号' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 
[2m2025-07-28 09:34:41.313[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createBizFsgjTaskJg [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-28 09:34:41.325[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createBizFsgjTaskJg [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-28 09:34:41.327[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.filterTables   [0;39m [2m:[0;39m ==>  Preparing: select table_name from information_schema.tables where table_schema= ? AND table_name in ( ? , ? ) 
[2m2025-07-28 09:34:41.327[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.filterTables   [0;39m [2m:[0;39m ==> Parameters: eeip_alone(String), biz_send_command(String), biz_send_command_dev(String)
[2m2025-07-28 09:34:41.334[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.filterTables   [0;39m [2m:[0;39m <==      Total: 2
[2m2025-07-28 09:34:41.335[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-28 09:34:41.335[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone(String), sb_sbxx(String), osbb(String)
[2m2025-07-28 09:34:41.342[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 09:34:41.342[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-28 09:34:41.343[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone(String), sb_sbxx(String), ntp_enable(String)
[2m2025-07-28 09:34:41.349[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 09:34:41.349[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-28 09:34:41.350[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone(String), sb_sbxx(String), ntp_source(String)
[2m2025-07-28 09:34:41.357[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 09:34:41.357[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-28 09:34:41.357[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone(String), sb_sbxx(String), ntp_interval(String)
[2m2025-07-28 09:34:41.364[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 09:34:41.365[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.createSbsj     [0;39m [2m:[0;39m ==>  Preparing: create table if not exists sb_sbsj ( id varchar(32) charset utf8 not null primary key, sbxlh varchar(128) charset utf8 not null comment '设备序列号', sblx varchar(32) charset utf8 not null comment 'kdwg考点网关 kcwg考场网关 ydzd移动终端', event_type varchar(16) charset utf8 not null comment 'ONLINE-上线事件 USB-usb事件 WIFI-wifi事件 YDZDBATTERY-移动终端电池事件', event_time datetime not null comment '事件时间', csbh varchar(32) charset utf8 null comment '场所编号', event_detail text charset utf8 null comment '事件详情', create_time datetime null comment '创建时间', update_time datetime null comment '更新时间', status varchar(4) charset utf8 null comment '状态', `repeat` varchar(4) charset utf8 null comment '是否重复', repeat_count int(10) null comment '重复次数 ', `desc` text charset utf8 null comment '描述', param1 varchar(64) charset utf8 null comment '扩展1', param2 varchar(64) charset utf8 null comment '扩展2', param3 varchar(64) charset utf8 null comment '扩展3', event_time_start datetime null comment '事件开始时间', event_time_end datetime null comment '事件结束时间' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='设备事件' 
[2m2025-07-28 09:34:41.365[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.createSbsj     [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-28 09:34:41.377[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.createSbsj     [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-28 09:34:41.379[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-28 09:34:41.379[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone(String), ks_ksrcxx(String), tbzt(String)
[2m2025-07-28 09:34:41.385[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 09:34:41.387[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.alterColumn    [0;39m [2m:[0;39m ==>  Preparing: ALTER TABLE ks_ksrcxx modify COLUMN tbzt varchar(2) COMMENT ?; 
[2m2025-07-28 09:34:41.387[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.alterColumn    [0;39m [2m:[0;39m ==> Parameters: 同步状态(String)
[2m2025-07-28 09:34:41.404[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.alterColumn    [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-28 09:34:41.404[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.service.impl.UtilsServiceImpl     [0;39m [2m:[0;39m 数据库dbName:[eeip_alone] tbName:[ks_ksrcxx] columName:[tbzt]变更
[2m2025-07-28 09:34:41.404[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-28 09:34:41.405[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone(String), ks_ksrcxx(String), xsd(String)
[2m2025-07-28 09:34:41.413[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 09:34:41.413[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-28 09:34:41.413[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone(String), ks_ksrcxx(String), sc_sfzp(String)
[2m2025-07-28 09:34:41.421[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 09:34:41.422[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-28 09:34:41.422[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone(String), ks_ksrcxx(String), sc_rlzp(String)
[2m2025-07-28 09:34:41.429[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 09:34:41.429[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36m.x.i.c.IdentityverifyBusiCacheInitConfig[0;39m [2m:[0;39m ####数据库变更初始化成功####
[2m2025-07-28 09:34:41.429[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36m.x.i.c.IdentityverifyBusiCacheInitConfig[0;39m [2m:[0;39m ####缓存校级身份核验平台配置信息缓存成功####
[2m2025-07-28 09:34:41.429[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.config.ZookeeperInitRunner     [0;39m [2m:[0;39m ###SystemCache Succ<<<<<<<<<<<<<##
[2m2025-07-28 09:34:42.033[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[-**************][0;39m [36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Initializing Servlet 'dispatcherServlet'
[2m2025-07-28 09:34:42.071[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[-**************][0;39m [36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Completed initialization in 37 ms
[2m2025-07-28 09:34:42.279[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[      Thread-63][0;39m [36mc.xcwlkj.msgque.que.XcRocektMqConsumer  [0;39m [2m:[0;39m 监听：JKYT_RSTJ_XXTS,启动成功！
[2m2025-07-28 09:34:42.510[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[-**************][0;39m [36mc.c.c.ConfigServicePropertySourceLocator[0;39m [2m:[0;39m Fetching config from server at : http://localhost:8888
[2m2025-07-28 09:34:42.803[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[-**************][0;39m [36mc.c.c.ConfigServicePropertySourceLocator[0;39m [2m:[0;39m Could not locate PropertySource: label not found
[2m2025-07-28 09:34:45.279[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[      Thread-62][0;39m [36mc.xcwlkj.msgque.que.XcRocektMqConsumer  [0;39m [2m:[0;39m 监听：JKYT_ATTENDANCE_JTXX,启动成功！
[2m2025-07-28 09:34:56.311[0;39m [32m INFO [,7f535ce512050344,7f535ce512050344,false][0;39m [33m[eeip-standalone-service,7f535ce512050344,7f535ce512050344,,false][0;39m [35m15324[0;39m [2m---[0;39m [2m[nio-8888-exec-4][0;39m [36mc.x.i.facade.manager.CxtjController     [0;39m [2m:[0;39m 收到请求开始：[紧急呼叫查询][/manager/identityverify/cxtj/jjhjcx]reqModel:JjhjCxReq[ksjhbh=2507211439231396864138952572928,kcbh=<null>,clzt=<null>,startTime=<null>,endTime=<null>,pageNum=1,pageSize=10,msgId=<null>,transChannel=<null>,channelSerialNo=<null>,channelDate=<null>,channelTime=<null>,token=<null>,loginToken=<null>,transCode=<null>,clientIp=<null>,merchantId=<null>,appPackage=<null>]
[2m2025-07-28 09:34:56.347[0;39m [32m INFO [,7f535ce512050344,7f535ce512050344,false][0;39m [33m[eeip-standalone-service,7f535ce512050344,7f535ce512050344,,false][0;39m [35m15324[0;39m [2m---[0;39m [2m[nio-8888-exec-4][0;39m [36mc.x.i.s.impl.KsKwMessageServiceImpl     [0;39m [2m:[0;39m 紧急呼叫查询开始，查询条件：JjhjCxDTO[ksjhbh=2507211439231396864138952572928,kcbh=<null>,clzt=<null>,startTime=<null>,endTime=<null>,pageNum=1,pageSize=10]
[2m2025-07-28 09:34:56.406[0;39m [32mDEBUG [,7f535ce512050344,7f535ce512050344,false][0;39m [33m[eeip-standalone-service,7f535ce512050344,7f535ce512050344,,false][0;39m [35m15324[0;39m [2m---[0;39m [2m[nio-8888-exec-4][0;39m [36mc.x.i.m.K.selectJjhjList_COUNT          [0;39m [2m:[0;39m ==>  Preparing: SELECT count(0) FROM ks_kw_message msg LEFT JOIN ks_kw_message reply ON msg.ksjhbh = reply.ksjhbh AND msg.id = reply.replay_id AND reply.sczt = '0' WHERE msg.sczt = '0' AND msg.msg_type = '2000' AND msg.origin_app_type = 'HYAPP' AND msg.operate = '0' AND msg.ksjhbh = ? 
[2m2025-07-28 09:34:56.406[0;39m [32mDEBUG [,7f535ce512050344,7f535ce512050344,false][0;39m [33m[eeip-standalone-service,7f535ce512050344,7f535ce512050344,,false][0;39m [35m15324[0;39m [2m---[0;39m [2m[nio-8888-exec-4][0;39m [36mc.x.i.m.K.selectJjhjList_COUNT          [0;39m [2m:[0;39m ==> Parameters: 2507211439231396864138952572928(String)
[2m2025-07-28 09:34:56.416[0;39m [32mDEBUG [,7f535ce512050344,7f535ce512050344,false][0;39m [33m[eeip-standalone-service,7f535ce512050344,7f535ce512050344,,false][0;39m [35m15324[0;39m [2m---[0;39m [2m[nio-8888-exec-4][0;39m [36mc.x.i.m.K.selectJjhjList_COUNT          [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 09:34:56.419[0;39m [32m INFO [,7f535ce512050344,7f535ce512050344,false][0;39m [33m[eeip-standalone-service,7f535ce512050344,7f535ce512050344,,false][0;39m [35m15324[0;39m [2m---[0;39m [2m[nio-8888-exec-4][0;39m [36mc.x.i.s.impl.KsKwMessageServiceImpl     [0;39m [2m:[0;39m 紧急呼叫查询结束，共查询到0条记录
[2m2025-07-28 09:34:56.445[0;39m [32m INFO [,7f535ce512050344,7f535ce512050344,false][0;39m [33m[eeip-standalone-service,7f535ce512050344,7f535ce512050344,,false][0;39m [35m15324[0;39m [2m---[0;39m [2m[nio-8888-exec-4][0;39m [36mc.x.i.facade.manager.CxtjController     [0;39m [2m:[0;39m 处理请求结束：[紧急呼叫查询][/manager/identityverify/cxtj/jjhjcx]reqModel:JjhjCxReq[ksjhbh=2507211439231396864138952572928,kcbh=<null>,clzt=<null>,startTime=<null>,endTime=<null>,pageNum=1,pageSize=10,msgId=<null>,transChannel=<null>,channelSerialNo=<null>,channelDate=<null>,channelTime=<null>,token=<null>,loginToken=<null>,transCode=<null>,clientIp=<null>,merchantId=<null>,appPackage=<null>],respModel:JjhjCxResp[data=Page{count=true, pageNum=1, pageSize=10, startRow=0, endRow=10, total=0, pages=0, reasonable=true, pageSizeZero=false}[],totalRows=0,pageNum=1,pageSize=10,msgId=<null>,transChannel=<null>,channelSerialNo=<null>,transCode=<null>,respDate=<null>,respTime=<null>,ext=<null>]
[2m2025-07-28 09:35:00.011[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-9][0;39m [36mc.x.i.util.schedule.ScheduleJobExecutor [0;39m [2m:[0;39m 任务准备执行，任务ID：24081610292802018092458168076288
[2m2025-07-28 09:35:00.011[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-8][0;39m [36mc.x.i.util.schedule.ScheduleJobExecutor [0;39m [2m:[0;39m 任务准备执行，任务ID：23110110740401808639620095703040
[2m2025-07-28 09:35:00.016[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-9][0;39m [36mc.x.i.m.KsKsjhMapper.selectByExample    [0;39m [2m:[0;39m ==>  Preparing: SELECT ksjhbh,mc,kssj,jssj,xn,xq,sfmr,kszt,sbjm,sbkqsj,sbgbsj,cjlx,kslx,qysbbmd,wifiqy,cjsj,xgsj,scztw,kzqy,qydsxf,dsxfsj,dsxfzt,jkqdrs,dbms FROM ks_ksjh WHERE ( ( scztw = ? and qydsxf = ? and dsxfzt = ? ) ) 
[2m2025-07-28 09:35:00.016[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-9][0;39m [36mc.x.i.m.KsKsjhMapper.selectByExample    [0;39m [2m:[0;39m ==> Parameters: 0(String), 1(Integer), 0(Integer)
[2m2025-07-28 09:35:00.025[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-9][0;39m [36mc.x.i.m.KsKsjhMapper.selectByExample    [0;39m [2m:[0;39m <==      Total: 0
[2m2025-07-28 09:35:00.025[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-9][0;39m [36mc.x.i.s.i.s.KsjhSjdsxfTaskServiceImpl   [0;39m [2m:[0;39m 当前无下发任务！
[2m2025-07-28 09:35:00.025[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-9][0;39m [36mc.x.i.util.schedule.ScheduleJobExecutor [0;39m [2m:[0;39m 任务执行完毕，任务ID：24081610292802018092458168076288  总共耗时：14毫秒
[2m2025-07-28 09:35:00.028[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-9][0;39m [36mc.x.i.m.S.insertSelective               [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO schedule_job_log ( log_id,job_id,job_name,bean_name,params,result,times,create_time ) VALUES( ?,?,?,?,?,?,?,? ) 
[2m2025-07-28 09:35:00.029[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-9][0;39m [36mc.x.i.m.S.insertSelective               [0;39m [2m:[0;39m ==> Parameters: 25072809350002268837445724304385(String), 24081610292802018092458168076288(String), ksjhSjdsxf(String), ksjhSjdsxfTaskService(String), (String), 0(Integer), 14(Integer), 2025-07-28 09:35:00.011(Timestamp)
[2m2025-07-28 09:35:00.044[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-8][0;39m [36mc.x.i.util.schedule.ScheduleJobExecutor [0;39m [2m:[0;39m 任务执行完毕，任务ID：23110110740401808639620095703040  总共耗时：33毫秒
[2m2025-07-28 09:35:00.044[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-8][0;39m [36mc.x.i.m.S.insertSelective               [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO schedule_job_log ( log_id,job_id,job_name,bean_name,result,times,create_time ) VALUES( ?,?,?,?,?,?,? ) 
[2m2025-07-28 09:35:00.049[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-8][0;39m [36mc.x.i.m.S.insertSelective               [0;39m [2m:[0;39m ==> Parameters: 25072809350002268837445724304384(String), 23110110740401808639620095703040(String), jobOnlineStatus(String), onlineStatusTaskService(String), 0(Integer), 33(Integer), 2025-07-28 09:35:00.011(Timestamp)
[2m2025-07-28 09:35:00.425[0;39m [32m INFO [,22e9159078229daf,22e9159078229daf,false][0;39m [33m[eeip-standalone-service,22e9159078229daf,22e9159078229daf,,false][0;39m [35m15324[0;39m [2m---[0;39m [2m[nio-8888-exec-3][0;39m [36mc.x.i.facade.manager.CxtjController     [0;39m [2m:[0;39m 收到请求开始：[紧急呼叫查询][/manager/identityverify/cxtj/jjhjcx]reqModel:JjhjCxReq[ksjhbh=<null>,kcbh=<null>,clzt=<null>,startTime=<null>,endTime=<null>,pageNum=1,pageSize=10,msgId=<null>,transChannel=<null>,channelSerialNo=<null>,channelDate=<null>,channelTime=<null>,token=<null>,loginToken=<null>,transCode=<null>,clientIp=<null>,merchantId=<null>,appPackage=<null>]
[2m2025-07-28 09:35:00.441[0;39m [32m INFO [,22e9159078229daf,22e9159078229daf,false][0;39m [33m[eeip-standalone-service,22e9159078229daf,22e9159078229daf,,false][0;39m [35m15324[0;39m [2m---[0;39m [2m[nio-8888-exec-3][0;39m [36mc.x.i.s.impl.KsKwMessageServiceImpl     [0;39m [2m:[0;39m 紧急呼叫查询开始，查询条件：JjhjCxDTO[ksjhbh=<null>,kcbh=<null>,clzt=<null>,startTime=<null>,endTime=<null>,pageNum=1,pageSize=10]
[2m2025-07-28 09:35:00.449[0;39m [32mDEBUG [,22e9159078229daf,22e9159078229daf,false][0;39m [33m[eeip-standalone-service,22e9159078229daf,22e9159078229daf,,false][0;39m [35m15324[0;39m [2m---[0;39m [2m[nio-8888-exec-3][0;39m [36mc.x.i.m.K.selectJjhjList_COUNT          [0;39m [2m:[0;39m ==>  Preparing: SELECT count(0) FROM ks_kw_message msg LEFT JOIN ks_kw_message reply ON msg.ksjhbh = reply.ksjhbh AND msg.id = reply.replay_id AND reply.sczt = '0' WHERE msg.sczt = '0' AND msg.msg_type = '2000' AND msg.origin_app_type = 'HYAPP' AND msg.operate = '0' 
[2m2025-07-28 09:35:00.449[0;39m [32mDEBUG [,22e9159078229daf,22e9159078229daf,false][0;39m [33m[eeip-standalone-service,22e9159078229daf,22e9159078229daf,,false][0;39m [35m15324[0;39m [2m---[0;39m [2m[nio-8888-exec-3][0;39m [36mc.x.i.m.K.selectJjhjList_COUNT          [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-28 09:35:00.453[0;39m [32mDEBUG [,22e9159078229daf,22e9159078229daf,false][0;39m [33m[eeip-standalone-service,22e9159078229daf,22e9159078229daf,,false][0;39m [35m15324[0;39m [2m---[0;39m [2m[nio-8888-exec-3][0;39m [36mc.x.i.m.K.selectJjhjList_COUNT          [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 09:35:00.458[0;39m [32mDEBUG [,22e9159078229daf,22e9159078229daf,false][0;39m [33m[eeip-standalone-service,22e9159078229daf,22e9159078229daf,,false][0;39m [35m15324[0;39m [2m---[0;39m [2m[nio-8888-exec-3][0;39m [36mc.x.i.m.K.selectJjhjList                [0;39m [2m:[0;39m ==>  Preparing: SELECT msg.id, DATE_FORMAT(msg.send_time, '%Y-%m-%d %H:%i:%s') as fbsj, msg.origin_kcbh as kcbh, msg.comment as jjhjnr, reply.comment as hfnr, DATE_FORMAT(reply.send_time, '%Y-%m-%d %H:%i:%s') as hfsj, CASE WHEN reply.id IS NULL THEN '0' ELSE '1' END as clzt FROM ks_kw_message msg LEFT JOIN ks_kw_message reply ON msg.ksjhbh = reply.ksjhbh AND msg.id = reply.replay_id AND reply.sczt = '0' WHERE msg.sczt = '0' AND msg.msg_type = '2000' AND msg.origin_app_type = 'HYAPP' AND msg.operate = '0' ORDER BY msg.send_time DESC LIMIT ? 
[2m2025-07-28 09:35:00.458[0;39m [32mDEBUG [,22e9159078229daf,22e9159078229daf,false][0;39m [33m[eeip-standalone-service,22e9159078229daf,22e9159078229daf,,false][0;39m [35m15324[0;39m [2m---[0;39m [2m[nio-8888-exec-3][0;39m [36mc.x.i.m.K.selectJjhjList                [0;39m [2m:[0;39m ==> Parameters: 10(Integer)
[2m2025-07-28 09:35:00.470[0;39m [32mDEBUG [,22e9159078229daf,22e9159078229daf,false][0;39m [33m[eeip-standalone-service,22e9159078229daf,22e9159078229daf,,false][0;39m [35m15324[0;39m [2m---[0;39m [2m[nio-8888-exec-3][0;39m [36mc.x.i.m.K.selectJjhjList                [0;39m [2m:[0;39m <==      Total: 10
[2m2025-07-28 09:35:00.470[0;39m [32m INFO [,22e9159078229daf,22e9159078229daf,false][0;39m [33m[eeip-standalone-service,22e9159078229daf,22e9159078229daf,,false][0;39m [35m15324[0;39m [2m---[0;39m [2m[nio-8888-exec-3][0;39m [36mc.x.i.s.impl.KsKwMessageServiceImpl     [0;39m [2m:[0;39m 紧急呼叫查询结束，共查询到53条记录
[2m2025-07-28 09:35:00.498[0;39m [32m INFO [,22e9159078229daf,22e9159078229daf,false][0;39m [33m[eeip-standalone-service,22e9159078229daf,22e9159078229daf,,false][0;39m [35m15324[0;39m [2m---[0;39m [2m[nio-8888-exec-3][0;39m [36mc.x.i.facade.manager.CxtjController     [0;39m [2m:[0;39m 处理请求结束：[紧急呼叫查询][/manager/identityverify/cxtj/jjhjcx]reqModel:JjhjCxReq[ksjhbh=<null>,kcbh=<null>,clzt=<null>,startTime=<null>,endTime=<null>,pageNum=1,pageSize=10,msgId=<null>,transChannel=<null>,channelSerialNo=<null>,channelDate=<null>,channelTime=<null>,token=<null>,loginToken=<null>,transCode=<null>,clientIp=<null>,merchantId=<null>,appPackage=<null>],respModel:JjhjCxResp[data=Page{count=true, pageNum=1, pageSize=10, startRow=0, endRow=10, total=53, pages=6, reasonable=true, pageSizeZero=false}[JjhjCxVO[id=2507071451291391793755551432704,fbsj=2025-07-07 14:51:29,kcbh=003,007,jjhjnr=混合考场情况,hfnr=已收到，正派人处理,hfsj=2025-07-07 14:52:14,clzt=1], JjhjCxVO[id=2507071142321391746204223668224,fbsj=2025-07-07 11:42:32,kcbh=003,007,jjhjnr=电子班牌有误,hfnr=<null>,hfsj=<null>,clzt=0], JjhjCxVO[id=2507062126101391530691837034496,fbsj=2025-07-06 21:26:09,kcbh=003,007,jjhjnr=电子班牌有误,hfnr=已收到，正派人处理,hfsj=2025-07-06 21:27:24,clzt=1], JjhjCxVO[id=2505271725281376974603104026624,fbsj=2025-05-27 17:25:29,kcbh=003,jjhjnr=电子班牌有误,hfnr=收到，设备有问题，可先进行人工核验。保持考场秩序,hfsj=2025-05-27 17:25:43,clzt=1], JjhjCxVO[id=24122709074502114446502591008768,fbsj=2024-12-27 09:07:45,kcbh=001,jjhjnr=电子班牌有误,hfnr=<null>,hfsj=<null>,clzt=0], JjhjCxVO[id=24111914244302087064562724573184,fbsj=2024-11-19 14:24:42,kcbh=003,jjhjnr=混合考场情况,hfnr=<null>,hfsj=<null>,clzt=0], JjhjCxVO[id=24111914244302087064562816978944,fbsj=2024-11-19 14:24:42,kcbh=003,jjhjnr=混合考场情况,hfnr=<null>,hfsj=<null>,clzt=0], JjhjCxVO[id=24111914241302087064306008002560,fbsj=2024-11-19 14:24:12,kcbh=003,jjhjnr=其它,hfnr=<null>,hfsj=<null>,clzt=0], JjhjCxVO[id=24111914241302087064306167517184,fbsj=2024-11-19 14:24:12,kcbh=003,jjhjnr=其它,hfnr=<null>,hfsj=<null>,clzt=0], JjhjCxVO[id=24111914391602087071879906004992,fbsj=2024-11-19 14:10:16,kcbh=003,jjhjnr=其它,hfnr=<null>,hfsj=<null>,clzt=0]],totalRows=53,pageNum=1,pageSize=10,msgId=<null>,transChannel=<null>,channelSerialNo=<null>,transCode=<null>,respDate=<null>,respTime=<null>,ext=<null>]
[2m2025-07-28 09:35:01.463[0;39m [31mERROR [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[tyVerify_test01][0;39m [36mo.s.i.m.outbound.MqttPahoMessageHandler [0;39m [2m:[0;39m Lost connection; will attempt reconnect on next request
[2m2025-07-28 09:35:26.717[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[duleTask-pool-0][0;39m [36mc.x.i.m.A.selectByExample               [0;39m [2m:[0;39m ==>  Preparing: SELECT id,version,created_by,creator_id,created_time,last_operator,last_operator_id,last_operator_time,ref_no,bucket_name,attachment_define_id,original_name,new_file_name,thumb_file_name,path,thumb_path,type,description,expire_time FROM attachment WHERE ( ( expire_time <= ? ) ) 
[2m2025-07-28 09:35:26.719[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[duleTask-pool-0][0;39m [36mc.x.i.m.A.selectByExample               [0;39m [2m:[0;39m ==> Parameters: 2025-07-28 09:35:26(String)
[2m2025-07-28 09:35:26.732[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[duleTask-pool-0][0;39m [36mc.x.i.m.A.selectByExample               [0;39m [2m:[0;39m <==      Total: 0
[2m2025-07-28 09:40:00.004[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[duler_Worker-10][0;39m [36mc.x.i.util.schedule.ScheduleJobExecutor [0;39m [2m:[0;39m 任务准备执行，任务ID：23110110440401808639620095703040
[2m2025-07-28 09:40:00.004[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-1][0;39m [36mc.x.i.util.schedule.ScheduleJobExecutor [0;39m [2m:[0;39m 任务准备执行，任务ID：23110111540401808639620095703040
[2m2025-07-28 09:40:00.005[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-3][0;39m [36mc.x.i.util.schedule.ScheduleJobExecutor [0;39m [2m:[0;39m 任务准备执行，任务ID：24081610292802018092458168076288
[2m2025-07-28 09:40:00.005[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.util.schedule.ScheduleJobExecutor [0;39m [2m:[0;39m 任务准备执行，任务ID：2502130940211339531603881230336
[2m2025-07-28 09:40:00.009[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[duler_Worker-10][0;39m [36mc.x.i.service.impl.SbSbxxServiceImpl    [0;39m [2m:[0;39m =======开始查询和更新设备在线状态=========
[2m2025-07-28 09:40:00.009[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.service.impl.SbSbxxServiceImpl    [0;39m [2m:[0;39m 固件版本更新
[2m2025-07-28 09:40:00.014[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.t.u.h.s.i.UnifyAccessServiceImpl  [0;39m [2m:[0;39m url[http://*************:8844/device/firmware/_query]参数[JetlinksDeviceFirewareReqModel(paging=true, parallelPager=true, pageSize=1, pageIndex=0)]
[2m2025-07-28 09:40:00.017[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-3][0;39m [36mc.x.i.m.KsKsjhMapper.selectByExample    [0;39m [2m:[0;39m ==>  Preparing: SELECT ksjhbh,mc,kssj,jssj,xn,xq,sfmr,kszt,sbjm,sbkqsj,sbgbsj,cjlx,kslx,qysbbmd,wifiqy,cjsj,xgsj,scztw,kzqy,qydsxf,dsxfsj,dsxfzt,jkqdrs,dbms FROM ks_ksjh WHERE ( ( scztw = ? and qydsxf = ? and dsxfzt = ? ) ) 
[2m2025-07-28 09:40:00.017[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-1][0;39m [36mc.x.i.m.SbSbxxMapper.selectByExample    [0;39m [2m:[0;39m ==>  Preparing: SELECT sbxxbh,sbmc,sbxh,sbbm,sbpp,sbcs,sblx,sblxmc,jspc,jssj,sbccrq,sbazrq,sbzbq,sbwldz,macdz,ipdz,dkh,czxt,kzdkh,appbb,firmwarebb,xlh,ramdiskbb,sbzt,jhzt,sbmy,sczt,zxzt,sbzpid,sbqm,sbjcjg,zhzxsj,create_time,update_time,sbztw,sfydsb,osbb,battery,ntp_enable,ntp_source,ntp_interval,ext_status FROM sb_sbxx WHERE ( ( sczt = ? ) ) 
[2m2025-07-28 09:40:00.019[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-1][0;39m [36mc.x.i.m.SbSbxxMapper.selectByExample    [0;39m [2m:[0;39m ==> Parameters: 0(String)
[2m2025-07-28 09:40:00.019[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-3][0;39m [36mc.x.i.m.KsKsjhMapper.selectByExample    [0;39m [2m:[0;39m ==> Parameters: 0(String), 1(Integer), 0(Integer)
[2m2025-07-28 09:40:00.027[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-3][0;39m [36mc.x.i.m.KsKsjhMapper.selectByExample    [0;39m [2m:[0;39m <==      Total: 0
[2m2025-07-28 09:40:00.027[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-3][0;39m [36mc.x.i.s.i.s.KsjhSjdsxfTaskServiceImpl   [0;39m [2m:[0;39m 当前无下发任务！
[2m2025-07-28 09:40:00.027[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[duler_Worker-10][0;39m [36mc.x.i.m.S.selectByExample_COUNT         [0;39m [2m:[0;39m ==>  Preparing: SELECT count(0) FROM sb_sbxx WHERE ((sczt = ? AND sblx IN (?, ?, ?, ?))) 
[2m2025-07-28 09:40:00.027[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-3][0;39m [36mc.x.i.util.schedule.ScheduleJobExecutor [0;39m [2m:[0;39m 任务执行完毕，任务ID：24081610292802018092458168076288  总共耗时：22毫秒
[2m2025-07-28 09:40:00.027[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[duler_Worker-10][0;39m [36mc.x.i.m.S.selectByExample_COUNT         [0;39m [2m:[0;39m ==> Parameters: 0(String), 170(String), 171(String), 172(String), 175(String)
[2m2025-07-28 09:40:00.030[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-1][0;39m [36mc.x.i.m.SbSbxxMapper.selectByExample    [0;39m [2m:[0;39m <==      Total: 16
[2m2025-07-28 09:40:00.030[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-3][0;39m [36mc.x.i.m.S.insertSelective               [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO schedule_job_log ( log_id,job_id,job_name,bean_name,params,result,times,create_time ) VALUES( ?,?,?,?,?,?,?,? ) 
[2m2025-07-28 09:40:00.030[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-3][0;39m [36mc.x.i.m.S.insertSelective               [0;39m [2m:[0;39m ==> Parameters: 25072809400002268839962256372736(String), 24081610292802018092458168076288(String), ksjhSjdsxf(String), ksjhSjdsxfTaskService(String), (String), 0(Integer), 22(Integer), 2025-07-28 09:40:00.005(Timestamp)
[2m2025-07-28 09:40:00.031[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-1][0;39m [36mc.x.i.t.u.h.s.i.UnifyAccessServiceImpl  [0;39m [2m:[0;39m url http://*************:8844/device/category?terms[0].column=parentId&terms[0].value=-5-
[2m2025-07-28 09:40:00.034[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[duler_Worker-10][0;39m [36mc.x.i.m.S.selectByExample_COUNT         [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 09:40:00.035[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[duler_Worker-10][0;39m [36mc.x.i.t.u.h.s.i.UnifyAccessServiceImpl  [0;39m [2m:[0;39m url[http://*************:8844/remote/access/device/listByDeviceNames]参数[DeviceStatusReqModel(deviceNames=[])]
[2m2025-07-28 09:40:00.043[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[duler_Worker-10][0;39m [36mc.x.i.t.u.h.s.i.UnifyAccessServiceImpl  [0;39m [2m:[0;39m 登录参数为空使用默认appId[RDbKcP5Z3mGwxeJB],accessKey[SNTiZ8mHKKhWFhNFApFRPMtk]
[2m2025-07-28 09:40:00.043[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[duler_Worker-10][0;39m [36mc.x.i.t.u.h.s.i.UnifyAccessServiceImpl  [0;39m [2m:[0;39m url[http://*************:8844/remote/access/user/login]参数[PlatLoginReqModel(appId=RDbKcP5Z3mGwxeJB, accessKey=SNTiZ8mHKKhWFhNFApFRPMtk, timestamp=2025-07-28 09:40:00, sign=null)]
[2m2025-07-28 09:40:00.066[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.t.u.h.s.i.UnifyAccessServiceImpl  [0;39m [2m:[0;39m 发送给接入返回结果result=JetlinksWrapper(message=success, status=200, code=200, timestamp=1753666798917, result=JetlinksPageRespModel(pageIndex=0, pageSize=1, total=22, data=[JetlinksDeviceFirewareRespModel(id=00024700113e, deviceName=办公室超脑, productId=HISOME-DX000, version=2025.05.20.1807)]))
[2m2025-07-28 09:40:00.067[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.t.u.h.s.i.UnifyAccessServiceImpl  [0;39m [2m:[0;39m url[http://*************:8844/device/firmware/_query]参数[JetlinksDeviceFirewareReqModel(paging=true, parallelPager=true, pageSize=1000, pageIndex=0)]
[2m2025-07-28 09:40:00.089[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[duler_Worker-10][0;39m [36mc.x.i.t.u.h.s.i.UnifyAccessServiceImpl  [0;39m [2m:[0;39m url[http://*************:8844/remote/access/user/login]响应[<200,Wrapper(code=200, message=success, result=PlatLoginRespModel(token=RDbKcP5Z3mGwxeJB_015eff457c2541d58e24612429ba559f)),[Vary:"Origin", "Access-Control-Request-Method", "Access-Control-Request-Headers", Content-Type:"application/json", Content-Length:"144"]>]
[2m2025-07-28 09:40:00.111[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-1][0;39m [36mc.x.i.t.u.h.s.i.UnifyAccessServiceImpl  [0;39m [2m:[0;39m 发送给接入返回结果result=JetlinksWrapper(message=success, status=200, code=200, timestamp=1753666798960, result=[DeviceCategoryRespModel(id=AIEdgeServer, parentId=-5-, key=AIEdgeServer, name=边缘计算服务器, productId=HISOME-DX000, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1747805356879, productName=汉尚多模态超脑), DeviceCategoryRespModel(id=cheating_prevention, parentId=-5-, key=CheatingPrevention, name=作弊防控设备, productId=HISOME-RRS3000, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1655261812143, productName=3536侦测服务器), DeviceCategoryRespModel(id=digital_hd_matrix, parentId=-5-, key=digital_hd_matrix, name=数字高清矩阵, productId=null, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1692077761801, productName=null), DeviceCategoryRespModel(id=ejjksb, parentId=-5-, key=ejjksb, name=耳机监考设备, productId=CENC_HEADPHONE, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1739411557430, productName=头戴式耳机), DeviceCategoryRespModel(id=hdd_box, parentId=-5-, key=hdd_box, name=视频存储盒子, productId=HISOME-HDD4000, sortIndex=1, level=2, creatorId=1199596756811550720, createTime=1680154724527, productName=视频数据存储终端), DeviceCategoryRespModel(id=hskpjmwxpb, parentId=-5-, key=hskpjmwxpb, name=汉尚考培加密无线平板, productId=null, sortIndex=1, level=1, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1740365256141, productName=null), DeviceCategoryRespModel(id=ipc, parentId=-5-, key=ipc, name=网络摄像机, productId=HISOME-IPC, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1691550353555, productName=IPC通用摄像机), DeviceCategoryRespModel(id=kcdzbp, parentId=-5-, key=kcdzbp, name=考场电子班牌, productId=PROD-DZBP, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1697418220328, productName=电子班牌), DeviceCategoryRespModel(id=kcwg, parentId=-5-, key=kcwg, name=考场网关, productId=CENCGW100_C, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1661844529888, productName=智能教室网关), DeviceCategoryRespModel(id=kdwg, parentId=-5-, key=kdwg, name=考点网关, productId=CENCGW100_S, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1661844519215, productName=智能校级网关), DeviceCategoryRespModel(id=lhsjkydzd, parentId=-5-, key=lhsjkydzd, name=理化生机考移动终端, productId=HISOME-PAD-101, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1740365218610, productName=汉尚理化生机考移动终端), DeviceCategoryRespModel(id=nvr, parentId=-5-, key=nvr, name=数字硬盘录像机, productId=HISOME-NVR4000, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1687656114753, productName=数字硬盘录像机), DeviceCategoryRespModel(id=sip, parentId=-5-, key=sip, name=SIP路由分发服务器, productId=SIP8000, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1689045772782, productName=SIP路由分发服务器), DeviceCategoryRespModel(id=tzsxj, parentId=-5-, key=tzsxj, name=同轴摄像机, productId=null, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1691552091707, productName=null), DeviceCategoryRespModel(id=ydzd, parentId=-5-, key=ydzd, name=移动终端, productId=HISOME_ANDROID, sortIndex=1, level=1, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1661844790208, productName=身份核验移动终端)])
[2m2025-07-28 09:40:00.113[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-1][0;39m [36mc.x.i.service.impl.SbSbxxServiceImpl    [0;39m [2m:[0;39m 
[2m2025-07-28 09:40:00.114[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.t.u.h.s.i.UnifyAccessServiceImpl  [0;39m [2m:[0;39m 发送给接入返回结果result=JetlinksWrapper(message=success, status=200, code=200, timestamp=1753666798963, result=JetlinksPageRespModel(pageIndex=0, pageSize=1000, total=22, data=[JetlinksDeviceFirewareRespModel(id=00024700113e, deviceName=办公室超脑, productId=HISOME-DX000, version=2025.05.20.1807), JetlinksDeviceFirewareRespModel(id=00024720d16a, deviceName=36路办公室超脑, productId=HISOME-DX000, version=2025.05.24.2002), JetlinksDeviceFirewareRespModel(id=AB1602853, deviceName=考场电子班牌, productId=PROD-DZBP, version=1.5.6), JetlinksDeviceFirewareRespModel(id=ATN240303000100YT55YBF, deviceName=ATN240303000100YT55YBF, productId=HISOME_ANDROID, version=MI.T0.LP4X.4G#2.1.3_test3), JetlinksDeviceFirewareRespModel(id=ddcdb3ac5810283f, deviceName=ddcdb3ac5810283f, productId=HISOME_ANDROID, version=MI.T0.LP4X.4G#2.1.8), JetlinksDeviceFirewareRespModel(id=DENCGW829_C04MB10BDJ027590, deviceName=柴行楼教学楼103_DENCGW829_C04MB10BDJ027590, productId=CENCGW100_C, version=2025.01.27), JetlinksDeviceFirewareRespModel(id=DENCGW829_C04MB10BDJ07548e, deviceName=柴行楼柴行楼zq002_DENCGW829_C04MB10BDJ07548e, productId=CENCGW100_C, version=2025.03.18), JetlinksDeviceFirewareRespModel(id=FC1836988, deviceName=DI思源楼1201考务FC1836988, productId=PROD-DZBP, version=1.3.3), JetlinksDeviceFirewareRespModel(id=FC1836989, deviceName=DI思源楼1203考场FC1836989, productId=PROD-DZBP, version=1.5.6), JetlinksDeviceFirewareRespModel(id=G04754401, deviceName=G04754401, productId=PROD-DZBP, version=1.1), JetlinksDeviceFirewareRespModel(id=G04754402, deviceName=G04754402, productId=PROD-DZBP, version=1.1), JetlinksDeviceFirewareRespModel(id=G04754403, deviceName=G04754403, productId=PROD-DZBP, version=1.1), JetlinksDeviceFirewareRespModel(id=G04754404, deviceName=G04754404, productId=PROD-DZBP, version=1.1), JetlinksDeviceFirewareRespModel(id=G04754405, deviceName=G04754405, productId=PROD-DZBP, version=1.1), JetlinksDeviceFirewareRespModel(id=G04754406, deviceName=致远楼致远楼2105_G04754406, productId=PROD-DZBP, version=1.5.6_c), JetlinksDeviceFirewareRespModel(id=G04754407, deviceName=G04754407, productId=PROD-DZBP, version=1.1), JetlinksDeviceFirewareRespModel(id=G04754408, deviceName=G04754408, productId=PROD-DZBP, version=1.1), JetlinksDeviceFirewareRespModel(id=G04754409, deviceName=G04754409, productId=PROD-DZBP, version=1.1), JetlinksDeviceFirewareRespModel(id=G16733827, deviceName=志勤楼教学楼207_G16733827, productId=PROD-DZBP, version=1.1.5), JetlinksDeviceFirewareRespModel(id=G16733836, deviceName=电子班牌测试, productId=PROD-DZBP, version=1.3.3_c), JetlinksDeviceFirewareRespModel(id=KEP20240707001, deviceName=黄河交通学院_身份核验移动终端2, productId=HISOME_ANDROID, version=20241224-1122#2.1.8), JetlinksDeviceFirewareRespModel(id=PID388E2207007544, deviceName=像素设备, productId=PROD-DZBP, version=1.1.6)]))
[2m2025-07-28 09:40:00.119[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) 
[2m2025-07-28 09:40:00.120[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: 2025.05.20.1807(String), 2025-07-28 09:40:00.114(Timestamp), (String), 0(Integer), 0(String), 00024700113e(String)
[2m2025-07-28 09:40:00.120[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-1][0;39m [36mc.x.i.t.u.h.s.i.UnifyAccessServiceImpl  [0;39m [2m:[0;39m Emqx消息桥接查询订阅 url=http://*************:8844/remote/access/emqx/subscribers/list
[2m2025-07-28 09:40:00.133[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-28 09:40:00.134[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) 
[2m2025-07-28 09:40:00.135[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: 2025.05.24.2002(String), 2025-07-28 09:40:00.133(Timestamp), (String), 0(Integer), 0(String), 00024720d16a(String)
[2m2025-07-28 09:40:00.147[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-1][0;39m [36mc.x.i.t.u.h.s.i.UnifyAccessServiceImpl  [0;39m [2m:[0;39m Emqx消息桥接查询订阅 result=Wrapper(code=200, message=success, result=EmqxSubscribersListRespModel(subscribers=[/HISOME_ANDROID/testYdzdXlh001/function/invoke, /CENCGW100_C/DENCGW829_C04MB10BDJ027590/function/invoke, /HISOME_ANDROID/testYdzd004/function/invoke, /PROD-DZBP/test0012/function/invoke, /PROD-DZBP/FC1836988/function/invoke, /HISOME_ANDROID/ddcdb3ac5810283f/function/invoke, /PROD-DZBP/G04754406/properties/read, /PROD-DZBP/FC1836989/function/invoke, /HISOME_ANDROID/KEP20240707001/function/invoke, /PROD-DZBP/AB1602853/function/invoke, /HISOME_ANDROID/testYdzdXlh002/function/invoke, /CENCGW100_S/CENCGW100_SIV1c9f717de4acfc99/function/invoke, /CENCGW100_S/CENCGW100_SIVb33ad3ede4acfc99/function/invoke, /PROD-DZBP/AB1602853/properties/read, /HISOME_ANDROID/ATN240303000100YT55YBF/function/invoke, /PROD-DZBP/G16733836/function/invoke, /HISOME_ANDROID/S30SZA2023140181/function/invoke, /CENCGW100_C/DENCGW829_C04MB10BDJ07548e/function/invoke, /PROD-DZBP/G04754406/function/invoke, /CENCGW100_S/CENCGW100_SIVb770d1a6a1ec5b44/function/invoke]))
[2m2025-07-28 09:40:00.148[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-28 09:40:00.149[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[duler_Worker-10][0;39m [36mc.x.i.t.u.h.s.i.UnifyAccessServiceImpl  [0;39m [2m:[0;39m 发送给接入返回结果result=Wrapper(code=200, message=success, result={deviceList=[{deviceName=00024700113e, status=0}, {deviceName=00024720d16a, status=0}, {deviceName=1231241157, status=0}, {deviceName=AB1602853, status=1}, {deviceName=ATN240303000100YT55YBF, status=0}, {deviceName=CENCGW100_S01MA00BBA000D1000E, status=0}, {deviceName=CENCGW100_SIVb770d1a6a1ec5b44, status=1}, {deviceName=cillum, status=0}, {deviceName=ddcdb3ac5810283f, status=0}, {deviceName=DENCGW829_C04MB10BDJ027590, status=1}, {deviceName=DENCGW829_C04MB10BDJ07548e, status=0}, {deviceName=FC1836988, status=1}, {deviceName=FC1836989, status=1}, {deviceName=G04754406, status=1}, {deviceName=G16733827, status=0}, {deviceName=G16733836, status=1}, {deviceName=ipsumadk, status=0}, {deviceName=KEP20240707001, status=1}, {deviceName=PID388E2207007544, status=0}, {deviceName=S30SZA2023140181, status=0}, {deviceName=test0012, status=0}, {deviceName=test01-xlh, status=0}, {deviceName=test091903, status=0}, {deviceName=testYdzd004, status=0}, {deviceName=testYdzdXlh001, status=0}, {deviceName=testYdzdXlh002, status=0}], totalRows=26})
[2m2025-07-28 09:40:00.150[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) 
[2m2025-07-28 09:40:00.150[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: 1.5.6(String), 2025-07-28 09:40:00.148(Timestamp), (String), 0(Integer), 0(String), AB1602853(String)
[2m2025-07-28 09:40:00.151[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-1][0;39m [36mc.x.i.util.schedule.ScheduleJobExecutor [0;39m [2m:[0;39m 任务执行完毕，任务ID：23110111540401808639620095703040  总共耗时：147毫秒
[2m2025-07-28 09:40:00.151[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-1][0;39m [36mc.x.i.m.S.insertSelective               [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO schedule_job_log ( log_id,job_id,job_name,bean_name,result,times,create_time ) VALUES( ?,?,?,?,?,?,? ) 
[2m2025-07-28 09:40:00.152[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-1][0;39m [36mc.x.i.m.S.insertSelective               [0;39m [2m:[0;39m ==> Parameters: 25072809400002268839962247984129(String), 23110111540401808639620095703040(String), jobSubscribeMqtt(String), subscriberDevInfoToMqttTaskService(String), 0(Integer), 147(Integer), 2025-07-28 09:40:00.004(Timestamp)
[2m2025-07-28 09:40:00.156[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[duler_Worker-10][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE sb_sbxx SET zxzt = ?,zhzxsj = ?,ext_status = ? WHERE ( ( xlh in ( ? , ? , ? , ? , ? , ? , ? , ? ) ) ) 
[2m2025-07-28 09:40:00.157[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[duler_Worker-10][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: 1(String), 2025-07-28 09:40:00.155(Timestamp), 0(Integer), AB1602853(String), CENCGW100_SIVb770d1a6a1ec5b44(String), DENCGW829_C04MB10BDJ027590(String), FC1836988(String), FC1836989(String), G04754406(String), G16733836(String), KEP20240707001(String)
[2m2025-07-28 09:40:00.168[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-28 09:40:00.170[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) 
[2m2025-07-28 09:40:00.170[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: 2.1.3_test3(String), 2025-07-28 09:40:00.168(Timestamp), MI.T0.LP4X.4G(String), 0(Integer), 0(String), ATN240303000100YT55YBF(String)
[2m2025-07-28 09:40:00.173[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[duler_Worker-10][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 40
[2m2025-07-28 09:40:00.174[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[duler_Worker-10][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE sb_sbxx SET zxzt = ?,ext_status = ? WHERE ( ( xlh in ( ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? ) ) ) 
[2m2025-07-28 09:40:00.175[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[duler_Worker-10][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: 0(String), 0(Integer), 00024700113e(String), 00024720d16a(String), 1231241157(String), ATN240303000100YT55YBF(String), CENCGW100_S01MA00BBA000D1000E(String), cillum(String), ddcdb3ac5810283f(String), DENCGW829_C04MB10BDJ07548e(String), G16733827(String), ipsumadk(String), PID388E2207007544(String), S30SZA2023140181(String), test0012(String), test01-xlh(String), test091903(String), testYdzd004(String), testYdzdXlh001(String), testYdzdXlh002(String)
[2m2025-07-28 09:40:00.184[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-28 09:40:00.185[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) 
[2m2025-07-28 09:40:00.185[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: 2.1.8(String), 2025-07-28 09:40:00.184(Timestamp), MI.T0.LP4X.4G(String), 0(Integer), 0(String), ddcdb3ac5810283f(String)
[2m2025-07-28 09:40:00.190[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[duler_Worker-10][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 31
[2m2025-07-28 09:40:00.190[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[duler_Worker-10][0;39m [36mc.x.i.service.impl.SbSbxxServiceImpl    [0;39m [2m:[0;39m getCssbZxzt - 开始获取场所设备在线状态.
[2m2025-07-28 09:40:00.193[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[duler_Worker-10][0;39m [36mc.x.i.m.SbSbxxMapper.getCssbZxzt_COUNT  [0;39m [2m:[0;39m ==>  Preparing: SELECT count(0) FROM sb_sbxx sb LEFT JOIN sb_sbcsgx gx ON sb.sbxxbh = gx.sbbh WHERE sb.sczt = '0' AND sb.sblx = '171' AND gx.csbh IS NOT NULL AND sb.xlh IS NOT NULL 
[2m2025-07-28 09:40:00.193[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[duler_Worker-10][0;39m [36mc.x.i.m.SbSbxxMapper.getCssbZxzt_COUNT  [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-28 09:40:00.199[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-28 09:40:00.199[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[duler_Worker-10][0;39m [36mc.x.i.m.SbSbxxMapper.getCssbZxzt_COUNT  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 09:40:00.200[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[duler_Worker-10][0;39m [36mc.x.i.m.KsKsccMapper.selectByExample    [0;39m [2m:[0;39m ==>  Preparing: SELECT bh,ksjhbh,ccm,kmm,ccmc,kmmc,kssj,jssj,yxcdsj,kssbqksj,ksrcsj,zc,kspch,cjsj,xgsj,xn,xq,scztw FROM ks_kscc WHERE ( ( scztw = ? and kssj <= ? and jssj >= ? ) ) 
[2m2025-07-28 09:40:00.200[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[duler_Worker-10][0;39m [36mc.x.i.m.KsKsccMapper.selectByExample    [0;39m [2m:[0;39m ==> Parameters: 0(String), 2025-07-28 09:40:00.2(Timestamp), 2025-07-28 09:40:00.2(Timestamp)
[2m2025-07-28 09:40:00.202[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) 
[2m2025-07-28 09:40:00.202[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: 2025.01.27(String), 2025-07-28 09:40:00.199(Timestamp), (String), 0(Integer), 0(String), DENCGW829_C04MB10BDJ027590(String)
[2m2025-07-28 09:40:00.207[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[duler_Worker-10][0;39m [36mc.x.i.m.KsKsccMapper.selectByExample    [0;39m [2m:[0;39m <==      Total: 0
[2m2025-07-28 09:40:00.207[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[duler_Worker-10][0;39m [36mc.x.i.service.impl.SbSbxxServiceImpl    [0;39m [2m:[0;39m getCssbZxzt - 获取场所设备在线状态. [OK]
[2m2025-07-28 09:40:00.207[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[duler_Worker-10][0;39m [36mc.x.i.service.impl.SbSbxxServiceImpl    [0;39m [2m:[0;39m ========设备在线状态更新完成=========
[2m2025-07-28 09:40:00.207[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[duler_Worker-10][0;39m [36mc.x.i.util.schedule.ScheduleJobExecutor [0;39m [2m:[0;39m 任务执行完毕，任务ID：23110110440401808639620095703040  总共耗时：203毫秒
[2m2025-07-28 09:40:00.209[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[duler_Worker-10][0;39m [36mc.x.i.m.S.insertSelective               [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO schedule_job_log ( log_id,job_id,job_name,bean_name,result,times,create_time ) VALUES( ?,?,?,?,?,?,? ) 
[2m2025-07-28 09:40:00.209[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[duler_Worker-10][0;39m [36mc.x.i.m.S.insertSelective               [0;39m [2m:[0;39m ==> Parameters: 25072809400002268839962247984128(String), 23110110440401808639620095703040(String), jobSbzxztGx(String), sbzxztCxAndGxTaskService(String), 0(Integer), 203(Integer), 2025-07-28 09:40:00.004(Timestamp)
[2m2025-07-28 09:40:00.216[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-28 09:40:00.218[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) 
[2m2025-07-28 09:40:00.218[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: 2025.03.18(String), 2025-07-28 09:40:00.217(Timestamp), (String), 0(Integer), 0(String), DENCGW829_C04MB10BDJ07548e(String)
[2m2025-07-28 09:40:00.233[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-28 09:40:00.234[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) 
[2m2025-07-28 09:40:00.235[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: 1.3.3(String), 2025-07-28 09:40:00.234(Timestamp), (String), 0(Integer), 0(String), FC1836988(String)
[2m2025-07-28 09:40:00.250[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-28 09:40:00.251[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) 
[2m2025-07-28 09:40:00.251[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: 1.5.6(String), 2025-07-28 09:40:00.25(Timestamp), (String), 0(Integer), 0(String), FC1836989(String)
[2m2025-07-28 09:40:00.266[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-28 09:40:00.268[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) 
[2m2025-07-28 09:40:00.268[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: 1.1(String), 2025-07-28 09:40:00.266(Timestamp), (String), 0(Integer), 0(String), G04754401(String)
[2m2025-07-28 09:40:00.280[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-28 09:40:00.281[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) 
[2m2025-07-28 09:40:00.281[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: 1.1(String), 2025-07-28 09:40:00.28(Timestamp), (String), 0(Integer), 0(String), G04754402(String)
[2m2025-07-28 09:40:00.294[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-28 09:40:00.295[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) 
[2m2025-07-28 09:40:00.296[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: 1.1(String), 2025-07-28 09:40:00.294(Timestamp), (String), 0(Integer), 0(String), G04754403(String)
[2m2025-07-28 09:40:00.308[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-28 09:40:00.309[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) 
[2m2025-07-28 09:40:00.309[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: 1.1(String), 2025-07-28 09:40:00.309(Timestamp), (String), 0(Integer), 0(String), G04754404(String)
[2m2025-07-28 09:40:00.322[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-28 09:40:00.323[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) 
[2m2025-07-28 09:40:00.323[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: 1.1(String), 2025-07-28 09:40:00.322(Timestamp), (String), 0(Integer), 0(String), G04754405(String)
[2m2025-07-28 09:40:00.335[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-28 09:40:00.336[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) 
[2m2025-07-28 09:40:00.337[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: 1.5.6_c(String), 2025-07-28 09:40:00.336(Timestamp), (String), 0(Integer), 0(String), G04754406(String)
[2m2025-07-28 09:40:00.352[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-28 09:40:00.353[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) 
[2m2025-07-28 09:40:00.354[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: 1.1(String), 2025-07-28 09:40:00.352(Timestamp), (String), 0(Integer), 0(String), G04754407(String)
[2m2025-07-28 09:40:00.367[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-28 09:40:00.369[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) 
[2m2025-07-28 09:40:00.369[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: 1.1(String), 2025-07-28 09:40:00.368(Timestamp), (String), 0(Integer), 0(String), G04754408(String)
[2m2025-07-28 09:40:00.382[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-28 09:40:00.383[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) 
[2m2025-07-28 09:40:00.384[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: 1.1(String), 2025-07-28 09:40:00.382(Timestamp), (String), 0(Integer), 0(String), G04754409(String)
[2m2025-07-28 09:40:00.397[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-28 09:40:00.398[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) 
[2m2025-07-28 09:40:00.398[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: 1.1.5(String), 2025-07-28 09:40:00.397(Timestamp), (String), 0(Integer), 0(String), G16733827(String)
[2m2025-07-28 09:40:00.411[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-28 09:40:00.412[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) 
[2m2025-07-28 09:40:00.412[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: 1.3.3_c(String), 2025-07-28 09:40:00.411(Timestamp), (String), 0(Integer), 0(String), G16733836(String)
[2m2025-07-28 09:40:00.427[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-28 09:40:00.429[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) 
[2m2025-07-28 09:40:00.429[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: 2.1.8(String), 2025-07-28 09:40:00.428(Timestamp), 20241224-1122(String), 0(Integer), 0(String), KEP20240707001(String)
[2m2025-07-28 09:40:00.444[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-28 09:40:00.445[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) 
[2m2025-07-28 09:40:00.445[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: 1.1.6(String), 2025-07-28 09:40:00.444(Timestamp), (String), 0(Integer), 0(String), PID388E2207007544(String)
[2m2025-07-28 09:40:00.458[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-28 09:40:00.458[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.service.impl.SbSbxxServiceImpl    [0;39m [2m:[0;39m 固件版本更新10条
[2m2025-07-28 09:40:00.459[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.service.impl.SbSbxxServiceImpl    [0;39m [2m:[0;39m 固件版本更新 OK, 共计更新10条
[2m2025-07-28 09:40:00.459[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.util.schedule.ScheduleJobExecutor [0;39m [2m:[0;39m 任务执行完毕，任务ID：2502130940211339531603881230336  总共耗时：454毫秒
[2m2025-07-28 09:40:00.459[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.m.S.insertSelective               [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO schedule_job_log ( log_id,job_id,job_name,bean_name,params,result,times,create_time ) VALUES( ?,?,?,?,?,?,?,? ) 
[2m2025-07-28 09:40:00.459[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.m.S.insertSelective               [0;39m [2m:[0;39m ==> Parameters: 25072809400002268839962256372737(String), 2502130940211339531603881230336(String), 设备固件版本更新(String), deviceVersionUpdateTaskService(String), (String), 0(Integer), 454(Integer), 2025-07-28 09:40:00.005(Timestamp)
[2m2025-07-28 09:40:49.026[0;39m [32m INFO [,bcf5da029679f519,bcf5da029679f519,false][0;39m [33m[eeip-standalone-service,bcf5da029679f519,bcf5da029679f519,,false][0;39m [35m15324[0;39m [2m---[0;39m [2m[nio-8888-exec-6][0;39m [36mc.x.i.facade.manager.CxtjController     [0;39m [2m:[0;39m 收到请求开始：[紧急呼叫查询][/manager/identityverify/cxtj/jjhjcx]reqModel:JjhjCxReq[ksjhbh=2507211439231396864138952572928,kcbh=<null>,clzt=<null>,startTime=<null>,endTime=<null>,pageNum=1,pageSize=10,msgId=<null>,transChannel=<null>,channelSerialNo=<null>,channelDate=<null>,channelTime=<null>,token=<null>,loginToken=<null>,transCode=<null>,clientIp=<null>,merchantId=<null>,appPackage=<null>]
[2m2025-07-28 09:40:49.039[0;39m [32m INFO [,bcf5da029679f519,bcf5da029679f519,false][0;39m [33m[eeip-standalone-service,bcf5da029679f519,bcf5da029679f519,,false][0;39m [35m15324[0;39m [2m---[0;39m [2m[nio-8888-exec-6][0;39m [36mc.x.i.s.impl.KsKwMessageServiceImpl     [0;39m [2m:[0;39m 紧急呼叫查询开始，查询条件：JjhjCxDTO[ksjhbh=2507211439231396864138952572928,kcbh=<null>,clzt=<null>,startTime=<null>,endTime=<null>,pageNum=1,pageSize=10]
[2m2025-07-28 09:40:49.041[0;39m [32mDEBUG [,bcf5da029679f519,bcf5da029679f519,false][0;39m [33m[eeip-standalone-service,bcf5da029679f519,bcf5da029679f519,,false][0;39m [35m15324[0;39m [2m---[0;39m [2m[nio-8888-exec-6][0;39m [36mc.x.i.m.K.selectJjhjList_COUNT          [0;39m [2m:[0;39m ==>  Preparing: SELECT count(0) FROM ks_kw_message msg LEFT JOIN ks_kw_message reply ON msg.ksjhbh = reply.ksjhbh AND msg.id = reply.replay_id AND reply.sczt = '0' WHERE msg.sczt = '0' AND msg.msg_type = '2000' AND msg.origin_app_type = 'HYAPP' AND msg.operate = '0' AND msg.ksjhbh = ? 
[2m2025-07-28 09:40:49.041[0;39m [32mDEBUG [,bcf5da029679f519,bcf5da029679f519,false][0;39m [33m[eeip-standalone-service,bcf5da029679f519,bcf5da029679f519,,false][0;39m [35m15324[0;39m [2m---[0;39m [2m[nio-8888-exec-6][0;39m [36mc.x.i.m.K.selectJjhjList_COUNT          [0;39m [2m:[0;39m ==> Parameters: 2507211439231396864138952572928(String)
[2m2025-07-28 09:40:49.049[0;39m [32mDEBUG [,bcf5da029679f519,bcf5da029679f519,false][0;39m [33m[eeip-standalone-service,bcf5da029679f519,bcf5da029679f519,,false][0;39m [35m15324[0;39m [2m---[0;39m [2m[nio-8888-exec-6][0;39m [36mc.x.i.m.K.selectJjhjList_COUNT          [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 09:40:49.049[0;39m [32m INFO [,bcf5da029679f519,bcf5da029679f519,false][0;39m [33m[eeip-standalone-service,bcf5da029679f519,bcf5da029679f519,,false][0;39m [35m15324[0;39m [2m---[0;39m [2m[nio-8888-exec-6][0;39m [36mc.x.i.s.impl.KsKwMessageServiceImpl     [0;39m [2m:[0;39m 紧急呼叫查询结束，共查询到0条记录
[2m2025-07-28 09:40:49.073[0;39m [32m INFO [,bcf5da029679f519,bcf5da029679f519,false][0;39m [33m[eeip-standalone-service,bcf5da029679f519,bcf5da029679f519,,false][0;39m [35m15324[0;39m [2m---[0;39m [2m[nio-8888-exec-6][0;39m [36mc.x.i.facade.manager.CxtjController     [0;39m [2m:[0;39m 处理请求结束：[紧急呼叫查询][/manager/identityverify/cxtj/jjhjcx]reqModel:JjhjCxReq[ksjhbh=2507211439231396864138952572928,kcbh=<null>,clzt=<null>,startTime=<null>,endTime=<null>,pageNum=1,pageSize=10,msgId=<null>,transChannel=<null>,channelSerialNo=<null>,channelDate=<null>,channelTime=<null>,token=<null>,loginToken=<null>,transCode=<null>,clientIp=<null>,merchantId=<null>,appPackage=<null>],respModel:JjhjCxResp[data=Page{count=true, pageNum=1, pageSize=10, startRow=0, endRow=10, total=0, pages=0, reasonable=true, pageSizeZero=false}[],totalRows=0,pageNum=1,pageSize=10,msgId=<null>,transChannel=<null>,channelSerialNo=<null>,transCode=<null>,respDate=<null>,respTime=<null>,ext=<null>]
[2m2025-07-28 09:42:00.015[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.util.schedule.ScheduleJobExecutor [0;39m [2m:[0;39m 任务准备执行，任务ID：23110110740401808639620095703040
[2m2025-07-28 09:42:00.023[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.util.schedule.ScheduleJobExecutor [0;39m [2m:[0;39m 任务执行完毕，任务ID：23110110740401808639620095703040  总共耗时：8毫秒
[2m2025-07-28 09:42:00.030[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.m.S.insertSelective               [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO schedule_job_log ( log_id,job_id,job_name,bean_name,result,times,create_time ) VALUES( ?,?,?,?,?,?,? ) 
[2m2025-07-28 09:42:00.030[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.m.S.insertSelective               [0;39m [2m:[0;39m ==> Parameters: 25072809420002268840968973218816(String), 23110110740401808639620095703040(String), jobOnlineStatus(String), onlineStatusTaskService(String), 0(Integer), 8(Integer), 2025-07-28 09:42:00.015(Timestamp)
[2m2025-07-28 09:42:01.466[0;39m [31mERROR [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[tyVerify_test01][0;39m [36mo.s.i.m.outbound.MqttPahoMessageHandler [0;39m [2m:[0;39m Lost connection; will attempt reconnect on next request
[2m2025-07-28 09:45:00.002[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-2][0;39m [36mc.x.i.util.schedule.ScheduleJobExecutor [0;39m [2m:[0;39m 任务准备执行，任务ID：24081610292802018092458168076288
[2m2025-07-28 09:45:00.009[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-2][0;39m [36mc.x.i.m.KsKsjhMapper.selectByExample    [0;39m [2m:[0;39m ==>  Preparing: SELECT ksjhbh,mc,kssj,jssj,xn,xq,sfmr,kszt,sbjm,sbkqsj,sbgbsj,cjlx,kslx,qysbbmd,wifiqy,cjsj,xgsj,scztw,kzqy,qydsxf,dsxfsj,dsxfzt,jkqdrs,dbms FROM ks_ksjh WHERE ( ( scztw = ? and qydsxf = ? and dsxfzt = ? ) ) 
[2m2025-07-28 09:45:00.009[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-2][0;39m [36mc.x.i.m.KsKsjhMapper.selectByExample    [0;39m [2m:[0;39m ==> Parameters: 0(String), 1(Integer), 0(Integer)
[2m2025-07-28 09:45:00.016[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-2][0;39m [36mc.x.i.m.KsKsjhMapper.selectByExample    [0;39m [2m:[0;39m <==      Total: 0
[2m2025-07-28 09:45:00.017[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-2][0;39m [36mc.x.i.s.i.s.KsjhSjdsxfTaskServiceImpl   [0;39m [2m:[0;39m 当前无下发任务！
[2m2025-07-28 09:45:00.017[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-2][0;39m [36mc.x.i.util.schedule.ScheduleJobExecutor [0;39m [2m:[0;39m 任务执行完毕，任务ID：24081610292802018092458168076288  总共耗时：15毫秒
[2m2025-07-28 09:45:00.017[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-2][0;39m [36mc.x.i.m.S.insertSelective               [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO schedule_job_log ( log_id,job_id,job_name,bean_name,params,result,times,create_time ) VALUES( ?,?,?,?,?,?,?,? ) 
[2m2025-07-28 09:45:00.017[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-2][0;39m [36mc.x.i.m.S.insertSelective               [0;39m [2m:[0;39m ==> Parameters: 25072809450002268842478813606912(String), 24081610292802018092458168076288(String), ksjhSjdsxf(String), ksjhSjdsxfTaskService(String), (String), 0(Integer), 15(Integer), 2025-07-28 09:45:00.002(Timestamp)
[2m2025-07-28 09:46:47.797[0;39m [32m INFO [,b976db99a6c599ae,162491289376f0a6,false][0;39m [33m[eeip-standalone-service,b976db99a6c599ae,162491289376f0a6,1b04efd2aa2b9310,false][0;39m [35m15324[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.b.u.m.s.impl.MqttMsgRevHandler      [0;39m [2m:[0;39m GenericMessage [payload={"data":{"OPERATION":"NOTIFY_DEVEVENT_YDZDBATTERY","TIMESTAMP":1753667205596,"Data":{"devType":"172","sbcsbh":"K3307820091100001","eventTime":"1753667205596","batteryVal":100,"eventType":"YDZDBATTERY","sn":"KEP20240707001"}},"sign":"","messageId":"batteryVal_1753667205596_KEP20240707001","timestamp":1753667205596,"token":"ee98d86d3ccc48a2a366770e8ffb7567"}, headers={mqtt_receivedRetained=false, spanTraceId=b976db99a6c599ae, spanId=b976db99a6c599ae, nativeHeaders={spanTraceId=[b976db99a6c599ae], spanId=[b976db99a6c599ae], spanSampled=[0]}, mqtt_duplicate=false, id=b23c7ee1-0533-bfac-18f1-a02d386b0134, spanSampled=0, mqtt_receivedTopic=/HISOME_ANDROID/KEP20240707001/event/REPORT_DEVEVENT, mqtt_receivedQos=1}]
[2m2025-07-28 09:46:47.804[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.r.s.impl.BasicMqttMsgRevHandler     [0;39m [2m:[0;39m topic[/HISOME_ANDROID/KEP20240707001/event/REPORT_DEVEVENT] 没有方法可以处理这个主题...
[2m2025-07-28 09:46:47.804[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[lTaskExecutor-3][0;39m [36mc.x.a.service.impl.BizMqttMsgRevHandler [0;39m [2m:[0;39m topic[/HISOME_ANDROID/KEP20240707001/event/REPORT_DEVEVENT] 没有方法可以处理这个主题...
[2m2025-07-28 09:46:47.805[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m judgeDeviceIdExist:KEP20240707001
[2m2025-07-28 09:46:47.809[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mx.i.s.i.m.MqttReportDeveventEvantHandler[0;39m [2m:[0;39m 处理mqtt上报设备事件消息:BaseOperationReq(operation=NOTIFY_DEVEVENT_YDZDBATTERY, timestamp=1753667205596, data={"devType":"172","sbcsbh":"K3307820091100001","eventTime":"1753667205596","batteryVal":100,"eventType":"YDZDBATTERY","sn":"KEP20240707001"})
[2m2025-07-28 09:46:47.827[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.i.m.S.updateZxztAndNtpByXlh         [0;39m [2m:[0;39m ==>  Preparing: update sb_sbxx SET zxzt = ?, battery = ?, update_time = now() where xlh = ? 
[2m2025-07-28 09:46:47.829[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.i.m.S.updateZxztAndNtpByXlh         [0;39m [2m:[0;39m ==> Parameters: 1(String), 100(String), KEP20240707001(String)
[2m2025-07-28 09:46:47.844[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.i.m.S.updateZxztAndNtpByXlh         [0;39m [2m:[0;39m <==    Updates: 6
[2m2025-07-28 09:46:47.845[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.i.m.SbSbxxMapper.selectByExample    [0;39m [2m:[0;39m ==>  Preparing: SELECT sbxxbh,sbmc,sbxh,sbbm,sbpp,sbcs,sblx,sblxmc,jspc,jssj,sbccrq,sbazrq,sbzbq,sbwldz,macdz,ipdz,dkh,czxt,kzdkh,appbb,firmwarebb,xlh,ramdiskbb,sbzt,jhzt,sbmy,sczt,zxzt,sbzpid,sbqm,sbjcjg,zhzxsj,create_time,update_time,sbztw,sfydsb,osbb,battery,ntp_enable,ntp_source,ntp_interval,ext_status FROM sb_sbxx WHERE ( ( sczt = ? and xlh = ? ) ) 
[2m2025-07-28 09:46:47.845[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.i.m.SbSbxxMapper.selectByExample    [0;39m [2m:[0;39m ==> Parameters: 0(String), KEP20240707001(String)
[2m2025-07-28 09:46:47.853[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mc.x.i.m.SbSbxxMapper.selectByExample    [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 09:46:47.853[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[lTaskExecutor-1][0;39m [36mx.i.s.i.m.MqttReportDeveventEvantHandler[0;39m [2m:[0;39m 上报电量大于阈值, 不记录日志
[2m2025-07-28 09:49:00.013[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-6][0;39m [36mc.x.i.util.schedule.ScheduleJobExecutor [0;39m [2m:[0;39m 任务准备执行，任务ID：23110110740401808639620095703040
[2m2025-07-28 09:49:00.019[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-6][0;39m [36mc.x.i.util.schedule.ScheduleJobExecutor [0;39m [2m:[0;39m 任务执行完毕，任务ID：23110110740401808639620095703040  总共耗时：6毫秒
[2m2025-07-28 09:49:00.029[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-6][0;39m [36mc.x.i.m.S.insertSelective               [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO schedule_job_log ( log_id,job_id,job_name,bean_name,result,times,create_time ) VALUES( ?,?,?,?,?,?,? ) 
[2m2025-07-28 09:49:00.029[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-6][0;39m [36mc.x.i.m.S.insertSelective               [0;39m [2m:[0;39m ==> Parameters: 25072809490002268844492171801600(String), 23110110740401808639620095703040(String), jobOnlineStatus(String), onlineStatusTaskService(String), 0(Integer), 6(Integer), 2025-07-28 09:49:00.013(Timestamp)
[2m2025-07-28 09:49:01.471[0;39m [31mERROR [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[tyVerify_test01][0;39m [36mo.s.i.m.outbound.MqttPahoMessageHandler [0;39m [2m:[0;39m Lost connection; will attempt reconnect on next request
[2m2025-07-28 09:50:00.006[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-4][0;39m [36mc.x.i.util.schedule.ScheduleJobExecutor [0;39m [2m:[0;39m 任务准备执行，任务ID：23110111540401808639620095703040
[2m2025-07-28 09:50:00.006[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-9][0;39m [36mc.x.i.util.schedule.ScheduleJobExecutor [0;39m [2m:[0;39m 任务准备执行，任务ID：24081610292802018092458168076288
[2m2025-07-28 09:50:00.006[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-4][0;39m [36mc.x.i.m.SbSbxxMapper.selectByExample    [0;39m [2m:[0;39m ==>  Preparing: SELECT sbxxbh,sbmc,sbxh,sbbm,sbpp,sbcs,sblx,sblxmc,jspc,jssj,sbccrq,sbazrq,sbzbq,sbwldz,macdz,ipdz,dkh,czxt,kzdkh,appbb,firmwarebb,xlh,ramdiskbb,sbzt,jhzt,sbmy,sczt,zxzt,sbzpid,sbqm,sbjcjg,zhzxsj,create_time,update_time,sbztw,sfydsb,osbb,battery,ntp_enable,ntp_source,ntp_interval,ext_status FROM sb_sbxx WHERE ( ( sczt = ? ) ) 
[2m2025-07-28 09:50:00.006[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-4][0;39m [36mc.x.i.m.SbSbxxMapper.selectByExample    [0;39m [2m:[0;39m ==> Parameters: 0(String)
[2m2025-07-28 09:50:00.015[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-9][0;39m [36mc.x.i.m.KsKsjhMapper.selectByExample    [0;39m [2m:[0;39m ==>  Preparing: SELECT ksjhbh,mc,kssj,jssj,xn,xq,sfmr,kszt,sbjm,sbkqsj,sbgbsj,cjlx,kslx,qysbbmd,wifiqy,cjsj,xgsj,scztw,kzqy,qydsxf,dsxfsj,dsxfzt,jkqdrs,dbms FROM ks_ksjh WHERE ( ( scztw = ? and qydsxf = ? and dsxfzt = ? ) ) 
[2m2025-07-28 09:50:00.015[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-9][0;39m [36mc.x.i.m.KsKsjhMapper.selectByExample    [0;39m [2m:[0;39m ==> Parameters: 0(String), 1(Integer), 0(Integer)
[2m2025-07-28 09:50:00.019[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-4][0;39m [36mc.x.i.m.SbSbxxMapper.selectByExample    [0;39m [2m:[0;39m <==      Total: 16
[2m2025-07-28 09:50:00.019[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-4][0;39m [36mc.x.i.t.u.h.s.i.UnifyAccessServiceImpl  [0;39m [2m:[0;39m url http://*************:8844/device/category?terms[0].column=parentId&terms[0].value=-5-
[2m2025-07-28 09:50:00.022[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-9][0;39m [36mc.x.i.m.KsKsjhMapper.selectByExample    [0;39m [2m:[0;39m <==      Total: 0
[2m2025-07-28 09:50:00.022[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-9][0;39m [36mc.x.i.s.i.s.KsjhSjdsxfTaskServiceImpl   [0;39m [2m:[0;39m 当前无下发任务！
[2m2025-07-28 09:50:00.022[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-9][0;39m [36mc.x.i.util.schedule.ScheduleJobExecutor [0;39m [2m:[0;39m 任务执行完毕，任务ID：24081610292802018092458168076288  总共耗时：16毫秒
[2m2025-07-28 09:50:00.022[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-9][0;39m [36mc.x.i.m.S.insertSelective               [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO schedule_job_log ( log_id,job_id,job_name,bean_name,params,result,times,create_time ) VALUES( ?,?,?,?,?,?,?,? ) 
[2m2025-07-28 09:50:00.022[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-9][0;39m [36mc.x.i.m.S.insertSelective               [0;39m [2m:[0;39m ==> Parameters: 25072809500002268844995429561345(String), 24081610292802018092458168076288(String), ksjhSjdsxf(String), ksjhSjdsxfTaskService(String), (String), 0(Integer), 16(Integer), 2025-07-28 09:50:00.006(Timestamp)
[2m2025-07-28 09:50:00.102[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-4][0;39m [36mc.x.i.t.u.h.s.i.UnifyAccessServiceImpl  [0;39m [2m:[0;39m 发送给接入返回结果result=JetlinksWrapper(message=success, status=200, code=200, timestamp=1753667398942, result=[DeviceCategoryRespModel(id=AIEdgeServer, parentId=-5-, key=AIEdgeServer, name=边缘计算服务器, productId=HISOME-DX000, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1747805356879, productName=汉尚多模态超脑), DeviceCategoryRespModel(id=cheating_prevention, parentId=-5-, key=CheatingPrevention, name=作弊防控设备, productId=HISOME-RRS3000, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1655261812143, productName=3536侦测服务器), DeviceCategoryRespModel(id=digital_hd_matrix, parentId=-5-, key=digital_hd_matrix, name=数字高清矩阵, productId=null, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1692077761801, productName=null), DeviceCategoryRespModel(id=ejjksb, parentId=-5-, key=ejjksb, name=耳机监考设备, productId=CENC_HEADPHONE, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1739411557430, productName=头戴式耳机), DeviceCategoryRespModel(id=hdd_box, parentId=-5-, key=hdd_box, name=视频存储盒子, productId=HISOME-HDD4000, sortIndex=1, level=2, creatorId=1199596756811550720, createTime=1680154724527, productName=视频数据存储终端), DeviceCategoryRespModel(id=hskpjmwxpb, parentId=-5-, key=hskpjmwxpb, name=汉尚考培加密无线平板, productId=null, sortIndex=1, level=1, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1740365256141, productName=null), DeviceCategoryRespModel(id=ipc, parentId=-5-, key=ipc, name=网络摄像机, productId=HISOME-IPC, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1691550353555, productName=IPC通用摄像机), DeviceCategoryRespModel(id=kcdzbp, parentId=-5-, key=kcdzbp, name=考场电子班牌, productId=PROD-DZBP, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1697418220328, productName=电子班牌), DeviceCategoryRespModel(id=kcwg, parentId=-5-, key=kcwg, name=考场网关, productId=CENCGW100_C, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1661844529888, productName=智能教室网关), DeviceCategoryRespModel(id=kdwg, parentId=-5-, key=kdwg, name=考点网关, productId=CENCGW100_S, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1661844519215, productName=智能校级网关), DeviceCategoryRespModel(id=lhsjkydzd, parentId=-5-, key=lhsjkydzd, name=理化生机考移动终端, productId=HISOME-PAD-101, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1740365218610, productName=汉尚理化生机考移动终端), DeviceCategoryRespModel(id=nvr, parentId=-5-, key=nvr, name=数字硬盘录像机, productId=HISOME-NVR4000, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1687656114753, productName=数字硬盘录像机), DeviceCategoryRespModel(id=sip, parentId=-5-, key=sip, name=SIP路由分发服务器, productId=SIP8000, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1689045772782, productName=SIP路由分发服务器), DeviceCategoryRespModel(id=tzsxj, parentId=-5-, key=tzsxj, name=同轴摄像机, productId=null, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1691552091707, productName=null), DeviceCategoryRespModel(id=ydzd, parentId=-5-, key=ydzd, name=移动终端, productId=HISOME_ANDROID, sortIndex=1, level=1, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1661844790208, productName=身份核验移动终端)])
[2m2025-07-28 09:50:00.103[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-4][0;39m [36mc.x.i.service.impl.SbSbxxServiceImpl    [0;39m [2m:[0;39m 
[2m2025-07-28 09:50:00.109[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-4][0;39m [36mc.x.i.t.u.h.s.i.UnifyAccessServiceImpl  [0;39m [2m:[0;39m Emqx消息桥接查询订阅 url=http://*************:8844/remote/access/emqx/subscribers/list
[2m2025-07-28 09:50:00.135[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-4][0;39m [36mc.x.i.t.u.h.s.i.UnifyAccessServiceImpl  [0;39m [2m:[0;39m Emqx消息桥接查询订阅 result=Wrapper(code=200, message=success, result=EmqxSubscribersListRespModel(subscribers=[/HISOME_ANDROID/testYdzdXlh001/function/invoke, /CENCGW100_C/DENCGW829_C04MB10BDJ027590/function/invoke, /HISOME_ANDROID/testYdzd004/function/invoke, /PROD-DZBP/test0012/function/invoke, /PROD-DZBP/FC1836988/function/invoke, /HISOME_ANDROID/ddcdb3ac5810283f/function/invoke, /PROD-DZBP/G04754406/properties/read, /PROD-DZBP/FC1836989/function/invoke, /HISOME_ANDROID/KEP20240707001/function/invoke, /PROD-DZBP/AB1602853/function/invoke, /HISOME_ANDROID/testYdzdXlh002/function/invoke, /CENCGW100_S/CENCGW100_SIV1c9f717de4acfc99/function/invoke, /CENCGW100_S/CENCGW100_SIVb33ad3ede4acfc99/function/invoke, /PROD-DZBP/AB1602853/properties/read, /HISOME_ANDROID/ATN240303000100YT55YBF/function/invoke, /PROD-DZBP/G16733836/function/invoke, /HISOME_ANDROID/S30SZA2023140181/function/invoke, /CENCGW100_C/DENCGW829_C04MB10BDJ07548e/function/invoke, /PROD-DZBP/G04754406/function/invoke, /CENCGW100_S/CENCGW100_SIVb770d1a6a1ec5b44/function/invoke]))
[2m2025-07-28 09:50:00.135[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-4][0;39m [36mc.x.i.util.schedule.ScheduleJobExecutor [0;39m [2m:[0;39m 任务执行完毕，任务ID：23110111540401808639620095703040  总共耗时：129毫秒
[2m2025-07-28 09:50:00.135[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-4][0;39m [36mc.x.i.m.S.insertSelective               [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO schedule_job_log ( log_id,job_id,job_name,bean_name,result,times,create_time ) VALUES( ?,?,?,?,?,?,? ) 
[2m2025-07-28 09:50:00.135[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-4][0;39m [36mc.x.i.m.S.insertSelective               [0;39m [2m:[0;39m ==> Parameters: 25072809500002268844995429561344(String), 23110111540401808639620095703040(String), jobSubscribeMqtt(String), subscriberDevInfoToMqttTaskService(String), 0(Integer), 129(Integer), 2025-07-28 09:50:00.006(Timestamp)
[2m2025-07-28 09:54:49.464[0;39m [32m INFO [,e16a279223e6fe57,e16a279223e6fe57,false][0;39m [33m[eeip-standalone-service,e16a279223e6fe57,e16a279223e6fe57,,false][0;39m [35m15324[0;39m [2m---[0;39m [2m[nio-8888-exec-9][0;39m [36mc.x.i.facade.manager.CxtjController     [0;39m [2m:[0;39m 收到请求开始：[紧急呼叫查询][/manager/identityverify/cxtj/jjhjcx]reqModel:JjhjCxReq[ksjhbh=2507071431551391788832445169664,kcbh=<null>,clzt=<null>,startTime=<null>,endTime=<null>,pageNum=1,pageSize=10,msgId=<null>,transChannel=<null>,channelSerialNo=<null>,channelDate=<null>,channelTime=<null>,token=<null>,loginToken=<null>,transCode=<null>,clientIp=<null>,merchantId=<null>,appPackage=<null>]
[2m2025-07-28 09:54:49.485[0;39m [32m INFO [,e16a279223e6fe57,e16a279223e6fe57,false][0;39m [33m[eeip-standalone-service,e16a279223e6fe57,e16a279223e6fe57,,false][0;39m [35m15324[0;39m [2m---[0;39m [2m[nio-8888-exec-9][0;39m [36mc.x.i.s.impl.KsKwMessageServiceImpl     [0;39m [2m:[0;39m 紧急呼叫查询开始，查询条件：JjhjCxDTO[ksjhbh=2507071431551391788832445169664,kcbh=<null>,clzt=<null>,startTime=<null>,endTime=<null>,pageNum=1,pageSize=10]
[2m2025-07-28 09:54:49.489[0;39m [32mDEBUG [,e16a279223e6fe57,e16a279223e6fe57,false][0;39m [33m[eeip-standalone-service,e16a279223e6fe57,e16a279223e6fe57,,false][0;39m [35m15324[0;39m [2m---[0;39m [2m[nio-8888-exec-9][0;39m [36mc.x.i.m.K.selectJjhjList_COUNT          [0;39m [2m:[0;39m ==>  Preparing: SELECT count(0) FROM ks_kw_message msg LEFT JOIN ks_kw_message reply ON msg.ksjhbh = reply.ksjhbh AND msg.id = reply.replay_id AND reply.sczt = '0' WHERE msg.sczt = '0' AND msg.msg_type = '2000' AND msg.origin_app_type = 'HYAPP' AND msg.operate = '0' AND msg.ksjhbh = ? 
[2m2025-07-28 09:54:49.489[0;39m [32mDEBUG [,e16a279223e6fe57,e16a279223e6fe57,false][0;39m [33m[eeip-standalone-service,e16a279223e6fe57,e16a279223e6fe57,,false][0;39m [35m15324[0;39m [2m---[0;39m [2m[nio-8888-exec-9][0;39m [36mc.x.i.m.K.selectJjhjList_COUNT          [0;39m [2m:[0;39m ==> Parameters: 2507071431551391788832445169664(String)
[2m2025-07-28 09:54:49.497[0;39m [32mDEBUG [,e16a279223e6fe57,e16a279223e6fe57,false][0;39m [33m[eeip-standalone-service,e16a279223e6fe57,e16a279223e6fe57,,false][0;39m [35m15324[0;39m [2m---[0;39m [2m[nio-8888-exec-9][0;39m [36mc.x.i.m.K.selectJjhjList_COUNT          [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 09:54:49.498[0;39m [32mDEBUG [,e16a279223e6fe57,e16a279223e6fe57,false][0;39m [33m[eeip-standalone-service,e16a279223e6fe57,e16a279223e6fe57,,false][0;39m [35m15324[0;39m [2m---[0;39m [2m[nio-8888-exec-9][0;39m [36mc.x.i.m.K.selectJjhjList                [0;39m [2m:[0;39m ==>  Preparing: SELECT msg.id, DATE_FORMAT(msg.send_time, '%Y-%m-%d %H:%i:%s') as fbsj, msg.origin_kcbh as kcbh, msg.comment as jjhjnr, reply.comment as hfnr, DATE_FORMAT(reply.send_time, '%Y-%m-%d %H:%i:%s') as hfsj, CASE WHEN reply.id IS NULL THEN '0' ELSE '1' END as clzt FROM ks_kw_message msg LEFT JOIN ks_kw_message reply ON msg.ksjhbh = reply.ksjhbh AND msg.id = reply.replay_id AND reply.sczt = '0' WHERE msg.sczt = '0' AND msg.msg_type = '2000' AND msg.origin_app_type = 'HYAPP' AND msg.operate = '0' AND msg.ksjhbh = ? ORDER BY msg.send_time DESC LIMIT ? 
[2m2025-07-28 09:54:49.498[0;39m [32mDEBUG [,e16a279223e6fe57,e16a279223e6fe57,false][0;39m [33m[eeip-standalone-service,e16a279223e6fe57,e16a279223e6fe57,,false][0;39m [35m15324[0;39m [2m---[0;39m [2m[nio-8888-exec-9][0;39m [36mc.x.i.m.K.selectJjhjList                [0;39m [2m:[0;39m ==> Parameters: 2507071431551391788832445169664(String), 10(Integer)
[2m2025-07-28 09:54:49.506[0;39m [32mDEBUG [,e16a279223e6fe57,e16a279223e6fe57,false][0;39m [33m[eeip-standalone-service,e16a279223e6fe57,e16a279223e6fe57,,false][0;39m [35m15324[0;39m [2m---[0;39m [2m[nio-8888-exec-9][0;39m [36mc.x.i.m.K.selectJjhjList                [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 09:54:49.506[0;39m [32m INFO [,e16a279223e6fe57,e16a279223e6fe57,false][0;39m [33m[eeip-standalone-service,e16a279223e6fe57,e16a279223e6fe57,,false][0;39m [35m15324[0;39m [2m---[0;39m [2m[nio-8888-exec-9][0;39m [36mc.x.i.s.impl.KsKwMessageServiceImpl     [0;39m [2m:[0;39m 紧急呼叫查询结束，共查询到1条记录
[2m2025-07-28 09:54:49.531[0;39m [32m INFO [,e16a279223e6fe57,e16a279223e6fe57,false][0;39m [33m[eeip-standalone-service,e16a279223e6fe57,e16a279223e6fe57,,false][0;39m [35m15324[0;39m [2m---[0;39m [2m[nio-8888-exec-9][0;39m [36mc.x.i.facade.manager.CxtjController     [0;39m [2m:[0;39m 处理请求结束：[紧急呼叫查询][/manager/identityverify/cxtj/jjhjcx]reqModel:JjhjCxReq[ksjhbh=2507071431551391788832445169664,kcbh=<null>,clzt=<null>,startTime=<null>,endTime=<null>,pageNum=1,pageSize=10,msgId=<null>,transChannel=<null>,channelSerialNo=<null>,channelDate=<null>,channelTime=<null>,token=<null>,loginToken=<null>,transCode=<null>,clientIp=<null>,merchantId=<null>,appPackage=<null>],respModel:JjhjCxResp[data=Page{count=true, pageNum=1, pageSize=10, startRow=0, endRow=10, total=1, pages=1, reasonable=true, pageSizeZero=false}[JjhjCxVO[id=2507071451291391793755551432704,fbsj=2025-07-07 14:51:29,kcbh=003,007,jjhjnr=混合考场情况,hfnr=已收到，正派人处理,hfsj=2025-07-07 14:52:14,clzt=1]],totalRows=1,pageNum=1,pageSize=10,msgId=<null>,transChannel=<null>,channelSerialNo=<null>,transCode=<null>,respDate=<null>,respTime=<null>,ext=<null>]
[2m2025-07-28 09:55:00.004[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-8][0;39m [36mc.x.i.util.schedule.ScheduleJobExecutor [0;39m [2m:[0;39m 任务准备执行，任务ID：24081610292802018092458168076288
[2m2025-07-28 09:55:00.005[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-8][0;39m [36mc.x.i.m.KsKsjhMapper.selectByExample    [0;39m [2m:[0;39m ==>  Preparing: SELECT ksjhbh,mc,kssj,jssj,xn,xq,sfmr,kszt,sbjm,sbkqsj,sbgbsj,cjlx,kslx,qysbbmd,wifiqy,cjsj,xgsj,scztw,kzqy,qydsxf,dsxfsj,dsxfzt,jkqdrs,dbms FROM ks_ksjh WHERE ( ( scztw = ? and qydsxf = ? and dsxfzt = ? ) ) 
[2m2025-07-28 09:55:00.005[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-8][0;39m [36mc.x.i.m.KsKsjhMapper.selectByExample    [0;39m [2m:[0;39m ==> Parameters: 0(String), 1(Integer), 0(Integer)
[2m2025-07-28 09:55:00.009[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-8][0;39m [36mc.x.i.m.KsKsjhMapper.selectByExample    [0;39m [2m:[0;39m <==      Total: 0
[2m2025-07-28 09:55:00.009[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-8][0;39m [36mc.x.i.s.i.s.KsjhSjdsxfTaskServiceImpl   [0;39m [2m:[0;39m 当前无下发任务！
[2m2025-07-28 09:55:00.009[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-8][0;39m [36mc.x.i.util.schedule.ScheduleJobExecutor [0;39m [2m:[0;39m 任务执行完毕，任务ID：24081610292802018092458168076288  总共耗时：5毫秒
[2m2025-07-28 09:55:00.009[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-8][0;39m [36mc.x.i.m.S.insertSelective               [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO schedule_job_log ( log_id,job_id,job_name,bean_name,params,result,times,create_time ) VALUES( ?,?,?,?,?,?,?,? ) 
[2m2025-07-28 09:55:00.009[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-8][0;39m [36mc.x.i.m.S.insertSelective               [0;39m [2m:[0;39m ==> Parameters: 25072809550002268847511995184128(String), 24081610292802018092458168076288(String), ksjhSjdsxf(String), ksjhSjdsxfTaskService(String), (String), 0(Integer), 5(Integer), 2025-07-28 09:55:00.004(Timestamp)
[2m2025-07-28 09:56:00.015[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-3][0;39m [36mc.x.i.util.schedule.ScheduleJobExecutor [0;39m [2m:[0;39m 任务准备执行，任务ID：23110110740401808639620095703040
[2m2025-07-28 09:56:00.019[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-3][0;39m [36mc.x.i.util.schedule.ScheduleJobExecutor [0;39m [2m:[0;39m 任务执行完毕，任务ID：23110110740401808639620095703040  总共耗时：4毫秒
[2m2025-07-28 09:56:00.019[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-3][0;39m [36mc.x.i.m.S.insertSelective               [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO schedule_job_log ( log_id,job_id,job_name,bean_name,result,times,create_time ) VALUES( ?,?,?,?,?,?,? ) 
[2m2025-07-28 09:56:00.024[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-3][0;39m [36mc.x.i.m.S.insertSelective               [0;39m [2m:[0;39m ==> Parameters: 25072809560002268848015395550208(String), 23110110740401808639620095703040(String), jobOnlineStatus(String), onlineStatusTaskService(String), 0(Integer), 4(Integer), 2025-07-28 09:56:00.014(Timestamp)
[2m2025-07-28 09:56:01.469[0;39m [31mERROR [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[tyVerify_test01][0;39m [36mo.s.i.m.outbound.MqttPahoMessageHandler [0;39m [2m:[0;39m Lost connection; will attempt reconnect on next request
[2m2025-07-28 10:00:00.007[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-1][0;39m [36mc.x.i.util.schedule.ScheduleJobExecutor [0;39m [2m:[0;39m 任务准备执行，任务ID：23110110440401808639620095703040
[2m2025-07-28 10:00:00.007[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[duler_Worker-10][0;39m [36mc.x.i.util.schedule.ScheduleJobExecutor [0;39m [2m:[0;39m 任务准备执行，任务ID：23110110740401808639620095703040
[2m2025-07-28 10:00:00.007[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-1][0;39m [36mc.x.i.service.impl.SbSbxxServiceImpl    [0;39m [2m:[0;39m =======开始查询和更新设备在线状态=========
[2m2025-07-28 10:00:00.009[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.util.schedule.ScheduleJobExecutor [0;39m [2m:[0;39m 任务准备执行，任务ID：23110111540401808639620095703040
[2m2025-07-28 10:00:00.009[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.util.schedule.ScheduleJobExecutor [0;39m [2m:[0;39m 任务准备执行，任务ID：23112811035901828218591246338048
[2m2025-07-28 10:00:00.009[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-2][0;39m [36mc.x.i.util.schedule.ScheduleJobExecutor [0;39m [2m:[0;39m 任务准备执行，任务ID：24081610292802018092458168076288
[2m2025-07-28 10:00:00.010[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-6][0;39m [36mc.x.i.util.schedule.ScheduleJobExecutor [0;39m [2m:[0;39m 任务准备执行，任务ID：2502130940211339531603881230336
[2m2025-07-28 10:00:00.010[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-6][0;39m [36mc.x.i.service.impl.SbSbxxServiceImpl    [0;39m [2m:[0;39m 固件版本更新
[2m2025-07-28 10:00:00.010[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-6][0;39m [36mc.x.i.t.u.h.s.i.UnifyAccessServiceImpl  [0;39m [2m:[0;39m url[http://*************:8844/device/firmware/_query]参数[JetlinksDeviceFirewareReqModel(paging=true, parallelPager=true, pageSize=1, pageIndex=0)]
[2m2025-07-28 10:00:00.016[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-1][0;39m [36mc.x.i.m.S.selectByExample_COUNT         [0;39m [2m:[0;39m ==>  Preparing: SELECT count(0) FROM sb_sbxx WHERE ((sczt = ? AND sblx IN (?, ?, ?, ?))) 
[2m2025-07-28 10:00:00.016[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.m.SbSbxxMapper.selectByExample    [0;39m [2m:[0;39m ==>  Preparing: SELECT sbxxbh,sbmc,sbxh,sbbm,sbpp,sbcs,sblx,sblxmc,jspc,jssj,sbccrq,sbazrq,sbzbq,sbwldz,macdz,ipdz,dkh,czxt,kzdkh,appbb,firmwarebb,xlh,ramdiskbb,sbzt,jhzt,sbmy,sczt,zxzt,sbzpid,sbqm,sbjcjg,zhzxsj,create_time,update_time,sbztw,sfydsb,osbb,battery,ntp_enable,ntp_source,ntp_interval,ext_status FROM sb_sbxx WHERE ( ( sczt = ? ) ) 
[2m2025-07-28 10:00:00.016[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.m.J.selectOneByExample            [0;39m [2m:[0;39m ==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) 
[2m2025-07-28 10:00:00.016[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.m.J.selectOneByExample            [0;39m [2m:[0;39m ==> Parameters: HISOME_subDevType(String)
[2m2025-07-28 10:00:00.017[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.m.SbSbxxMapper.selectByExample    [0;39m [2m:[0;39m ==> Parameters: 0(String)
[2m2025-07-28 10:00:00.017[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-1][0;39m [36mc.x.i.m.S.selectByExample_COUNT         [0;39m [2m:[0;39m ==> Parameters: 0(String), 170(String), 171(String), 172(String), 175(String)
[2m2025-07-28 10:00:00.023[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.m.J.selectOneByExample            [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 10:00:00.024[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-2][0;39m [36mc.x.i.m.KsKsjhMapper.selectByExample    [0;39m [2m:[0;39m ==>  Preparing: SELECT ksjhbh,mc,kssj,jssj,xn,xq,sfmr,kszt,sbjm,sbkqsj,sbgbsj,cjlx,kslx,qysbbmd,wifiqy,cjsj,xgsj,scztw,kzqy,qydsxf,dsxfsj,dsxfzt,jkqdrs,dbms FROM ks_ksjh WHERE ( ( scztw = ? and qydsxf = ? and dsxfzt = ? ) ) 
[2m2025-07-28 10:00:00.024[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-1][0;39m [36mc.x.i.m.S.selectByExample_COUNT         [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 10:00:00.024[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-2][0;39m [36mc.x.i.m.KsKsjhMapper.selectByExample    [0;39m [2m:[0;39m ==> Parameters: 0(String), 1(Integer), 0(Integer)
[2m2025-07-28 10:00:00.024[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-1][0;39m [36mc.x.i.t.u.h.s.i.UnifyAccessServiceImpl  [0;39m [2m:[0;39m url[http://*************:8844/remote/access/device/listByDeviceNames]参数[DeviceStatusReqModel(deviceNames=[])]
[2m2025-07-28 10:00:00.025[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.m.SbSbxxMapper.selectByExample    [0;39m [2m:[0;39m <==      Total: 16
[2m2025-07-28 10:00:00.027[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.t.u.h.s.i.UnifyAccessServiceImpl  [0;39m [2m:[0;39m url http://*************:8844/device/category?terms[0].column=parentId&terms[0].value=-5-
[2m2025-07-28 10:00:00.027[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.m.S.selectBySubStatusAndType      [0;39m [2m:[0;39m ==>  Preparing: SELECT xlh FROM sb_sbxx WHERE sczt = '0' AND sbztw = '0' AND sblx IN ( ? , ? ) 
[2m2025-07-28 10:00:00.028[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.m.S.selectBySubStatusAndType      [0;39m [2m:[0;39m ==> Parameters: kcdzbp(String), ydzd(String)
[2m2025-07-28 10:00:00.031[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-2][0;39m [36mc.x.i.m.KsKsjhMapper.selectByExample    [0;39m [2m:[0;39m <==      Total: 0
[2m2025-07-28 10:00:00.031[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-2][0;39m [36mc.x.i.s.i.s.KsjhSjdsxfTaskServiceImpl   [0;39m [2m:[0;39m 当前无下发任务！
[2m2025-07-28 10:00:00.031[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[duler_Worker-10][0;39m [36mc.x.i.util.schedule.ScheduleJobExecutor [0;39m [2m:[0;39m 任务执行完毕，任务ID：23110110740401808639620095703040  总共耗时：24毫秒
[2m2025-07-28 10:00:00.031[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-2][0;39m [36mc.x.i.util.schedule.ScheduleJobExecutor [0;39m [2m:[0;39m 任务执行完毕，任务ID：24081610292802018092458168076288  总共耗时：22毫秒
[2m2025-07-28 10:00:00.032[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-2][0;39m [36mc.x.i.m.S.insertSelective               [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO schedule_job_log ( log_id,job_id,job_name,bean_name,params,result,times,create_time ) VALUES( ?,?,?,?,?,?,?,? ) 
[2m2025-07-28 10:00:00.032[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[duler_Worker-10][0;39m [36mc.x.i.m.S.insertSelective               [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO schedule_job_log ( log_id,job_id,job_name,bean_name,result,times,create_time ) VALUES( ?,?,?,?,?,?,? ) 
[2m2025-07-28 10:00:00.033[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-2][0;39m [36mc.x.i.m.S.insertSelective               [0;39m [2m:[0;39m ==> Parameters: 25072810000002268850028619527170(String), 24081610292802018092458168076288(String), ksjhSjdsxf(String), ksjhSjdsxfTaskService(String), (String), 0(Integer), 22(Integer), 2025-07-28 10:00:00.009(Timestamp)
[2m2025-07-28 10:00:00.033[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[duler_Worker-10][0;39m [36mc.x.i.m.S.insertSelective               [0;39m [2m:[0;39m ==> Parameters: 25072810000002268850028602749953(String), 23110110740401808639620095703040(String), jobOnlineStatus(String), onlineStatusTaskService(String), 0(Integer), 24(Integer), 2025-07-28 10:00:00.007(Timestamp)
[2m2025-07-28 10:00:00.034[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.m.S.selectBySubStatusAndType      [0;39m [2m:[0;39m <==      Total: 3
[2m2025-07-28 10:00:00.034[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.s.i.s.SbxxReportTaskServiceImpl   [0;39m [2m:[0;39m 设备信息上报，上报类型：[kcdzbp, ydzd]，上报设备序列号：[testYdzdXlh001, testYdzdXlh002, testYdzd004]
[2m2025-07-28 10:00:00.036[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.m.S.getDevicePlaceRelation        [0;39m [2m:[0;39m ==>  Preparing: SELECT sb_sbxx.xlh AS sbxlh, sb_sbxx.sbmc AS sbmc, sb_sbxx.sblx AS sblb, sb_sbxx.sbzbq AS sbzbjzsj, sb_sbxx.jssj AS cgsj, sb_sbxx.sbcs AS sbcsm, sb_sbxx.sbpp AS sbpp, sb_sbxx.sbzt AS ywzt, sb_sbxx.ipdz AS sbip, sb_sbxx.sfydsb AS sfydsb, csgx.csbh AS csdm, sb_sbxx.sbmy AS sbmy FROM sb_sbxx INNER JOIN sb_sbcsgx csgx ON sb_sbxx.sbxxbh = csgx.sbbh WHERE sb_sbxx.sczt = '0' AND sb_sbxx.sbzt != '3' AND sb_sbxx.xlh IN ( ? , ? , ? ) 
[2m2025-07-28 10:00:00.036[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.m.S.getDevicePlaceRelation        [0;39m [2m:[0;39m ==> Parameters: testYdzdXlh001(String), testYdzdXlh002(String), testYdzd004(String)
[2m2025-07-28 10:00:00.043[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.m.S.getDevicePlaceRelation        [0;39m [2m:[0;39m <==      Total: 0
[2m2025-07-28 10:00:00.044[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.m.CsXxjbxxMapper.selectByExample  [0;39m [2m:[0;39m ==>  Preparing: SELECT xxdm,xxmc,zzjgm,xxdz,xzqhm FROM cs_xxjbxx 
[2m2025-07-28 10:00:00.044[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.m.CsXxjbxxMapper.selectByExample  [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-28 10:00:00.051[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.m.CsXxjbxxMapper.selectByExample  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 10:00:00.051[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.service.impl.SbSbxxServiceImpl    [0;39m [2m:[0;39m 没有需要上报的设备
[2m2025-07-28 10:00:00.051[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.util.schedule.ScheduleJobExecutor [0;39m [2m:[0;39m 任务执行完毕，任务ID：23112811035901828218591246338048  总共耗时：42毫秒
[2m2025-07-28 10:00:00.053[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.m.S.insertSelective               [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO schedule_job_log ( log_id,job_id,job_name,bean_name,params,result,times,create_time ) VALUES( ?,?,?,?,?,?,?,? ) 
[2m2025-07-28 10:00:00.053[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-6][0;39m [36mc.x.i.t.u.h.s.i.UnifyAccessServiceImpl  [0;39m [2m:[0;39m 发送给接入返回结果result=JetlinksWrapper(message=success, status=200, code=200, timestamp=1753667998900, result=JetlinksPageRespModel(pageIndex=0, pageSize=1, total=22, data=[JetlinksDeviceFirewareRespModel(id=00024700113e, deviceName=办公室超脑, productId=HISOME-DX000, version=2025.05.20.1807)]))
[2m2025-07-28 10:00:00.053[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-5][0;39m [36mc.x.i.m.S.insertSelective               [0;39m [2m:[0;39m ==> Parameters: 25072810000002268850028619527169(String), 23112811035901828218591246338048(String), 设备信息上报(String), sbxxReportTaskService(String), (String), 0(Integer), 42(Integer), 2025-07-28 10:00:00.009(Timestamp)
[2m2025-07-28 10:00:00.053[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-6][0;39m [36mc.x.i.t.u.h.s.i.UnifyAccessServiceImpl  [0;39m [2m:[0;39m url[http://*************:8844/device/firmware/_query]参数[JetlinksDeviceFirewareReqModel(paging=true, parallelPager=true, pageSize=1000, pageIndex=0)]
[2m2025-07-28 10:00:00.086[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-1][0;39m [36mc.x.i.t.u.h.s.i.UnifyAccessServiceImpl  [0;39m [2m:[0;39m 发送给接入返回结果result=Wrapper(code=200, message=success, result={deviceList=[{deviceName=00024700113e, status=0}, {deviceName=00024720d16a, status=0}, {deviceName=1231241157, status=0}, {deviceName=AB1602853, status=1}, {deviceName=ATN240303000100YT55YBF, status=0}, {deviceName=CENCGW100_S01MA00BBA000D1000E, status=0}, {deviceName=CENCGW100_SIVb770d1a6a1ec5b44, status=1}, {deviceName=cillum, status=0}, {deviceName=ddcdb3ac5810283f, status=0}, {deviceName=DENCGW829_C04MB10BDJ027590, status=1}, {deviceName=DENCGW829_C04MB10BDJ07548e, status=0}, {deviceName=FC1836988, status=1}, {deviceName=FC1836989, status=1}, {deviceName=G04754406, status=1}, {deviceName=G16733827, status=0}, {deviceName=G16733836, status=1}, {deviceName=ipsumadk, status=0}, {deviceName=KEP20240707001, status=1}, {deviceName=PID388E2207007544, status=0}, {deviceName=S30SZA2023140181, status=0}, {deviceName=test0012, status=0}, {deviceName=test01-xlh, status=0}, {deviceName=test091903, status=0}, {deviceName=testYdzd004, status=0}, {deviceName=testYdzdXlh001, status=0}, {deviceName=testYdzdXlh002, status=0}], totalRows=26})
[2m2025-07-28 10:00:00.088[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-1][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE sb_sbxx SET zxzt = ?,zhzxsj = ?,ext_status = ? WHERE ( ( xlh in ( ? , ? , ? , ? , ? , ? , ? , ? ) ) ) 
[2m2025-07-28 10:00:00.088[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-1][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: 1(String), 2025-07-28 10:00:00.087(Timestamp), 0(Integer), AB1602853(String), CENCGW100_SIVb770d1a6a1ec5b44(String), DENCGW829_C04MB10BDJ027590(String), FC1836988(String), FC1836989(String), G04754406(String), G16733836(String), KEP20240707001(String)
[2m2025-07-28 10:00:00.099[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.t.u.h.s.i.UnifyAccessServiceImpl  [0;39m [2m:[0;39m 发送给接入返回结果result=JetlinksWrapper(message=success, status=200, code=200, timestamp=1753667998939, result=[DeviceCategoryRespModel(id=AIEdgeServer, parentId=-5-, key=AIEdgeServer, name=边缘计算服务器, productId=HISOME-DX000, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1747805356879, productName=汉尚多模态超脑), DeviceCategoryRespModel(id=cheating_prevention, parentId=-5-, key=CheatingPrevention, name=作弊防控设备, productId=HISOME-RRS3000, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1655261812143, productName=3536侦测服务器), DeviceCategoryRespModel(id=digital_hd_matrix, parentId=-5-, key=digital_hd_matrix, name=数字高清矩阵, productId=null, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1692077761801, productName=null), DeviceCategoryRespModel(id=ejjksb, parentId=-5-, key=ejjksb, name=耳机监考设备, productId=CENC_HEADPHONE, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1739411557430, productName=头戴式耳机), DeviceCategoryRespModel(id=hdd_box, parentId=-5-, key=hdd_box, name=视频存储盒子, productId=HISOME-HDD4000, sortIndex=1, level=2, creatorId=1199596756811550720, createTime=1680154724527, productName=视频数据存储终端), DeviceCategoryRespModel(id=hskpjmwxpb, parentId=-5-, key=hskpjmwxpb, name=汉尚考培加密无线平板, productId=null, sortIndex=1, level=1, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1740365256141, productName=null), DeviceCategoryRespModel(id=ipc, parentId=-5-, key=ipc, name=网络摄像机, productId=HISOME-IPC, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1691550353555, productName=IPC通用摄像机), DeviceCategoryRespModel(id=kcdzbp, parentId=-5-, key=kcdzbp, name=考场电子班牌, productId=PROD-DZBP, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1697418220328, productName=电子班牌), DeviceCategoryRespModel(id=kcwg, parentId=-5-, key=kcwg, name=考场网关, productId=CENCGW100_C, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1661844529888, productName=智能教室网关), DeviceCategoryRespModel(id=kdwg, parentId=-5-, key=kdwg, name=考点网关, productId=CENCGW100_S, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1661844519215, productName=智能校级网关), DeviceCategoryRespModel(id=lhsjkydzd, parentId=-5-, key=lhsjkydzd, name=理化生机考移动终端, productId=HISOME-PAD-101, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1740365218610, productName=汉尚理化生机考移动终端), DeviceCategoryRespModel(id=nvr, parentId=-5-, key=nvr, name=数字硬盘录像机, productId=HISOME-NVR4000, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1687656114753, productName=数字硬盘录像机), DeviceCategoryRespModel(id=sip, parentId=-5-, key=sip, name=SIP路由分发服务器, productId=SIP8000, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1689045772782, productName=SIP路由分发服务器), DeviceCategoryRespModel(id=tzsxj, parentId=-5-, key=tzsxj, name=同轴摄像机, productId=null, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1691552091707, productName=null), DeviceCategoryRespModel(id=ydzd, parentId=-5-, key=ydzd, name=移动终端, productId=HISOME_ANDROID, sortIndex=1, level=1, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1661844790208, productName=身份核验移动终端)])
[2m2025-07-28 10:00:00.099[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.service.impl.SbSbxxServiceImpl    [0;39m [2m:[0;39m 
[2m2025-07-28 10:00:00.100[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-6][0;39m [36mc.x.i.t.u.h.s.i.UnifyAccessServiceImpl  [0;39m [2m:[0;39m 发送给接入返回结果result=JetlinksWrapper(message=success, status=200, code=200, timestamp=1753667998943, result=JetlinksPageRespModel(pageIndex=0, pageSize=1000, total=22, data=[JetlinksDeviceFirewareRespModel(id=00024700113e, deviceName=办公室超脑, productId=HISOME-DX000, version=2025.05.20.1807), JetlinksDeviceFirewareRespModel(id=00024720d16a, deviceName=36路办公室超脑, productId=HISOME-DX000, version=2025.05.24.2002), JetlinksDeviceFirewareRespModel(id=AB1602853, deviceName=考场电子班牌, productId=PROD-DZBP, version=1.5.6), JetlinksDeviceFirewareRespModel(id=ATN240303000100YT55YBF, deviceName=ATN240303000100YT55YBF, productId=HISOME_ANDROID, version=MI.T0.LP4X.4G#2.1.3_test3), JetlinksDeviceFirewareRespModel(id=ddcdb3ac5810283f, deviceName=ddcdb3ac5810283f, productId=HISOME_ANDROID, version=MI.T0.LP4X.4G#2.1.8), JetlinksDeviceFirewareRespModel(id=DENCGW829_C04MB10BDJ027590, deviceName=柴行楼教学楼103_DENCGW829_C04MB10BDJ027590, productId=CENCGW100_C, version=2025.01.27), JetlinksDeviceFirewareRespModel(id=DENCGW829_C04MB10BDJ07548e, deviceName=柴行楼柴行楼zq002_DENCGW829_C04MB10BDJ07548e, productId=CENCGW100_C, version=2025.03.18), JetlinksDeviceFirewareRespModel(id=FC1836988, deviceName=DI思源楼1201考务FC1836988, productId=PROD-DZBP, version=1.3.3), JetlinksDeviceFirewareRespModel(id=FC1836989, deviceName=DI思源楼1203考场FC1836989, productId=PROD-DZBP, version=1.5.6), JetlinksDeviceFirewareRespModel(id=G04754401, deviceName=G04754401, productId=PROD-DZBP, version=1.1), JetlinksDeviceFirewareRespModel(id=G04754402, deviceName=G04754402, productId=PROD-DZBP, version=1.1), JetlinksDeviceFirewareRespModel(id=G04754403, deviceName=G04754403, productId=PROD-DZBP, version=1.1), JetlinksDeviceFirewareRespModel(id=G04754404, deviceName=G04754404, productId=PROD-DZBP, version=1.1), JetlinksDeviceFirewareRespModel(id=G04754405, deviceName=G04754405, productId=PROD-DZBP, version=1.1), JetlinksDeviceFirewareRespModel(id=G04754406, deviceName=致远楼致远楼2105_G04754406, productId=PROD-DZBP, version=1.5.6_c), JetlinksDeviceFirewareRespModel(id=G04754407, deviceName=G04754407, productId=PROD-DZBP, version=1.1), JetlinksDeviceFirewareRespModel(id=G04754408, deviceName=G04754408, productId=PROD-DZBP, version=1.1), JetlinksDeviceFirewareRespModel(id=G04754409, deviceName=G04754409, productId=PROD-DZBP, version=1.1), JetlinksDeviceFirewareRespModel(id=G16733827, deviceName=志勤楼教学楼207_G16733827, productId=PROD-DZBP, version=1.1.5), JetlinksDeviceFirewareRespModel(id=G16733836, deviceName=电子班牌测试, productId=PROD-DZBP, version=1.3.3_c), JetlinksDeviceFirewareRespModel(id=KEP20240707001, deviceName=黄河交通学院_身份核验移动终端2, productId=HISOME_ANDROID, version=20241224-1122#2.1.8), JetlinksDeviceFirewareRespModel(id=PID388E2207007544, deviceName=像素设备, productId=PROD-DZBP, version=1.1.6)]))
[2m2025-07-28 10:00:00.101[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-6][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) 
[2m2025-07-28 10:00:00.102[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-6][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: 2025.05.20.1807(String), 2025-07-28 10:00:00.101(Timestamp), (String), 0(Integer), 0(String), 00024700113e(String)
[2m2025-07-28 10:00:00.105[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.t.u.h.s.i.UnifyAccessServiceImpl  [0;39m [2m:[0;39m Emqx消息桥接查询订阅 url=http://*************:8844/remote/access/emqx/subscribers/list
[2m2025-07-28 10:00:00.106[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-1][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 40
[2m2025-07-28 10:00:00.107[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-1][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE sb_sbxx SET zxzt = ?,ext_status = ? WHERE ( ( xlh in ( ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? ) ) ) 
[2m2025-07-28 10:00:00.107[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-1][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: 0(String), 0(Integer), 00024700113e(String), 00024720d16a(String), 1231241157(String), ATN240303000100YT55YBF(String), CENCGW100_S01MA00BBA000D1000E(String), cillum(String), ddcdb3ac5810283f(String), DENCGW829_C04MB10BDJ07548e(String), G16733827(String), ipsumadk(String), PID388E2207007544(String), S30SZA2023140181(String), test0012(String), test01-xlh(String), test091903(String), testYdzd004(String), testYdzdXlh001(String), testYdzdXlh002(String)
[2m2025-07-28 10:00:00.117[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-6][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-28 10:00:00.117[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-6][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) 
[2m2025-07-28 10:00:00.118[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-6][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: 2025.05.24.2002(String), 2025-07-28 10:00:00.117(Timestamp), (String), 0(Integer), 0(String), 00024720d16a(String)
[2m2025-07-28 10:00:00.128[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-1][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 31
[2m2025-07-28 10:00:00.128[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-1][0;39m [36mc.x.i.service.impl.SbSbxxServiceImpl    [0;39m [2m:[0;39m getCssbZxzt - 开始获取场所设备在线状态.
[2m2025-07-28 10:00:00.129[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-1][0;39m [36mc.x.i.m.SbSbxxMapper.getCssbZxzt_COUNT  [0;39m [2m:[0;39m ==>  Preparing: SELECT count(0) FROM sb_sbxx sb LEFT JOIN sb_sbcsgx gx ON sb.sbxxbh = gx.sbbh WHERE sb.sczt = '0' AND sb.sblx = '171' AND gx.csbh IS NOT NULL AND sb.xlh IS NOT NULL 
[2m2025-07-28 10:00:00.129[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-1][0;39m [36mc.x.i.m.SbSbxxMapper.getCssbZxzt_COUNT  [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-28 10:00:00.131[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-6][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-28 10:00:00.131[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-6][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) 
[2m2025-07-28 10:00:00.132[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-6][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: 1.5.6(String), 2025-07-28 10:00:00.131(Timestamp), (String), 0(Integer), 0(String), AB1602853(String)
[2m2025-07-28 10:00:00.135[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.t.u.h.s.i.UnifyAccessServiceImpl  [0;39m [2m:[0;39m Emqx消息桥接查询订阅 result=Wrapper(code=200, message=success, result=EmqxSubscribersListRespModel(subscribers=[/HISOME_ANDROID/testYdzdXlh001/function/invoke, /CENCGW100_C/DENCGW829_C04MB10BDJ027590/function/invoke, /HISOME_ANDROID/testYdzd004/function/invoke, /PROD-DZBP/test0012/function/invoke, /PROD-DZBP/FC1836988/function/invoke, /HISOME_ANDROID/ddcdb3ac5810283f/function/invoke, /PROD-DZBP/G04754406/properties/read, /PROD-DZBP/FC1836989/function/invoke, /HISOME_ANDROID/KEP20240707001/function/invoke, /PROD-DZBP/AB1602853/function/invoke, /HISOME_ANDROID/testYdzdXlh002/function/invoke, /CENCGW100_S/CENCGW100_SIV1c9f717de4acfc99/function/invoke, /CENCGW100_S/CENCGW100_SIVb33ad3ede4acfc99/function/invoke, /PROD-DZBP/AB1602853/properties/read, /HISOME_ANDROID/ATN240303000100YT55YBF/function/invoke, /PROD-DZBP/G16733836/function/invoke, /HISOME_ANDROID/S30SZA2023140181/function/invoke, /CENCGW100_C/DENCGW829_C04MB10BDJ07548e/function/invoke, /PROD-DZBP/G04754406/function/invoke, /CENCGW100_S/CENCGW100_SIVb770d1a6a1ec5b44/function/invoke]))
[2m2025-07-28 10:00:00.135[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.util.schedule.ScheduleJobExecutor [0;39m [2m:[0;39m 任务执行完毕，任务ID：23110111540401808639620095703040  总共耗时：126毫秒
[2m2025-07-28 10:00:00.135[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-1][0;39m [36mc.x.i.m.SbSbxxMapper.getCssbZxzt_COUNT  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 10:00:00.136[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.m.S.insertSelective               [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO schedule_job_log ( log_id,job_id,job_name,bean_name,result,times,create_time ) VALUES( ?,?,?,?,?,?,? ) 
[2m2025-07-28 10:00:00.136[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-1][0;39m [36mc.x.i.m.KsKsccMapper.selectByExample    [0;39m [2m:[0;39m ==>  Preparing: SELECT bh,ksjhbh,ccm,kmm,ccmc,kmmc,kssj,jssj,yxcdsj,kssbqksj,ksrcsj,zc,kspch,cjsj,xgsj,xn,xq,scztw FROM ks_kscc WHERE ( ( scztw = ? and kssj <= ? and jssj >= ? ) ) 
[2m2025-07-28 10:00:00.136[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-7][0;39m [36mc.x.i.m.S.insertSelective               [0;39m [2m:[0;39m ==> Parameters: 25072810000002268850028619527168(String), 23110111540401808639620095703040(String), jobSubscribeMqtt(String), subscriberDevInfoToMqttTaskService(String), 0(Integer), 126(Integer), 2025-07-28 10:00:00.009(Timestamp)
[2m2025-07-28 10:00:00.136[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-1][0;39m [36mc.x.i.m.KsKsccMapper.selectByExample    [0;39m [2m:[0;39m ==> Parameters: 0(String), 2025-07-28 10:00:00.136(Timestamp), 2025-07-28 10:00:00.136(Timestamp)
[2m2025-07-28 10:00:00.144[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-1][0;39m [36mc.x.i.m.KsKsccMapper.selectByExample    [0;39m [2m:[0;39m <==      Total: 0
[2m2025-07-28 10:00:00.145[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-1][0;39m [36mc.x.i.service.impl.SbSbxxServiceImpl    [0;39m [2m:[0;39m getCssbZxzt - 获取场所设备在线状态. [OK]
[2m2025-07-28 10:00:00.145[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-1][0;39m [36mc.x.i.service.impl.SbSbxxServiceImpl    [0;39m [2m:[0;39m ========设备在线状态更新完成=========
[2m2025-07-28 10:00:00.145[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-1][0;39m [36mc.x.i.util.schedule.ScheduleJobExecutor [0;39m [2m:[0;39m 任务执行完毕，任务ID：23110110440401808639620095703040  总共耗时：138毫秒
[2m2025-07-28 10:00:00.145[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-1][0;39m [36mc.x.i.m.S.insertSelective               [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO schedule_job_log ( log_id,job_id,job_name,bean_name,result,times,create_time ) VALUES( ?,?,?,?,?,?,? ) 
[2m2025-07-28 10:00:00.145[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-1][0;39m [36mc.x.i.m.S.insertSelective               [0;39m [2m:[0;39m ==> Parameters: 25072810000002268850028602749952(String), 23110110440401808639620095703040(String), jobSbzxztGx(String), sbzxztCxAndGxTaskService(String), 0(Integer), 138(Integer), 2025-07-28 10:00:00.007(Timestamp)
[2m2025-07-28 10:00:00.147[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-6][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-28 10:00:00.147[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-6][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) 
[2m2025-07-28 10:00:00.148[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-6][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: 2.1.3_test3(String), 2025-07-28 10:00:00.147(Timestamp), MI.T0.LP4X.4G(String), 0(Integer), 0(String), ATN240303000100YT55YBF(String)
[2m2025-07-28 10:00:00.163[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-6][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-28 10:00:00.164[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-6][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) 
[2m2025-07-28 10:00:00.164[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-6][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: 2.1.8(String), 2025-07-28 10:00:00.163(Timestamp), MI.T0.LP4X.4G(String), 0(Integer), 0(String), ddcdb3ac5810283f(String)
[2m2025-07-28 10:00:00.179[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-6][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-28 10:00:00.181[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-6][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) 
[2m2025-07-28 10:00:00.181[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-6][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: 2025.01.27(String), 2025-07-28 10:00:00.179(Timestamp), (String), 0(Integer), 0(String), DENCGW829_C04MB10BDJ027590(String)
[2m2025-07-28 10:00:00.195[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-6][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-28 10:00:00.195[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-6][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) 
[2m2025-07-28 10:00:00.196[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-6][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: 2025.03.18(String), 2025-07-28 10:00:00.195(Timestamp), (String), 0(Integer), 0(String), DENCGW829_C04MB10BDJ07548e(String)
[2m2025-07-28 10:00:00.210[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-6][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-28 10:00:00.211[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-6][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) 
[2m2025-07-28 10:00:00.211[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-6][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: 1.3.3(String), 2025-07-28 10:00:00.21(Timestamp), (String), 0(Integer), 0(String), FC1836988(String)
[2m2025-07-28 10:00:00.226[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-6][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-28 10:00:00.227[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-6][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) 
[2m2025-07-28 10:00:00.227[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-6][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: 1.5.6(String), 2025-07-28 10:00:00.227(Timestamp), (String), 0(Integer), 0(String), FC1836989(String)
[2m2025-07-28 10:00:00.242[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-6][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-28 10:00:00.243[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-6][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) 
[2m2025-07-28 10:00:00.243[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-6][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: 1.1(String), 2025-07-28 10:00:00.242(Timestamp), (String), 0(Integer), 0(String), G04754401(String)
[2m2025-07-28 10:00:00.256[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-6][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-28 10:00:00.257[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-6][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) 
[2m2025-07-28 10:00:00.257[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-6][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: 1.1(String), 2025-07-28 10:00:00.257(Timestamp), (String), 0(Integer), 0(String), G04754402(String)
[2m2025-07-28 10:00:00.270[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-6][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-28 10:00:00.271[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-6][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) 
[2m2025-07-28 10:00:00.271[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-6][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: 1.1(String), 2025-07-28 10:00:00.27(Timestamp), (String), 0(Integer), 0(String), G04754403(String)
[2m2025-07-28 10:00:00.283[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-6][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-28 10:00:00.285[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-6][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) 
[2m2025-07-28 10:00:00.286[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-6][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: 1.1(String), 2025-07-28 10:00:00.283(Timestamp), (String), 0(Integer), 0(String), G04754404(String)
[2m2025-07-28 10:00:00.299[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-6][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-28 10:00:00.300[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-6][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) 
[2m2025-07-28 10:00:00.300[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-6][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: 1.1(String), 2025-07-28 10:00:00.299(Timestamp), (String), 0(Integer), 0(String), G04754405(String)
[2m2025-07-28 10:00:00.312[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-6][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-28 10:00:00.313[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-6][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) 
[2m2025-07-28 10:00:00.314[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-6][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: 1.5.6_c(String), 2025-07-28 10:00:00.313(Timestamp), (String), 0(Integer), 0(String), G04754406(String)
[2m2025-07-28 10:00:00.329[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-6][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-28 10:00:00.330[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-6][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) 
[2m2025-07-28 10:00:00.330[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-6][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: 1.1(String), 2025-07-28 10:00:00.329(Timestamp), (String), 0(Integer), 0(String), G04754407(String)
[2m2025-07-28 10:00:00.343[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-6][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-28 10:00:00.344[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-6][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) 
[2m2025-07-28 10:00:00.344[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-6][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: 1.1(String), 2025-07-28 10:00:00.343(Timestamp), (String), 0(Integer), 0(String), G04754408(String)
[2m2025-07-28 10:00:00.356[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-6][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-28 10:00:00.357[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-6][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) 
[2m2025-07-28 10:00:00.357[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-6][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: 1.1(String), 2025-07-28 10:00:00.356(Timestamp), (String), 0(Integer), 0(String), G04754409(String)
[2m2025-07-28 10:00:00.368[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-6][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-28 10:00:00.368[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-6][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) 
[2m2025-07-28 10:00:00.368[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-6][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: 1.1.5(String), 2025-07-28 10:00:00.368(Timestamp), (String), 0(Integer), 0(String), G16733827(String)
[2m2025-07-28 10:00:00.381[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-6][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-28 10:00:00.382[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-6][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) 
[2m2025-07-28 10:00:00.382[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-6][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: 1.3.3_c(String), 2025-07-28 10:00:00.381(Timestamp), (String), 0(Integer), 0(String), G16733836(String)
[2m2025-07-28 10:00:00.397[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-6][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-28 10:00:00.398[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-6][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) 
[2m2025-07-28 10:00:00.398[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-6][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: 2.1.8(String), 2025-07-28 10:00:00.397(Timestamp), 20241224-1122(String), 0(Integer), 0(String), KEP20240707001(String)
[2m2025-07-28 10:00:00.413[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-6][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-28 10:00:00.415[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-6][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) 
[2m2025-07-28 10:00:00.415[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-6][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: 1.1.6(String), 2025-07-28 10:00:00.414(Timestamp), (String), 0(Integer), 0(String), PID388E2207007544(String)
[2m2025-07-28 10:00:00.428[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-6][0;39m [36mc.x.i.m.S.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-28 10:00:00.429[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-6][0;39m [36mc.x.i.service.impl.SbSbxxServiceImpl    [0;39m [2m:[0;39m 固件版本更新10条
[2m2025-07-28 10:00:00.429[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-6][0;39m [36mc.x.i.service.impl.SbSbxxServiceImpl    [0;39m [2m:[0;39m 固件版本更新 OK, 共计更新10条
[2m2025-07-28 10:00:00.429[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-6][0;39m [36mc.x.i.util.schedule.ScheduleJobExecutor [0;39m [2m:[0;39m 任务执行完毕，任务ID：2502130940211339531603881230336  总共耗时：419毫秒
[2m2025-07-28 10:00:00.430[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-6][0;39m [36mc.x.i.m.S.insertSelective               [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO schedule_job_log ( log_id,job_id,job_name,bean_name,params,result,times,create_time ) VALUES( ?,?,?,?,?,?,?,? ) 
[2m2025-07-28 10:00:00.430[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-6][0;39m [36mc.x.i.m.S.insertSelective               [0;39m [2m:[0;39m ==> Parameters: 25072810000002268850028619527171(String), 2502130940211339531603881230336(String), 设备固件版本更新(String), deviceVersionUpdateTaskService(String), (String), 0(Integer), 419(Integer), 2025-07-28 10:00:00.009(Timestamp)
[2m2025-07-28 10:00:01.481[0;39m [31mERROR [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[tyVerify_test01][0;39m [36mo.s.i.m.outbound.MqttPahoMessageHandler [0;39m [2m:[0;39m Lost connection; will attempt reconnect on next request
[2m2025-07-28 10:05:00.006[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-9][0;39m [36mc.x.i.util.schedule.ScheduleJobExecutor [0;39m [2m:[0;39m 任务准备执行，任务ID：24081610292802018092458168076288
[2m2025-07-28 10:05:00.014[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-9][0;39m [36mc.x.i.m.KsKsjhMapper.selectByExample    [0;39m [2m:[0;39m ==>  Preparing: SELECT ksjhbh,mc,kssj,jssj,xn,xq,sfmr,kszt,sbjm,sbkqsj,sbgbsj,cjlx,kslx,qysbbmd,wifiqy,cjsj,xgsj,scztw,kzqy,qydsxf,dsxfsj,dsxfzt,jkqdrs,dbms FROM ks_ksjh WHERE ( ( scztw = ? and qydsxf = ? and dsxfzt = ? ) ) 
[2m2025-07-28 10:05:00.014[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-9][0;39m [36mc.x.i.m.KsKsjhMapper.selectByExample    [0;39m [2m:[0;39m ==> Parameters: 0(String), 1(Integer), 0(Integer)
[2m2025-07-28 10:05:00.022[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-9][0;39m [36mc.x.i.m.KsKsjhMapper.selectByExample    [0;39m [2m:[0;39m <==      Total: 0
[2m2025-07-28 10:05:00.023[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-9][0;39m [36mc.x.i.s.i.s.KsjhSjdsxfTaskServiceImpl   [0;39m [2m:[0;39m 当前无下发任务！
[2m2025-07-28 10:05:00.023[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-9][0;39m [36mc.x.i.util.schedule.ScheduleJobExecutor [0;39m [2m:[0;39m 任务执行完毕，任务ID：24081610292802018092458168076288  总共耗时：17毫秒
[2m2025-07-28 10:05:00.023[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-9][0;39m [36mc.x.i.m.S.insertSelective               [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO schedule_job_log ( log_id,job_id,job_name,bean_name,params,result,times,create_time ) VALUES( ?,?,?,?,?,?,?,? ) 
[2m2025-07-28 10:05:00.024[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-9][0;39m [36mc.x.i.m.S.insertSelective               [0;39m [2m:[0;39m ==> Parameters: 25072810050002268852545176761344(String), 24081610292802018092458168076288(String), ksjhSjdsxf(String), ksjhSjdsxfTaskService(String), (String), 0(Integer), 17(Integer), 2025-07-28 10:05:00.006(Timestamp)
[2m2025-07-28 10:06:12.457[0;39m [32m INFO [,84edec9770691793,84edec9770691793,false][0;39m [33m[eeip-standalone-service,84edec9770691793,84edec9770691793,,false][0;39m [35m15324[0;39m [2m---[0;39m [2m[nio-8888-exec-1][0;39m [36mc.x.i.facade.manager.CxtjController     [0;39m [2m:[0;39m 收到请求开始：[紧急呼叫查询][/manager/identityverify/cxtj/jjhjcx]reqModel:JjhjCxReq[ksjhbh=<null>,kcbh=<null>,clzt=<null>,startTime=<null>,endTime=<null>,pageNum=1,pageSize=10,msgId=<null>,transChannel=<null>,channelSerialNo=<null>,channelDate=<null>,channelTime=<null>,token=<null>,loginToken=<null>,transCode=<null>,clientIp=<null>,merchantId=<null>,appPackage=<null>]
[2m2025-07-28 10:06:12.503[0;39m [32m INFO [,84edec9770691793,84edec9770691793,false][0;39m [33m[eeip-standalone-service,84edec9770691793,84edec9770691793,,false][0;39m [35m15324[0;39m [2m---[0;39m [2m[nio-8888-exec-1][0;39m [36mc.x.i.s.impl.KsKwMessageServiceImpl     [0;39m [2m:[0;39m 紧急呼叫查询开始，查询条件：JjhjCxDTO[ksjhbh=<null>,kcbh=<null>,clzt=<null>,startTime=<null>,endTime=<null>,pageNum=1,pageSize=10]
[2m2025-07-28 10:06:12.506[0;39m [32mDEBUG [,84edec9770691793,84edec9770691793,false][0;39m [33m[eeip-standalone-service,84edec9770691793,84edec9770691793,,false][0;39m [35m15324[0;39m [2m---[0;39m [2m[nio-8888-exec-1][0;39m [36mc.x.i.m.K.selectJjhjList_COUNT          [0;39m [2m:[0;39m ==>  Preparing: SELECT count(0) FROM ks_kw_message msg LEFT JOIN ks_kw_message reply ON msg.ksjhbh = reply.ksjhbh AND msg.id = reply.replay_id AND reply.sczt = '0' WHERE msg.sczt = '0' AND msg.msg_type = '2000' AND msg.origin_app_type = 'HYAPP' AND msg.operate = '0' 
[2m2025-07-28 10:06:12.506[0;39m [32mDEBUG [,84edec9770691793,84edec9770691793,false][0;39m [33m[eeip-standalone-service,84edec9770691793,84edec9770691793,,false][0;39m [35m15324[0;39m [2m---[0;39m [2m[nio-8888-exec-1][0;39m [36mc.x.i.m.K.selectJjhjList_COUNT          [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-28 10:06:12.526[0;39m [32mDEBUG [,84edec9770691793,84edec9770691793,false][0;39m [33m[eeip-standalone-service,84edec9770691793,84edec9770691793,,false][0;39m [35m15324[0;39m [2m---[0;39m [2m[nio-8888-exec-1][0;39m [36mc.x.i.m.K.selectJjhjList_COUNT          [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 10:06:12.527[0;39m [32mDEBUG [,84edec9770691793,84edec9770691793,false][0;39m [33m[eeip-standalone-service,84edec9770691793,84edec9770691793,,false][0;39m [35m15324[0;39m [2m---[0;39m [2m[nio-8888-exec-1][0;39m [36mc.x.i.m.K.selectJjhjList                [0;39m [2m:[0;39m ==>  Preparing: SELECT msg.id, DATE_FORMAT(msg.send_time, '%Y-%m-%d %H:%i:%s') as fbsj, msg.origin_kcbh as kcbh, msg.comment as jjhjnr, reply.comment as hfnr, DATE_FORMAT(reply.send_time, '%Y-%m-%d %H:%i:%s') as hfsj, CASE WHEN reply.id IS NULL THEN '0' ELSE '1' END as clzt FROM ks_kw_message msg LEFT JOIN ks_kw_message reply ON msg.ksjhbh = reply.ksjhbh AND msg.id = reply.replay_id AND reply.sczt = '0' WHERE msg.sczt = '0' AND msg.msg_type = '2000' AND msg.origin_app_type = 'HYAPP' AND msg.operate = '0' ORDER BY msg.send_time DESC LIMIT ? 
[2m2025-07-28 10:06:12.527[0;39m [32mDEBUG [,84edec9770691793,84edec9770691793,false][0;39m [33m[eeip-standalone-service,84edec9770691793,84edec9770691793,,false][0;39m [35m15324[0;39m [2m---[0;39m [2m[nio-8888-exec-1][0;39m [36mc.x.i.m.K.selectJjhjList                [0;39m [2m:[0;39m ==> Parameters: 10(Integer)
[2m2025-07-28 10:06:12.536[0;39m [32mDEBUG [,84edec9770691793,84edec9770691793,false][0;39m [33m[eeip-standalone-service,84edec9770691793,84edec9770691793,,false][0;39m [35m15324[0;39m [2m---[0;39m [2m[nio-8888-exec-1][0;39m [36mc.x.i.m.K.selectJjhjList                [0;39m [2m:[0;39m <==      Total: 10
[2m2025-07-28 10:06:12.536[0;39m [32m INFO [,84edec9770691793,84edec9770691793,false][0;39m [33m[eeip-standalone-service,84edec9770691793,84edec9770691793,,false][0;39m [35m15324[0;39m [2m---[0;39m [2m[nio-8888-exec-1][0;39m [36mc.x.i.s.impl.KsKwMessageServiceImpl     [0;39m [2m:[0;39m 紧急呼叫查询结束，共查询到53条记录
[2m2025-07-28 10:06:12.561[0;39m [32m INFO [,84edec9770691793,84edec9770691793,false][0;39m [33m[eeip-standalone-service,84edec9770691793,84edec9770691793,,false][0;39m [35m15324[0;39m [2m---[0;39m [2m[nio-8888-exec-1][0;39m [36mc.x.i.facade.manager.CxtjController     [0;39m [2m:[0;39m 处理请求结束：[紧急呼叫查询][/manager/identityverify/cxtj/jjhjcx]reqModel:JjhjCxReq[ksjhbh=<null>,kcbh=<null>,clzt=<null>,startTime=<null>,endTime=<null>,pageNum=1,pageSize=10,msgId=<null>,transChannel=<null>,channelSerialNo=<null>,channelDate=<null>,channelTime=<null>,token=<null>,loginToken=<null>,transCode=<null>,clientIp=<null>,merchantId=<null>,appPackage=<null>],respModel:JjhjCxResp[data=Page{count=true, pageNum=1, pageSize=10, startRow=0, endRow=10, total=53, pages=6, reasonable=true, pageSizeZero=false}[JjhjCxVO[id=2507071451291391793755551432704,fbsj=2025-07-07 14:51:29,kcbh=003,007,jjhjnr=混合考场情况,hfnr=已收到，正派人处理,hfsj=2025-07-07 14:52:14,clzt=1], JjhjCxVO[id=2507071142321391746204223668224,fbsj=2025-07-07 11:42:32,kcbh=003,007,jjhjnr=电子班牌有误,hfnr=<null>,hfsj=<null>,clzt=0], JjhjCxVO[id=2507062126101391530691837034496,fbsj=2025-07-06 21:26:09,kcbh=003,007,jjhjnr=电子班牌有误,hfnr=已收到，正派人处理,hfsj=2025-07-06 21:27:24,clzt=1], JjhjCxVO[id=2505271725281376974603104026624,fbsj=2025-05-27 17:25:29,kcbh=003,jjhjnr=电子班牌有误,hfnr=收到，设备有问题，可先进行人工核验。保持考场秩序,hfsj=2025-05-27 17:25:43,clzt=1], JjhjCxVO[id=24122709074502114446502591008768,fbsj=2024-12-27 09:07:45,kcbh=001,jjhjnr=电子班牌有误,hfnr=<null>,hfsj=<null>,clzt=0], JjhjCxVO[id=24111914244302087064562724573184,fbsj=2024-11-19 14:24:42,kcbh=003,jjhjnr=混合考场情况,hfnr=<null>,hfsj=<null>,clzt=0], JjhjCxVO[id=24111914244302087064562816978944,fbsj=2024-11-19 14:24:42,kcbh=003,jjhjnr=混合考场情况,hfnr=<null>,hfsj=<null>,clzt=0], JjhjCxVO[id=24111914241302087064306008002560,fbsj=2024-11-19 14:24:12,kcbh=003,jjhjnr=其它,hfnr=<null>,hfsj=<null>,clzt=0], JjhjCxVO[id=24111914241302087064306167517184,fbsj=2024-11-19 14:24:12,kcbh=003,jjhjnr=其它,hfnr=<null>,hfsj=<null>,clzt=0], JjhjCxVO[id=24111914391602087071879906004992,fbsj=2024-11-19 14:10:16,kcbh=003,jjhjnr=其它,hfnr=<null>,hfsj=<null>,clzt=0]],totalRows=53,pageNum=1,pageSize=10,msgId=<null>,transChannel=<null>,channelSerialNo=<null>,transCode=<null>,respDate=<null>,respTime=<null>,ext=<null>]
[2m2025-07-28 10:06:47.862[0;39m [32m INFO [,e5d61a7221a07993,eb01aa526e99004f,false][0;39m [33m[eeip-standalone-service,e5d61a7221a07993,eb01aa526e99004f,764eae2b6402482e,false][0;39m [35m15324[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.b.u.m.s.impl.MqttMsgRevHandler      [0;39m [2m:[0;39m GenericMessage [payload={"data":{"OPERATION":"NOTIFY_DEVEVENT_YDZDBATTERY","TIMESTAMP":1753668406586,"Data":{"devType":"172","sbcsbh":"K3307820091100001","eventTime":"1753668406586","batteryVal":100,"eventType":"YDZDBATTERY","sn":"KEP20240707001"}},"sign":"","messageId":"batteryVal_1753668406586_KEP20240707001","timestamp":1753668406586,"token":"ee98d86d3ccc48a2a366770e8ffb7567"}, headers={mqtt_receivedRetained=false, spanTraceId=e5d61a7221a07993, spanId=e5d61a7221a07993, nativeHeaders={spanTraceId=[e5d61a7221a07993], spanId=[e5d61a7221a07993], spanSampled=[0]}, mqtt_duplicate=false, id=df401ffb-384a-f427-ee06-e4d428a376e4, spanSampled=0, mqtt_receivedTopic=/HISOME_ANDROID/KEP20240707001/event/REPORT_DEVEVENT, mqtt_receivedQos=1}]
[2m2025-07-28 10:06:47.864[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[lTaskExecutor-2][0;39m [36mc.x.a.service.impl.BizMqttMsgRevHandler [0;39m [2m:[0;39m topic[/HISOME_ANDROID/KEP20240707001/event/REPORT_DEVEVENT] 没有方法可以处理这个主题...
[2m2025-07-28 10:06:47.864[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[lTaskExecutor-5][0;39m [36mc.x.r.s.impl.BasicMqttMsgRevHandler     [0;39m [2m:[0;39m topic[/HISOME_ANDROID/KEP20240707001/event/REPORT_DEVEVENT] 没有方法可以处理这个主题...
[2m2025-07-28 10:06:47.864[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36m.u.m.s.i.IdentityVerifyMqttMsgRevHandler[0;39m [2m:[0;39m judgeDeviceIdExist:KEP20240707001
[2m2025-07-28 10:06:47.864[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mx.i.s.i.m.MqttReportDeveventEvantHandler[0;39m [2m:[0;39m 处理mqtt上报设备事件消息:BaseOperationReq(operation=NOTIFY_DEVEVENT_YDZDBATTERY, timestamp=1753668406586, data={"devType":"172","sbcsbh":"K3307820091100001","eventTime":"1753668406586","batteryVal":100,"eventType":"YDZDBATTERY","sn":"KEP20240707001"})
[2m2025-07-28 10:06:47.926[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.i.m.S.updateZxztAndNtpByXlh         [0;39m [2m:[0;39m ==>  Preparing: update sb_sbxx SET zxzt = ?, battery = ?, update_time = now() where xlh = ? 
[2m2025-07-28 10:06:47.926[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.i.m.S.updateZxztAndNtpByXlh         [0;39m [2m:[0;39m ==> Parameters: 1(String), 100(String), KEP20240707001(String)
[2m2025-07-28 10:06:48.052[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.i.m.S.updateZxztAndNtpByXlh         [0;39m [2m:[0;39m <==    Updates: 6
[2m2025-07-28 10:06:48.052[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.i.m.SbSbxxMapper.selectByExample    [0;39m [2m:[0;39m ==>  Preparing: SELECT sbxxbh,sbmc,sbxh,sbbm,sbpp,sbcs,sblx,sblxmc,jspc,jssj,sbccrq,sbazrq,sbzbq,sbwldz,macdz,ipdz,dkh,czxt,kzdkh,appbb,firmwarebb,xlh,ramdiskbb,sbzt,jhzt,sbmy,sczt,zxzt,sbzpid,sbqm,sbjcjg,zhzxsj,create_time,update_time,sbztw,sfydsb,osbb,battery,ntp_enable,ntp_source,ntp_interval,ext_status FROM sb_sbxx WHERE ( ( sczt = ? and xlh = ? ) ) 
[2m2025-07-28 10:06:48.052[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.i.m.SbSbxxMapper.selectByExample    [0;39m [2m:[0;39m ==> Parameters: 0(String), KEP20240707001(String)
[2m2025-07-28 10:06:48.096[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mc.x.i.m.SbSbxxMapper.selectByExample    [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 10:06:48.096[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[lTaskExecutor-4][0;39m [36mx.i.s.i.m.MqttReportDeveventEvantHandler[0;39m [2m:[0;39m 上报电量大于阈值, 不记录日志
[2m2025-07-28 10:06:55.761[0;39m [32m INFO [,3a962092feb915b0,3a962092feb915b0,false][0;39m [33m[eeip-standalone-service,3a962092feb915b0,3a962092feb915b0,,false][0;39m [35m15324[0;39m [2m---[0;39m [2m[nio-8888-exec-4][0;39m [36mc.x.i.facade.manager.CxtjController     [0;39m [2m:[0;39m 收到请求开始：[紧急呼叫查询][/manager/identityverify/cxtj/jjhjcx]reqModel:JjhjCxReq[ksjhbh=2507211439231396864138952572928,kcbh=<null>,clzt=<null>,startTime=<null>,endTime=<null>,pageNum=1,pageSize=10,msgId=<null>,transChannel=<null>,channelSerialNo=<null>,channelDate=<null>,channelTime=<null>,token=<null>,loginToken=<null>,transCode=<null>,clientIp=<null>,merchantId=<null>,appPackage=<null>]
[2m2025-07-28 10:06:55.780[0;39m [32m INFO [,3a962092feb915b0,3a962092feb915b0,false][0;39m [33m[eeip-standalone-service,3a962092feb915b0,3a962092feb915b0,,false][0;39m [35m15324[0;39m [2m---[0;39m [2m[nio-8888-exec-4][0;39m [36mc.x.i.s.impl.KsKwMessageServiceImpl     [0;39m [2m:[0;39m 紧急呼叫查询开始，查询条件：JjhjCxDTO[ksjhbh=2507211439231396864138952572928,kcbh=<null>,clzt=<null>,startTime=<null>,endTime=<null>,pageNum=1,pageSize=10]
[2m2025-07-28 10:06:55.781[0;39m [32mDEBUG [,3a962092feb915b0,3a962092feb915b0,false][0;39m [33m[eeip-standalone-service,3a962092feb915b0,3a962092feb915b0,,false][0;39m [35m15324[0;39m [2m---[0;39m [2m[nio-8888-exec-4][0;39m [36mc.x.i.m.K.selectJjhjList_COUNT          [0;39m [2m:[0;39m ==>  Preparing: SELECT count(0) FROM ks_kw_message msg LEFT JOIN ks_kw_message reply ON msg.ksjhbh = reply.ksjhbh AND msg.id = reply.replay_id AND reply.sczt = '0' WHERE msg.sczt = '0' AND msg.msg_type = '2000' AND msg.origin_app_type = 'HYAPP' AND msg.operate = '0' AND msg.ksjhbh = ? 
[2m2025-07-28 10:06:55.781[0;39m [32mDEBUG [,3a962092feb915b0,3a962092feb915b0,false][0;39m [33m[eeip-standalone-service,3a962092feb915b0,3a962092feb915b0,,false][0;39m [35m15324[0;39m [2m---[0;39m [2m[nio-8888-exec-4][0;39m [36mc.x.i.m.K.selectJjhjList_COUNT          [0;39m [2m:[0;39m ==> Parameters: 2507211439231396864138952572928(String)
[2m2025-07-28 10:06:55.790[0;39m [32mDEBUG [,3a962092feb915b0,3a962092feb915b0,false][0;39m [33m[eeip-standalone-service,3a962092feb915b0,3a962092feb915b0,,false][0;39m [35m15324[0;39m [2m---[0;39m [2m[nio-8888-exec-4][0;39m [36mc.x.i.m.K.selectJjhjList_COUNT          [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 10:06:55.790[0;39m [32m INFO [,3a962092feb915b0,3a962092feb915b0,false][0;39m [33m[eeip-standalone-service,3a962092feb915b0,3a962092feb915b0,,false][0;39m [35m15324[0;39m [2m---[0;39m [2m[nio-8888-exec-4][0;39m [36mc.x.i.s.impl.KsKwMessageServiceImpl     [0;39m [2m:[0;39m 紧急呼叫查询结束，共查询到0条记录
[2m2025-07-28 10:06:55.814[0;39m [32m INFO [,3a962092feb915b0,3a962092feb915b0,false][0;39m [33m[eeip-standalone-service,3a962092feb915b0,3a962092feb915b0,,false][0;39m [35m15324[0;39m [2m---[0;39m [2m[nio-8888-exec-4][0;39m [36mc.x.i.facade.manager.CxtjController     [0;39m [2m:[0;39m 处理请求结束：[紧急呼叫查询][/manager/identityverify/cxtj/jjhjcx]reqModel:JjhjCxReq[ksjhbh=2507211439231396864138952572928,kcbh=<null>,clzt=<null>,startTime=<null>,endTime=<null>,pageNum=1,pageSize=10,msgId=<null>,transChannel=<null>,channelSerialNo=<null>,channelDate=<null>,channelTime=<null>,token=<null>,loginToken=<null>,transCode=<null>,clientIp=<null>,merchantId=<null>,appPackage=<null>],respModel:JjhjCxResp[data=Page{count=true, pageNum=1, pageSize=10, startRow=0, endRow=10, total=0, pages=0, reasonable=true, pageSizeZero=false}[],totalRows=0,pageNum=1,pageSize=10,msgId=<null>,transChannel=<null>,channelSerialNo=<null>,transCode=<null>,respDate=<null>,respTime=<null>,ext=<null>]
[2m2025-07-28 10:07:00.010[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-4][0;39m [36mc.x.i.util.schedule.ScheduleJobExecutor [0;39m [2m:[0;39m 任务准备执行，任务ID：23110110740401808639620095703040
[2m2025-07-28 10:07:00.017[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-4][0;39m [36mc.x.i.util.schedule.ScheduleJobExecutor [0;39m [2m:[0;39m 任务执行完毕，任务ID：23110110740401808639620095703040  总共耗时：7毫秒
[2m2025-07-28 10:07:00.019[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-4][0;39m [36mc.x.i.m.S.insertSelective               [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO schedule_job_log ( log_id,job_id,job_name,bean_name,result,times,create_time ) VALUES( ?,?,?,?,?,?,? ) 
[2m2025-07-28 10:07:00.019[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-4][0;39m [36mc.x.i.m.S.insertSelective               [0;39m [2m:[0;39m ==> Parameters: 25072810070002268853551843275776(String), 23110110740401808639620095703040(String), jobOnlineStatus(String), onlineStatusTaskService(String), 0(Integer), 7(Integer), 2025-07-28 10:07:00.01(Timestamp)
[2m2025-07-28 10:07:01.476[0;39m [31mERROR [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[tyVerify_test01][0;39m [36mo.s.i.m.outbound.MqttPahoMessageHandler [0;39m [2m:[0;39m Lost connection; will attempt reconnect on next request
[2m2025-07-28 10:10:00.003[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-8][0;39m [36mc.x.i.util.schedule.ScheduleJobExecutor [0;39m [2m:[0;39m 任务准备执行，任务ID：23110111540401808639620095703040
[2m2025-07-28 10:10:00.003[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-3][0;39m [36mc.x.i.util.schedule.ScheduleJobExecutor [0;39m [2m:[0;39m 任务准备执行，任务ID：24081610292802018092458168076288
[2m2025-07-28 10:10:00.011[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-8][0;39m [36mc.x.i.m.SbSbxxMapper.selectByExample    [0;39m [2m:[0;39m ==>  Preparing: SELECT sbxxbh,sbmc,sbxh,sbbm,sbpp,sbcs,sblx,sblxmc,jspc,jssj,sbccrq,sbazrq,sbzbq,sbwldz,macdz,ipdz,dkh,czxt,kzdkh,appbb,firmwarebb,xlh,ramdiskbb,sbzt,jhzt,sbmy,sczt,zxzt,sbzpid,sbqm,sbjcjg,zhzxsj,create_time,update_time,sbztw,sfydsb,osbb,battery,ntp_enable,ntp_source,ntp_interval,ext_status FROM sb_sbxx WHERE ( ( sczt = ? ) ) 
[2m2025-07-28 10:10:00.011[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-8][0;39m [36mc.x.i.m.SbSbxxMapper.selectByExample    [0;39m [2m:[0;39m ==> Parameters: 0(String)
[2m2025-07-28 10:10:00.011[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-3][0;39m [36mc.x.i.m.KsKsjhMapper.selectByExample    [0;39m [2m:[0;39m ==>  Preparing: SELECT ksjhbh,mc,kssj,jssj,xn,xq,sfmr,kszt,sbjm,sbkqsj,sbgbsj,cjlx,kslx,qysbbmd,wifiqy,cjsj,xgsj,scztw,kzqy,qydsxf,dsxfsj,dsxfzt,jkqdrs,dbms FROM ks_ksjh WHERE ( ( scztw = ? and qydsxf = ? and dsxfzt = ? ) ) 
[2m2025-07-28 10:10:00.011[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-3][0;39m [36mc.x.i.m.KsKsjhMapper.selectByExample    [0;39m [2m:[0;39m ==> Parameters: 0(String), 1(Integer), 0(Integer)
[2m2025-07-28 10:10:00.021[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-3][0;39m [36mc.x.i.m.KsKsjhMapper.selectByExample    [0;39m [2m:[0;39m <==      Total: 0
[2m2025-07-28 10:10:00.021[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-3][0;39m [36mc.x.i.s.i.s.KsjhSjdsxfTaskServiceImpl   [0;39m [2m:[0;39m 当前无下发任务！
[2m2025-07-28 10:10:00.021[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-3][0;39m [36mc.x.i.util.schedule.ScheduleJobExecutor [0;39m [2m:[0;39m 任务执行完毕，任务ID：24081610292802018092458168076288  总共耗时：18毫秒
[2m2025-07-28 10:10:00.022[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-3][0;39m [36mc.x.i.m.S.insertSelective               [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO schedule_job_log ( log_id,job_id,job_name,bean_name,params,result,times,create_time ) VALUES( ?,?,?,?,?,?,?,? ) 
[2m2025-07-28 10:10:00.022[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-3][0;39m [36mc.x.i.m.S.insertSelective               [0;39m [2m:[0;39m ==> Parameters: 25072810100002268855061733995521(String), 24081610292802018092458168076288(String), ksjhSjdsxf(String), ksjhSjdsxfTaskService(String), (String), 0(Integer), 18(Integer), 2025-07-28 10:10:00.003(Timestamp)
[2m2025-07-28 10:10:00.022[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-8][0;39m [36mc.x.i.m.SbSbxxMapper.selectByExample    [0;39m [2m:[0;39m <==      Total: 16
[2m2025-07-28 10:10:00.022[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-8][0;39m [36mc.x.i.t.u.h.s.i.UnifyAccessServiceImpl  [0;39m [2m:[0;39m url http://*************:8844/device/category?terms[0].column=parentId&terms[0].value=-5-
[2m2025-07-28 10:10:00.113[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-8][0;39m [36mc.x.i.t.u.h.s.i.UnifyAccessServiceImpl  [0;39m [2m:[0;39m 发送给接入返回结果result=JetlinksWrapper(message=success, status=200, code=200, timestamp=1753668598946, result=[DeviceCategoryRespModel(id=AIEdgeServer, parentId=-5-, key=AIEdgeServer, name=边缘计算服务器, productId=HISOME-DX000, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1747805356879, productName=汉尚多模态超脑), DeviceCategoryRespModel(id=cheating_prevention, parentId=-5-, key=CheatingPrevention, name=作弊防控设备, productId=HISOME-RRS3000, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1655261812143, productName=3536侦测服务器), DeviceCategoryRespModel(id=digital_hd_matrix, parentId=-5-, key=digital_hd_matrix, name=数字高清矩阵, productId=null, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1692077761801, productName=null), DeviceCategoryRespModel(id=ejjksb, parentId=-5-, key=ejjksb, name=耳机监考设备, productId=CENC_HEADPHONE, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1739411557430, productName=头戴式耳机), DeviceCategoryRespModel(id=hdd_box, parentId=-5-, key=hdd_box, name=视频存储盒子, productId=HISOME-HDD4000, sortIndex=1, level=2, creatorId=1199596756811550720, createTime=1680154724527, productName=视频数据存储终端), DeviceCategoryRespModel(id=hskpjmwxpb, parentId=-5-, key=hskpjmwxpb, name=汉尚考培加密无线平板, productId=null, sortIndex=1, level=1, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1740365256141, productName=null), DeviceCategoryRespModel(id=ipc, parentId=-5-, key=ipc, name=网络摄像机, productId=HISOME-IPC, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1691550353555, productName=IPC通用摄像机), DeviceCategoryRespModel(id=kcdzbp, parentId=-5-, key=kcdzbp, name=考场电子班牌, productId=PROD-DZBP, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1697418220328, productName=电子班牌), DeviceCategoryRespModel(id=kcwg, parentId=-5-, key=kcwg, name=考场网关, productId=CENCGW100_C, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1661844529888, productName=智能教室网关), DeviceCategoryRespModel(id=kdwg, parentId=-5-, key=kdwg, name=考点网关, productId=CENCGW100_S, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1661844519215, productName=智能校级网关), DeviceCategoryRespModel(id=lhsjkydzd, parentId=-5-, key=lhsjkydzd, name=理化生机考移动终端, productId=HISOME-PAD-101, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1740365218610, productName=汉尚理化生机考移动终端), DeviceCategoryRespModel(id=nvr, parentId=-5-, key=nvr, name=数字硬盘录像机, productId=HISOME-NVR4000, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1687656114753, productName=数字硬盘录像机), DeviceCategoryRespModel(id=sip, parentId=-5-, key=sip, name=SIP路由分发服务器, productId=SIP8000, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1689045772782, productName=SIP路由分发服务器), DeviceCategoryRespModel(id=tzsxj, parentId=-5-, key=tzsxj, name=同轴摄像机, productId=null, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1691552091707, productName=null), DeviceCategoryRespModel(id=ydzd, parentId=-5-, key=ydzd, name=移动终端, productId=HISOME_ANDROID, sortIndex=1, level=1, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1661844790208, productName=身份核验移动终端)])
[2m2025-07-28 10:10:00.113[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-8][0;39m [36mc.x.i.service.impl.SbSbxxServiceImpl    [0;39m [2m:[0;39m 
[2m2025-07-28 10:10:00.120[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-8][0;39m [36mc.x.i.t.u.h.s.i.UnifyAccessServiceImpl  [0;39m [2m:[0;39m Emqx消息桥接查询订阅 url=http://*************:8844/remote/access/emqx/subscribers/list
[2m2025-07-28 10:10:00.145[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-8][0;39m [36mc.x.i.t.u.h.s.i.UnifyAccessServiceImpl  [0;39m [2m:[0;39m Emqx消息桥接查询订阅 result=Wrapper(code=200, message=success, result=EmqxSubscribersListRespModel(subscribers=[/HISOME_ANDROID/testYdzdXlh001/function/invoke, /CENCGW100_C/DENCGW829_C04MB10BDJ027590/function/invoke, /HISOME_ANDROID/testYdzd004/function/invoke, /PROD-DZBP/test0012/function/invoke, /PROD-DZBP/FC1836988/function/invoke, /HISOME_ANDROID/ddcdb3ac5810283f/function/invoke, /PROD-DZBP/G04754406/properties/read, /PROD-DZBP/FC1836989/function/invoke, /HISOME_ANDROID/KEP20240707001/function/invoke, /PROD-DZBP/AB1602853/function/invoke, /HISOME_ANDROID/testYdzdXlh002/function/invoke, /CENCGW100_S/CENCGW100_SIV1c9f717de4acfc99/function/invoke, /CENCGW100_S/CENCGW100_SIVb33ad3ede4acfc99/function/invoke, /PROD-DZBP/AB1602853/properties/read, /HISOME_ANDROID/ATN240303000100YT55YBF/function/invoke, /PROD-DZBP/G16733836/function/invoke, /HISOME_ANDROID/S30SZA2023140181/function/invoke, /CENCGW100_C/DENCGW829_C04MB10BDJ07548e/function/invoke, /PROD-DZBP/G04754406/function/invoke, /CENCGW100_S/CENCGW100_SIVb770d1a6a1ec5b44/function/invoke]))
[2m2025-07-28 10:10:00.145[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-8][0;39m [36mc.x.i.util.schedule.ScheduleJobExecutor [0;39m [2m:[0;39m 任务执行完毕，任务ID：23110111540401808639620095703040  总共耗时：142毫秒
[2m2025-07-28 10:10:00.146[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-8][0;39m [36mc.x.i.m.S.insertSelective               [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO schedule_job_log ( log_id,job_id,job_name,bean_name,result,times,create_time ) VALUES( ?,?,?,?,?,?,? ) 
[2m2025-07-28 10:10:00.146[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-8][0;39m [36mc.x.i.m.S.insertSelective               [0;39m [2m:[0;39m ==> Parameters: 25072810100002268855061733995520(String), 23110111540401808639620095703040(String), jobSubscribeMqtt(String), subscriberDevInfoToMqttTaskService(String), 0(Integer), 142(Integer), 2025-07-28 10:10:00.003(Timestamp)
[2m2025-07-28 10:14:00.003[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[duler_Worker-10][0;39m [36mc.x.i.util.schedule.ScheduleJobExecutor [0;39m [2m:[0;39m 任务准备执行，任务ID：23110110740401808639620095703040
[2m2025-07-28 10:14:00.010[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[duler_Worker-10][0;39m [36mc.x.i.util.schedule.ScheduleJobExecutor [0;39m [2m:[0;39m 任务执行完毕，任务ID：23110110740401808639620095703040  总共耗时：7毫秒
[2m2025-07-28 10:14:00.017[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[duler_Worker-10][0;39m [36mc.x.i.m.S.insertSelective               [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO schedule_job_log ( log_id,job_id,job_name,bean_name,result,times,create_time ) VALUES( ?,?,?,?,?,?,? ) 
[2m2025-07-28 10:14:00.017[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[duler_Worker-10][0;39m [36mc.x.i.m.S.insertSelective               [0;39m [2m:[0;39m ==> Parameters: 25072810140002268857074999915520(String), 23110110740401808639620095703040(String), jobOnlineStatus(String), onlineStatusTaskService(String), 0(Integer), 7(Integer), 2025-07-28 10:14:00.003(Timestamp)
[2m2025-07-28 10:14:01.482[0;39m [31mERROR [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[tyVerify_test01][0;39m [36mo.s.i.m.outbound.MqttPahoMessageHandler [0;39m [2m:[0;39m Lost connection; will attempt reconnect on next request
[2m2025-07-28 10:14:50.891[0;39m [32m INFO [,9582f7195e37a62e,9582f7195e37a62e,false][0;39m [33m[eeip-standalone-service,9582f7195e37a62e,9582f7195e37a62e,,false][0;39m [35m15324[0;39m [2m---[0;39m [2m[nio-8888-exec-2][0;39m [36mc.x.i.facade.manager.CxtjController     [0;39m [2m:[0;39m 收到请求开始：[紧急呼叫查询][/manager/identityverify/cxtj/jjhjcx]reqModel:JjhjCxReq[ksjhbh=2507211439231396864138952572928,kcbh=<null>,clzt=<null>,startTime=<null>,endTime=<null>,pageNum=1,pageSize=10,msgId=<null>,transChannel=<null>,channelSerialNo=<null>,channelDate=<null>,channelTime=<null>,token=<null>,loginToken=<null>,transCode=<null>,clientIp=<null>,merchantId=<null>,appPackage=<null>]
[2m2025-07-28 10:14:50.904[0;39m [32m INFO [,9582f7195e37a62e,9582f7195e37a62e,false][0;39m [33m[eeip-standalone-service,9582f7195e37a62e,9582f7195e37a62e,,false][0;39m [35m15324[0;39m [2m---[0;39m [2m[nio-8888-exec-2][0;39m [36mc.x.i.s.impl.KsKwMessageServiceImpl     [0;39m [2m:[0;39m 紧急呼叫查询开始，查询条件：JjhjCxDTO[ksjhbh=2507211439231396864138952572928,kcbh=<null>,clzt=<null>,startTime=<null>,endTime=<null>,pageNum=1,pageSize=10]
[2m2025-07-28 10:14:50.906[0;39m [32mDEBUG [,9582f7195e37a62e,9582f7195e37a62e,false][0;39m [33m[eeip-standalone-service,9582f7195e37a62e,9582f7195e37a62e,,false][0;39m [35m15324[0;39m [2m---[0;39m [2m[nio-8888-exec-2][0;39m [36mc.x.i.m.K.selectJjhjList_COUNT          [0;39m [2m:[0;39m ==>  Preparing: SELECT count(0) FROM ks_kw_message msg LEFT JOIN ks_kw_message reply ON msg.ksjhbh = reply.ksjhbh AND msg.id = reply.replay_id AND reply.sczt = '0' WHERE msg.sczt = '0' AND msg.msg_type = '2000' AND msg.origin_app_type = 'HYAPP' AND msg.operate = '0' AND msg.ksjhbh = ? 
[2m2025-07-28 10:14:50.906[0;39m [32mDEBUG [,9582f7195e37a62e,9582f7195e37a62e,false][0;39m [33m[eeip-standalone-service,9582f7195e37a62e,9582f7195e37a62e,,false][0;39m [35m15324[0;39m [2m---[0;39m [2m[nio-8888-exec-2][0;39m [36mc.x.i.m.K.selectJjhjList_COUNT          [0;39m [2m:[0;39m ==> Parameters: 2507211439231396864138952572928(String)
[2m2025-07-28 10:14:50.916[0;39m [32mDEBUG [,9582f7195e37a62e,9582f7195e37a62e,false][0;39m [33m[eeip-standalone-service,9582f7195e37a62e,9582f7195e37a62e,,false][0;39m [35m15324[0;39m [2m---[0;39m [2m[nio-8888-exec-2][0;39m [36mc.x.i.m.K.selectJjhjList_COUNT          [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 10:14:50.916[0;39m [32m INFO [,9582f7195e37a62e,9582f7195e37a62e,false][0;39m [33m[eeip-standalone-service,9582f7195e37a62e,9582f7195e37a62e,,false][0;39m [35m15324[0;39m [2m---[0;39m [2m[nio-8888-exec-2][0;39m [36mc.x.i.s.impl.KsKwMessageServiceImpl     [0;39m [2m:[0;39m 紧急呼叫查询结束，共查询到0条记录
[2m2025-07-28 10:14:50.941[0;39m [32m INFO [,9582f7195e37a62e,9582f7195e37a62e,false][0;39m [33m[eeip-standalone-service,9582f7195e37a62e,9582f7195e37a62e,,false][0;39m [35m15324[0;39m [2m---[0;39m [2m[nio-8888-exec-2][0;39m [36mc.x.i.facade.manager.CxtjController     [0;39m [2m:[0;39m 处理请求结束：[紧急呼叫查询][/manager/identityverify/cxtj/jjhjcx]reqModel:JjhjCxReq[ksjhbh=2507211439231396864138952572928,kcbh=<null>,clzt=<null>,startTime=<null>,endTime=<null>,pageNum=1,pageSize=10,msgId=<null>,transChannel=<null>,channelSerialNo=<null>,channelDate=<null>,channelTime=<null>,token=<null>,loginToken=<null>,transCode=<null>,clientIp=<null>,merchantId=<null>,appPackage=<null>],respModel:JjhjCxResp[data=Page{count=true, pageNum=1, pageSize=10, startRow=0, endRow=10, total=0, pages=0, reasonable=true, pageSizeZero=false}[],totalRows=0,pageNum=1,pageSize=10,msgId=<null>,transChannel=<null>,channelSerialNo=<null>,transCode=<null>,respDate=<null>,respTime=<null>,ext=<null>]
[2m2025-07-28 10:15:00.003[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-2][0;39m [36mc.x.i.util.schedule.ScheduleJobExecutor [0;39m [2m:[0;39m 任务准备执行，任务ID：24081610292802018092458168076288
[2m2025-07-28 10:15:00.004[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-2][0;39m [36mc.x.i.m.KsKsjhMapper.selectByExample    [0;39m [2m:[0;39m ==>  Preparing: SELECT ksjhbh,mc,kssj,jssj,xn,xq,sfmr,kszt,sbjm,sbkqsj,sbgbsj,cjlx,kslx,qysbbmd,wifiqy,cjsj,xgsj,scztw,kzqy,qydsxf,dsxfsj,dsxfzt,jkqdrs,dbms FROM ks_ksjh WHERE ( ( scztw = ? and qydsxf = ? and dsxfzt = ? ) ) 
[2m2025-07-28 10:15:00.005[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-2][0;39m [36mc.x.i.m.KsKsjhMapper.selectByExample    [0;39m [2m:[0;39m ==> Parameters: 0(String), 1(Integer), 0(Integer)
[2m2025-07-28 10:15:00.014[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-2][0;39m [36mc.x.i.m.KsKsjhMapper.selectByExample    [0;39m [2m:[0;39m <==      Total: 0
[2m2025-07-28 10:15:00.014[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-2][0;39m [36mc.x.i.s.i.s.KsjhSjdsxfTaskServiceImpl   [0;39m [2m:[0;39m 当前无下发任务！
[2m2025-07-28 10:15:00.014[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-2][0;39m [36mc.x.i.util.schedule.ScheduleJobExecutor [0;39m [2m:[0;39m 任务执行完毕，任务ID：24081610292802018092458168076288  总共耗时：11毫秒
[2m2025-07-28 10:15:00.014[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-2][0;39m [36mc.x.i.m.S.insertSelective               [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO schedule_job_log ( log_id,job_id,job_name,bean_name,params,result,times,create_time ) VALUES( ?,?,?,?,?,?,?,? ) 
[2m2025-07-28 10:15:00.015[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[eduler_Worker-2][0;39m [36mc.x.i.m.S.insertSelective               [0;39m [2m:[0;39m ==> Parameters: 25072810150002268857578316395520(String), 24081610292802018092458168076288(String), ksjhSjdsxf(String), ksjhSjdsxfTaskService(String), (String), 0(Integer), 11(Integer), 2025-07-28 10:15:00.003(Timestamp)
[2m2025-07-28 10:15:52.014[0;39m [32m INFO [,1b7f4eba129627ef,1b7f4eba129627ef,false][0;39m [33m[eeip-standalone-service,1b7f4eba129627ef,1b7f4eba129627ef,,false][0;39m [35m15324[0;39m [2m---[0;39m [2m[nio-8888-exec-6][0;39m [36mc.x.i.facade.manager.CxtjController     [0;39m [2m:[0;39m 收到请求开始：[紧急呼叫查询][/manager/identityverify/cxtj/jjhjcx]reqModel:JjhjCxReq[ksjhbh=2507211439231396864138952572928,kcbh=<null>,clzt=<null>,startTime=<null>,endTime=<null>,pageNum=1,pageSize=10,msgId=<null>,transChannel=<null>,channelSerialNo=<null>,channelDate=<null>,channelTime=<null>,token=<null>,loginToken=<null>,transCode=<null>,clientIp=<null>,merchantId=<null>,appPackage=<null>]
[2m2025-07-28 10:15:52.028[0;39m [32m INFO [,1b7f4eba129627ef,1b7f4eba129627ef,false][0;39m [33m[eeip-standalone-service,1b7f4eba129627ef,1b7f4eba129627ef,,false][0;39m [35m15324[0;39m [2m---[0;39m [2m[nio-8888-exec-6][0;39m [36mc.x.i.s.impl.KsKwMessageServiceImpl     [0;39m [2m:[0;39m 紧急呼叫查询开始，查询条件：JjhjCxDTO[ksjhbh=2507211439231396864138952572928,kcbh=<null>,clzt=<null>,startTime=<null>,endTime=<null>,pageNum=1,pageSize=10]
[2m2025-07-28 10:15:52.030[0;39m [32mDEBUG [,1b7f4eba129627ef,1b7f4eba129627ef,false][0;39m [33m[eeip-standalone-service,1b7f4eba129627ef,1b7f4eba129627ef,,false][0;39m [35m15324[0;39m [2m---[0;39m [2m[nio-8888-exec-6][0;39m [36mc.x.i.m.K.selectJjhjList_COUNT          [0;39m [2m:[0;39m ==>  Preparing: SELECT count(0) FROM ks_kw_message msg LEFT JOIN ks_kw_message reply ON msg.ksjhbh = reply.ksjhbh AND msg.id = reply.replay_id AND reply.sczt = '0' WHERE msg.sczt = '0' AND msg.msg_type = '2000' AND msg.origin_app_type = 'HYAPP' AND msg.operate = '0' AND msg.ksjhbh = ? 
[2m2025-07-28 10:15:52.030[0;39m [32mDEBUG [,1b7f4eba129627ef,1b7f4eba129627ef,false][0;39m [33m[eeip-standalone-service,1b7f4eba129627ef,1b7f4eba129627ef,,false][0;39m [35m15324[0;39m [2m---[0;39m [2m[nio-8888-exec-6][0;39m [36mc.x.i.m.K.selectJjhjList_COUNT          [0;39m [2m:[0;39m ==> Parameters: 2507211439231396864138952572928(String)
[2m2025-07-28 10:15:52.037[0;39m [32mDEBUG [,1b7f4eba129627ef,1b7f4eba129627ef,false][0;39m [33m[eeip-standalone-service,1b7f4eba129627ef,1b7f4eba129627ef,,false][0;39m [35m15324[0;39m [2m---[0;39m [2m[nio-8888-exec-6][0;39m [36mc.x.i.m.K.selectJjhjList_COUNT          [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 10:15:52.037[0;39m [32m INFO [,1b7f4eba129627ef,1b7f4eba129627ef,false][0;39m [33m[eeip-standalone-service,1b7f4eba129627ef,1b7f4eba129627ef,,false][0;39m [35m15324[0;39m [2m---[0;39m [2m[nio-8888-exec-6][0;39m [36mc.x.i.s.impl.KsKwMessageServiceImpl     [0;39m [2m:[0;39m 紧急呼叫查询结束，共查询到0条记录
[2m2025-07-28 10:15:52.063[0;39m [32m INFO [,1b7f4eba129627ef,1b7f4eba129627ef,false][0;39m [33m[eeip-standalone-service,1b7f4eba129627ef,1b7f4eba129627ef,,false][0;39m [35m15324[0;39m [2m---[0;39m [2m[nio-8888-exec-6][0;39m [36mc.x.i.facade.manager.CxtjController     [0;39m [2m:[0;39m 处理请求结束：[紧急呼叫查询][/manager/identityverify/cxtj/jjhjcx]reqModel:JjhjCxReq[ksjhbh=2507211439231396864138952572928,kcbh=<null>,clzt=<null>,startTime=<null>,endTime=<null>,pageNum=1,pageSize=10,msgId=<null>,transChannel=<null>,channelSerialNo=<null>,channelDate=<null>,channelTime=<null>,token=<null>,loginToken=<null>,transCode=<null>,clientIp=<null>,merchantId=<null>,appPackage=<null>],respModel:JjhjCxResp[data=Page{count=true, pageNum=1, pageSize=10, startRow=0, endRow=10, total=0, pages=0, reasonable=true, pageSizeZero=false}[],totalRows=0,pageNum=1,pageSize=10,msgId=<null>,transChannel=<null>,channelSerialNo=<null>,transCode=<null>,respDate=<null>,respTime=<null>,ext=<null>]
[2m2025-07-28 10:16:42.105[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[     Thread-113][0;39m [36mo.s.c.n.e.s.EurekaServiceRegistry       [0;39m [2m:[0;39m Unregistering application EEIP-STANDALONE-SERVICE with eureka with status DOWN
[2m2025-07-28 10:16:46.140[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[     Thread-113][0;39m [36m.m.i.MqttPahoMessageDrivenChannelAdapter[0;39m [2m:[0;39m stopped inbound
[2m2025-07-28 10:16:46.140[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[     Thread-113][0;39m [36mProxyFactoryBean$MethodInvocationGateway[0;39m [2m:[0;39m stopped mqttGateway
[2m2025-07-28 10:16:46.141[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[     Thread-113][0;39m [36mo.s.i.gateway.GatewayProxyFactoryBean   [0;39m [2m:[0;39m stopped mqttGateway
[2m2025-07-28 10:16:46.141[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[     Thread-113][0;39m [36mo.s.i.endpoint.EventDrivenConsumer      [0;39m [2m:[0;39m Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
[2m2025-07-28 10:16:46.142[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[     Thread-113][0;39m [36mo.s.i.channel.PublishSubscribeChannel   [0;39m [2m:[0;39m Channel 'application-1.errorChannel' has 0 subscriber(s).
[2m2025-07-28 10:16:46.142[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[     Thread-113][0;39m [36mo.s.i.endpoint.EventDrivenConsumer      [0;39m [2m:[0;39m stopped _org.springframework.integration.errorLogger
[2m2025-07-28 10:16:46.142[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[     Thread-113][0;39m [36mo.s.i.endpoint.EventDrivenConsumer      [0;39m [2m:[0;39m Removing {message-handler:mqttConfig.mqttOutbound.serviceActivator} as a subscriber to the 'mqttOutboundChannel' channel
[2m2025-07-28 10:16:46.142[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[     Thread-113][0;39m [36mo.s.integration.channel.ExecutorChannel [0;39m [2m:[0;39m Channel 'application-1.mqttOutboundChannel' has 0 subscriber(s).
[2m2025-07-28 10:16:46.142[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[     Thread-113][0;39m [36mo.s.i.endpoint.EventDrivenConsumer      [0;39m [2m:[0;39m stopped mqttConfig.mqttOutbound.serviceActivator
[2m2025-07-28 10:16:46.142[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[     Thread-113][0;39m [36mo.s.i.endpoint.EventDrivenConsumer      [0;39m [2m:[0;39m Removing {message-handler:mqttConfig.handler.serviceActivator} as a subscriber to the 'mqttInputChannel' channel
[2m2025-07-28 10:16:46.142[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[     Thread-113][0;39m [36mo.s.integration.channel.ExecutorChannel [0;39m [2m:[0;39m Channel 'application-1.mqttInputChannel' has 0 subscriber(s).
[2m2025-07-28 10:16:46.142[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[     Thread-113][0;39m [36mo.s.i.endpoint.EventDrivenConsumer      [0;39m [2m:[0;39m stopped mqttConfig.handler.serviceActivator
[2m2025-07-28 10:16:50.248[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[     Thread-113][0;39m [36mo.s.s.c.ThreadPoolTaskScheduler         [0;39m [2m:[0;39m Shutting down ExecutorService 'taskScheduler'
[2m2025-07-28 10:16:50.248[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[     Thread-113][0;39m [36mo.s.s.quartz.SchedulerFactoryBean       [0;39m [2m:[0;39m Shutting down Quartz Scheduler
[2m2025-07-28 10:16:50.250[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m15324[0;39m [2m---[0;39m [2m[     Thread-113][0;39m [36mo.s.s.concurrent.ThreadPoolTaskExecutor [0;39m [2m:[0;39m Shutting down ExecutorService
[2m2025-07-28 10:17:03.437[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m35440[0;39m [2m---[0;39m [2m[           main][0;39m [36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$f195d098] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2m2025-07-28 10:17:03.918[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m35440[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.c.c.ConfigServicePropertySourceLocator[0;39m [2m:[0;39m Fetching config from server at : http://localhost:8888
[2m2025-07-28 10:17:06.060[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m35440[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.c.c.ConfigServicePropertySourceLocator[0;39m [2m:[0;39m Connect Timeout Exception on Url - http://localhost:8888. Will be trying the next url if available
[2m2025-07-28 10:17:06.062[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m35440[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.c.c.ConfigServicePropertySourceLocator[0;39m [2m:[0;39m Could not locate PropertySource: I/O error on GET request for "http://localhost:8888/eeip-standalone-service/default": Connection refused: connect; nested exception is java.net.ConnectException: Connection refused: connect
[2m2025-07-28 10:17:06.065[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m35440[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.standalone.StandaloneApplication    [0;39m [2m:[0;39m The following profiles are active: alone
[2m2025-07-28 10:17:11.654[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m35440[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Multiple Spring Data modules found, entering strict repository configuration mode!
[2m2025-07-28 10:17:11.658[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m35440[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[2m2025-07-28 10:17:11.942[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m35440[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 260ms. Found 0 Redis repository interfaces.
[2m2025-07-28 10:17:12.136[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m35440[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.boot.actuate.endpoint.EndpointId    [0;39m [2m:[0;39m Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
[2m2025-07-28 10:17:12.356[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m35440[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.boot.actuate.endpoint.EndpointId    [0;39m [2m:[0;39m Endpoint ID 'hystrix.stream' contains invalid characters, please migrate to a valid format.
[2m2025-07-28 10:17:12.841[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m35440[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.cloud.context.scope.GenericScope    [0;39m [2m:[0;39m BeanFactory id=5b395cf1-ddbf-331b-876f-aa2c7020b748
[2m2025-07-28 10:17:12.866[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m35440[0;39m [2m---[0;39m [2m[           main][0;39m [36mfaultConfiguringBeanFactoryPostProcessor[0;39m [2m:[0;39m No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
[2m2025-07-28 10:17:12.873[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m35440[0;39m [2m---[0;39m [2m[           main][0;39m [36mfaultConfiguringBeanFactoryPostProcessor[0;39m [2m:[0;39m No bean named 'taskScheduler' has been explicitly defined. Therefore, a default ThreadPoolTaskScheduler will be created.
[2m2025-07-28 10:17:12.887[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m35440[0;39m [2m---[0;39m [2m[           main][0;39m [36mfaultConfiguringBeanFactoryPostProcessor[0;39m [2m:[0;39m No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
[2m2025-07-28 10:17:12.981[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m35440[0;39m [2m---[0;39m [2m[           main][0;39m [36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'configProperties' of type [com.xcwlkj.core.config.properties.ConfigProperties$$EnhancerBySpringCGLIB$$3273ad31] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2m2025-07-28 10:17:12.983[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m35440[0;39m [2m---[0;39m [2m[           main][0;39m [36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'asyncTaskExecutorConfiguration' of type [com.xcwlkj.core.config.AsyncTaskExecutorConfiguration$$EnhancerBySpringCGLIB$$eba5f261] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2m2025-07-28 10:17:13.077[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m35440[0;39m [2m---[0;39m [2m[           main][0;39m [36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$cb3f65c0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2m2025-07-28 10:17:13.404[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m35440[0;39m [2m---[0;39m [2m[           main][0;39m [36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$d57bcd9b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2m2025-07-28 10:17:13.465[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m35440[0;39m [2m---[0;39m [2m[           main][0;39m [36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'redisConfig' of type [com.xcwlkj.standalone.config.RedisConfig$$EnhancerBySpringCGLIB$$b11eab18] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2m2025-07-28 10:17:13.513[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m35440[0;39m [2m---[0;39m [2m[           main][0;39m [36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'appConfig' of type [com.xcwlkj.standalone.config.AppConfig$$EnhancerBySpringCGLIB$$ed3fdcbe] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2m2025-07-28 10:17:13.548[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m35440[0;39m [2m---[0;39m [2m[           main][0;39m [36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'integrationDisposableAutoCreatedBeans' of type [org.springframework.integration.config.annotation.Disposables] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2m2025-07-28 10:17:13.593[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m35440[0;39m [2m---[0;39m [2m[           main][0;39m [36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'com.xcwlkj.biz.core.feignclient.FeignClientConfig' of type [com.xcwlkj.biz.core.feignclient.FeignClientConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2m2025-07-28 10:17:13.604[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m35440[0;39m [2m---[0;39m [2m[           main][0;39m [36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'org.springframework.integration.config.IntegrationManagementConfiguration' of type [org.springframework.integration.config.IntegrationManagementConfiguration$$EnhancerBySpringCGLIB$$406d88ca] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2m2025-07-28 10:17:13.648[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m35440[0;39m [2m---[0;39m [2m[           main][0;39m [36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$f195d098] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2m2025-07-28 10:17:14.249[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m35440[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat initialized with port(s): 8888 (http)
[2m2025-07-28 10:17:14.400[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m35440[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.web.context.ContextLoader           [0;39m [2m:[0;39m Root WebApplicationContext: initialization completed in 8310 ms
[2m2025-07-28 10:17:17.807[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m35440[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.a.e.web.ServletEndpointRegistrar  [0;39m [2m:[0;39m Registered '/actuator/hystrix.stream' to hystrix.stream-actuator-endpoint
[2m2025-07-28 10:17:17.904[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m35440[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.s.concurrent.ThreadPoolTaskExecutor [0;39m [2m:[0;39m Initializing ExecutorService
[2m2025-07-28 10:17:18.012[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m35440[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.s.concurrent.ThreadPoolTaskExecutor [0;39m [2m:[0;39m Initializing ExecutorService
[2m2025-07-28 10:17:18.114[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m35440[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.s.concurrent.ThreadPoolTaskExecutor [0;39m [2m:[0;39m Initializing ExecutorService
[2m2025-07-28 10:17:18.118[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m35440[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.xcwlkj.standalone.config.XcDfsConfig  [0;39m [2m:[0;39m ###dfsServerUrl=http://************:8811
[2m2025-07-28 10:17:18.120[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m35440[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.xcwlkj.standalone.config.XcDfsConfig  [0;39m [2m:[0;39m ###dfsChannel=ZJKSZHPT
[2m2025-07-28 10:17:18.120[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m35440[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.xcwlkj.standalone.config.XcDfsConfig  [0;39m [2m:[0;39m ###dfsAppId=e046e7c7e7fbf1f1a4183e00c76e0182
[2m2025-07-28 10:17:18.120[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m35440[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.xcwlkj.standalone.config.XcDfsConfig  [0;39m [2m:[0;39m ###dfsAppSecret=6d0dc6d02af7bab1f1ed39a7baadbb55
[2m2025-07-28 10:17:19.102[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m35440[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.interceptor.SqlLogInterceptor  [0;39m [2m:[0;39m [打印SQL拦截器创建]noticeTime=5.0秒
[2m2025-07-28 10:17:21.826[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m35440[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.c.c.AsyncTaskExecutorConfiguration  [0;39m [2m:[0;39m Creating Async Task Executor
[2m2025-07-28 10:17:21.828[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m35440[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.s.concurrent.ThreadPoolTaskExecutor [0;39m [2m:[0;39m Initializing ExecutorService
[2m2025-07-28 10:17:23.812[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m35440[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.xcwlkj.pubc.config.SmsConfig        [0;39m [2m:[0;39m SMS Bean IAcsClient Start
[2m2025-07-28 10:17:23.825[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m35440[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.xcwlkj.pubc.config.SmsConfig        [0;39m [2m:[0;39m 加载SMS Bean IAcsClient OK
