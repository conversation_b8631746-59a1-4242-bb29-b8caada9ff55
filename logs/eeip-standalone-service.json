{"@timestamp":"2025-07-28T01:25:40.197Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$daf13b78] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-28T01:25:40.730Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"o.s.c.c.c.ConfigServicePropertySourceLocator","rest":"Fetching config from server at : http://localhost:8888"}
{"@timestamp":"2025-07-28T01:25:42.865Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"o.s.c.c.c.ConfigServicePropertySourceLocator","rest":"Connect Timeout Exception on Url - http://localhost:8888. Will be trying the next url if available"}
{"@timestamp":"2025-07-28T01:25:42.866Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"o.s.c.c.c.ConfigServicePropertySourceLocator","rest":"Could not locate PropertySource: I/O error on GET request for \"http://localhost:8888/eeip-standalone-service/default\": Connection refused: connect; nested exception is java.net.ConnectException: Connection refused: connect"}
{"@timestamp":"2025-07-28T01:25:42.869Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.standalone.StandaloneApplication","rest":"The following profiles are active: alone"}
{"@timestamp":"2025-07-28T01:25:48.853Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"o.s.d.r.c.RepositoryConfigurationDelegate","rest":"Multiple Spring Data modules found, entering strict repository configuration mode!"}
{"@timestamp":"2025-07-28T01:25:48.857Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"o.s.d.r.c.RepositoryConfigurationDelegate","rest":"Bootstrapping Spring Data Redis repositories in DEFAULT mode."}
{"@timestamp":"2025-07-28T01:25:49.229Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"o.s.d.r.c.RepositoryConfigurationDelegate","rest":"Finished Spring Data repository scanning in 340ms. Found 0 Redis repository interfaces."}
{"@timestamp":"2025-07-28T01:25:49.480Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"o.s.boot.actuate.endpoint.EndpointId","rest":"Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format."}
{"@timestamp":"2025-07-28T01:25:49.761Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"o.s.boot.actuate.endpoint.EndpointId","rest":"Endpoint ID 'hystrix.stream' contains invalid characters, please migrate to a valid format."}
{"@timestamp":"2025-07-28T01:25:50.384Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"o.s.cloud.context.scope.GenericScope","rest":"BeanFactory id=5b395cf1-ddbf-331b-876f-aa2c7020b748"}
{"@timestamp":"2025-07-28T01:25:50.416Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor","rest":"No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created."}
{"@timestamp":"2025-07-28T01:25:50.426Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor","rest":"No bean named 'taskScheduler' has been explicitly defined. Therefore, a default ThreadPoolTaskScheduler will be created."}
{"@timestamp":"2025-07-28T01:25:50.446Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor","rest":"No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created."}
{"@timestamp":"2025-07-28T01:25:50.592Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'configProperties' of type [com.xcwlkj.core.config.properties.ConfigProperties$$EnhancerBySpringCGLIB$$1bcf1811] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-28T01:25:50.592Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'asyncTaskExecutorConfiguration' of type [com.xcwlkj.core.config.AsyncTaskExecutorConfiguration$$EnhancerBySpringCGLIB$$d5015d41] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-28T01:25:50.699Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$b49ad0a0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-28T01:25:51.074Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$bed7387b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-28T01:25:51.139Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'redisConfig' of type [com.xcwlkj.standalone.config.RedisConfig$$EnhancerBySpringCGLIB$$9a7a15f8] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-28T01:25:51.188Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'appConfig' of type [com.xcwlkj.standalone.config.AppConfig$$EnhancerBySpringCGLIB$$d69b479e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-28T01:25:51.224Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'integrationDisposableAutoCreatedBeans' of type [org.springframework.integration.config.annotation.Disposables] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-28T01:25:51.287Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'com.xcwlkj.biz.core.feignclient.FeignClientConfig' of type [com.xcwlkj.biz.core.feignclient.FeignClientConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-28T01:25:51.297Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.integration.config.IntegrationManagementConfiguration' of type [org.springframework.integration.config.IntegrationManagementConfiguration$$EnhancerBySpringCGLIB$$29c8f3aa] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-28T01:25:51.346Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$daf13b78] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-28T01:25:52.109Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"o.s.b.w.embedded.tomcat.TomcatWebServer","rest":"Tomcat initialized with port(s): 8888 (http)"}
{"@timestamp":"2025-07-28T01:25:52.309Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"o.s.web.context.ContextLoader","rest":"Root WebApplicationContext: initialization completed in 9416 ms"}
{"@timestamp":"2025-07-28T01:25:56.580Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"o.s.b.a.e.web.ServletEndpointRegistrar","rest":"Registered '/actuator/hystrix.stream' to hystrix.stream-actuator-endpoint"}
{"@timestamp":"2025-07-28T01:25:56.718Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"o.s.s.concurrent.ThreadPoolTaskExecutor","rest":"Initializing ExecutorService"}
{"@timestamp":"2025-07-28T01:25:56.846Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"o.s.s.concurrent.ThreadPoolTaskExecutor","rest":"Initializing ExecutorService"}
{"@timestamp":"2025-07-28T01:25:56.969Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"o.s.s.concurrent.ThreadPoolTaskExecutor","rest":"Initializing ExecutorService"}
{"@timestamp":"2025-07-28T01:25:56.977Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsServerUrl=http://************:8811"}
{"@timestamp":"2025-07-28T01:25:56.977Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsChannel=ZJKSZHPT"}
{"@timestamp":"2025-07-28T01:25:56.977Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsAppId=e046e7c7e7fbf1f1a4183e00c76e0182"}
{"@timestamp":"2025-07-28T01:25:56.977Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsAppSecret=6d0dc6d02af7bab1f1ed39a7baadbb55"}
{"@timestamp":"2025-07-28T01:25:58.234Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.core.interceptor.SqlLogInterceptor","rest":"[打印SQL拦截器创建]noticeTime=5.0秒"}
{"@timestamp":"2025-07-28T01:26:01.055Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.c.c.AsyncTaskExecutorConfiguration","rest":"Creating Async Task Executor"}
{"@timestamp":"2025-07-28T01:26:01.056Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"o.s.s.concurrent.ThreadPoolTaskExecutor","rest":"Initializing ExecutorService"}
{"@timestamp":"2025-07-28T01:26:02.675Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"com.xcwlkj.pubc.config.SmsConfig","rest":"SMS Bean IAcsClient Start"}
{"@timestamp":"2025-07-28T01:26:02.690Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"com.xcwlkj.pubc.config.SmsConfig","rest":"加载SMS Bean IAcsClient OK"}
{"@timestamp":"2025-07-28T01:26:11.869Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化####"}
{"@timestamp":"2025-07-28T01:26:11.877Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化成功####"}
{"@timestamp":"2025-07-28T01:26:14.015Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.taskcenter.DefaultHandleFactory","rest":"配置线程池工作线程数量[16]"}
{"@timestamp":"2025-07-28T01:26:14.159Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"开始检查平台类型和初始化上级平台DFS客户端..."}
{"@timestamp":"2025-07-28T01:26:14.511Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) "}
{"@timestamp":"2025-07-28T01:26:14.529Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==> Parameters: defaultPlat(String)"}
{"@timestamp":"2025-07-28T01:26:14.552Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-28T01:26:14.559Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"当前平台类型为：HISOME，开始初始化上级平台DFS客户端"}
{"@timestamp":"2025-07-28T01:26:14.559Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) "}
{"@timestamp":"2025-07-28T01:26:14.559Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==> Parameters: HISOME_fileServerUrl(String)"}
{"@timestamp":"2025-07-28T01:26:14.569Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-28T01:26:14.571Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) "}
{"@timestamp":"2025-07-28T01:26:14.571Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==> Parameters: HISOME_fileServerChannel(String)"}
{"@timestamp":"2025-07-28T01:26:14.578Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-28T01:26:14.582Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) "}
{"@timestamp":"2025-07-28T01:26:14.582Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==> Parameters: HISOME_fileServiceAppId(String)"}
{"@timestamp":"2025-07-28T01:26:14.588Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-28T01:26:14.588Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) "}
{"@timestamp":"2025-07-28T01:26:14.592Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==> Parameters: HISOME_fileServiceAppSecret(String)"}
{"@timestamp":"2025-07-28T01:26:14.599Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-28T01:26:14.608Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"上级平台DFS客户端初始化成功"}
{"@timestamp":"2025-07-28T01:26:14.608Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"上级平台DFS客户端自动初始化完成"}
{"@timestamp":"2025-07-28T01:26:16.771Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动中...."}
{"@timestamp":"2025-07-28T01:26:16.771Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.ScheduleJobMapper.selectAll","rest":"==>  Preparing: SELECT job_id,job_name,bean_name,params,cron_expression,status,remark,create_time,update_time FROM schedule_job "}
{"@timestamp":"2025-07-28T01:26:16.771Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.ScheduleJobMapper.selectAll","rest":"==> Parameters: "}
{"@timestamp":"2025-07-28T01:26:16.781Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.ScheduleJobMapper.selectAll","rest":"<==      Total: 10"}
{"@timestamp":"2025-07-28T01:26:16.802Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[teskTask-jobTest111]"}
{"@timestamp":"2025-07-28T01:26:16.802Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[sbzxztCxAndGxTaskService-jobSbzxztGx]"}
{"@timestamp":"2025-07-28T01:26:16.804Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[onlineStatusTaskService-jobOnlineStatus]"}
{"@timestamp":"2025-07-28T01:26:16.804Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[subscriberDevInfoToMqttTaskService-jobSubscribeMqtt]"}
{"@timestamp":"2025-07-28T01:26:16.804Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[sjxfsbCxxfTaskService-sjxfsbCxxf]"}
{"@timestamp":"2025-07-28T01:26:16.804Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[sbxxReportTaskService-设备信息上报]"}
{"@timestamp":"2025-07-28T01:26:16.804Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[ksjhSjdsxfTaskService-ksjhSjdsxf]"}
{"@timestamp":"2025-07-28T01:26:16.804Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[deviceVersionUpdateTaskService-设备固件版本更新]"}
{"@timestamp":"2025-07-28T01:26:16.804Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[deviceEventDeleteTaskService-设备事件删除]"}
{"@timestamp":"2025-07-28T01:26:16.804Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[kssjDsscsjptTaskService-考试数据定时上传上级平台]"}
{"@timestamp":"2025-07-28T01:26:16.809Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动完成"}
{"@timestamp":"2025-07-28T01:26:19.084Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"o.s.s.concurrent.ThreadPoolTaskScheduler","rest":"Initializing ExecutorService 'taskScheduler'"}
{"@timestamp":"2025-07-28T01:26:19.541Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.c.f.FeignHystrixConcurrencyStrategyIntellif","rest":"Current Hystrix plugins configuration is [concurrencyStrategy [com.netflix.hystrix.strategy.concurrency.HystrixConcurrencyStrategyDefault@52355243],eventNotifier [com.netflix.hystrix.strategy.eventnotifier.HystrixEventNotifierDefault@2544f6f1],metricPublisher [com.netflix.hystrix.strategy.metrics.HystrixMetricsPublisherDefault@12710aa4],propertiesStrategy [com.netflix.hystrix.strategy.properties.HystrixPropertiesStrategyDefault@52991723],]"}
{"@timestamp":"2025-07-28T01:26:19.541Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.c.f.FeignHystrixConcurrencyStrategyIntellif","rest":"Registering Sleuth Hystrix Concurrency Strategy."}
{"@timestamp":"2025-07-28T01:26:24.488Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"com.xcwlkj.standalone.config.MqttConfig","rest":"######监听MQTT主题[/CENCGW100_S/CENCGW100_SIV1c9f717de4acfc99/function/invoke]"}
{"@timestamp":"2025-07-28T01:26:24.490Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"com.xcwlkj.standalone.config.MqttConfig","rest":"######监听MQTT主题[/CENCGW100_S/CENCGW100_SIV1c9f717de4acfc99/token/get/reply]"}
{"@timestamp":"2025-07-28T01:26:24.490Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"com.xcwlkj.standalone.config.MqttConfig","rest":"######监听MQTT主题[/+/+/event/+]"}
{"@timestamp":"2025-07-28T01:26:24.521Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.xcwlkj.standalone.config.XxlJobConfig","rest":">>>>>>>>>>> xxl-job config init."}
{"@timestamp":"2025-07-28T01:26:25.715Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"o.s.b.a.f.FreeMarkerAutoConfiguration","rest":"Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)"}
{"@timestamp":"2025-07-28T01:26:27.039Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"o.s.b.a.e.web.EndpointLinksResolver","rest":"Exposing 21 endpoint(s) beneath base path '/actuator'"}
{"@timestamp":"2025-07-28T01:26:28.510Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel"}
{"@timestamp":"2025-07-28T01:26:28.511Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"o.s.i.channel.PublishSubscribeChannel","rest":"Channel 'application-1.errorChannel' has 1 subscriber(s)."}
{"@timestamp":"2025-07-28T01:26:28.511Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"started _org.springframework.integration.errorLogger"}
{"@timestamp":"2025-07-28T01:26:28.511Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"Adding {message-handler:mqttConfig.handler.serviceActivator} as a subscriber to the 'mqttInputChannel' channel"}
{"@timestamp":"2025-07-28T01:26:28.511Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"o.s.integration.channel.ExecutorChannel","rest":"Channel 'application-1.mqttInputChannel' has 1 subscriber(s)."}
{"@timestamp":"2025-07-28T01:26:28.511Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"started mqttConfig.handler.serviceActivator"}
{"@timestamp":"2025-07-28T01:26:28.511Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"Adding {message-handler:mqttConfig.mqttOutbound.serviceActivator} as a subscriber to the 'mqttOutboundChannel' channel"}
{"@timestamp":"2025-07-28T01:26:28.511Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"o.s.integration.channel.ExecutorChannel","rest":"Channel 'application-1.mqttOutboundChannel' has 1 subscriber(s)."}
{"@timestamp":"2025-07-28T01:26:28.512Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"started mqttConfig.mqttOutbound.serviceActivator"}
{"@timestamp":"2025-07-28T01:26:28.512Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"o.s.i.g.GatewayProxyFactoryBean$MethodInvocationGateway","rest":"started mqttGateway"}
{"@timestamp":"2025-07-28T01:26:28.512Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"o.s.i.gateway.GatewayProxyFactoryBean","rest":"started mqttGateway"}
{"@timestamp":"2025-07-28T01:26:28.544Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"o.s.c.netflix.eureka.InstanceInfoFactory","rest":"Setting initial instance status as: STARTING"}
{"@timestamp":"2025-07-28T01:26:31.411Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"o.s.c.n.e.s.EurekaServiceRegistry","rest":"Registering application EEIP-STANDALONE-SERVICE with eureka with status UP"}
{"@timestamp":"2025-07-28T01:26:31.800Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"o.s.i.m.i.MqttPahoMessageDrivenChannelAdapter","rest":"started inbound"}
{"@timestamp":"2025-07-28T01:26:31.800Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"o.s.s.quartz.SchedulerFactoryBean","rest":"Starting Quartz Scheduler now"}
{"@timestamp":"2025-07-28T01:26:31.903Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"o.s.b.w.embedded.tomcat.TomcatWebServer","rest":"Tomcat started on port(s): 8888 (http) with context path ''"}
{"@timestamp":"2025-07-28T01:26:31.903Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"o.s.c.n.e.s.EurekaAutoServiceRegistration","rest":"Updating port to 8888"}
{"@timestamp":"2025-07-28T01:26:32.187Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.standalone.StandaloneApplication","rest":"Started StandaloneApplication in 53.635 seconds (JVM running for 54.403)"}
{"@timestamp":"2025-07-28T01:26:32.216Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"com.xcwlkj.util.YmlUtil","rest":"开始从application-alone.yml加载配置信息"}
{"@timestamp":"2025-07-28T01:26:32.217Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"com.xcwlkj.util.YmlUtil","rest":"读取外部文件失败D:\\code\\HS_CUEEIP\\trunk\\provider/config/application-alone.yml"}
{"@timestamp":"2025-07-28T01:26:32.221Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"com.xcwlkj.util.YmlUtil","rest":"开始从application-alone.yml加载配置信息【成功】"}
{"@timestamp":"2025-07-28T01:26:32.266Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"com.xcwlkj.cache.AbstractRedisDataCache","rest":"[PkgDataFileServices]cleanup cache finished"}
{"@timestamp":"2025-07-28T01:26:32.270Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.m.K.updateByExampleSelective","rest":"==>  Preparing: UPDATE ks_kssj_pkg_task SET complete = ? WHERE ( ( complete in ( ? , ? ) ) ) "}
{"@timestamp":"2025-07-28T01:26:32.270Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.m.K.updateByExampleSelective","rest":"==> Parameters: -1(Integer), 0(Integer), 1(Integer)"}
{"@timestamp":"2025-07-28T01:26:32.300Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.m.K.updateByExampleSelective","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-28T01:26:32.301Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.m.KsKssjPkgStatusMapper.clearDoing","rest":"==>  Preparing: UPDATE ks_kssj_pkg_status SET kssjb_qk = case when kssjb_qk = 1 then -10 else kssjb_qk end, jkryjbsjb_qk = case when jkryjbsjb_qk = 1 then -10 else jkryjbsjb_qk end, jkrybpsjb_qk = case when jkrybpsjb_qk = 1 then -10 else jkrybpsjb_qk end, pzsjb_qk = case when pzsjb_qk = 1 then -10 else pzsjb_qk end, kszpsjb_qk = case when kszpsjb_qk = 1 then -10 else kszpsjb_qk end, jkryzpsjb_qk = case when jkryzpsjb_qk = 1 then -10 else jkryzpsjb_qk end "}
{"@timestamp":"2025-07-28T01:26:32.302Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.m.KsKssjPkgStatusMapper.clearDoing","rest":"==> Parameters: "}
{"@timestamp":"2025-07-28T01:26:32.315Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.m.KsKssjPkgStatusMapper.clearDoing","rest":"<==    Updates: 295"}
{"@timestamp":"2025-07-28T01:26:32.318Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SnowflakeIdGenerator，init. workerId=0,datacenterId=0"}
{"@timestamp":"2025-07-28T01:26:32.319Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SnowflakeIdGenerator，finish<<<<<<<<<<<<<"}
{"@timestamp":"2025-07-28T01:26:32.319Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###ZookeeperInitRunner，init. HostAddress=**************:8888, applicationName=eeip-standalone-service"}
{"@timestamp":"2025-07-28T01:26:32.329Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.c.z.r.z.ZookeeperRegistryCenter","rest":"Elastic job: zookeeper registry center init, server lists is: **************:2181."}
{"@timestamp":"2025-07-28T01:26:32.607Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###ZookeeperInitRunner，finish<<<<<<<<<<<<<"}
{"@timestamp":"2025-07-28T01:26:32.612Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Init##"}
{"@timestamp":"2025-07-28T01:26:32.612Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.b.c.BasicinfoBusiCacheInitConfig","rest":"####开始缓存商户配置信息####"}
{"@timestamp":"2025-07-28T01:26:32.612Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.b.c.BasicinfoBusiCacheInitConfig","rest":"####缓存商户配置信息缓存成功####"}
{"@timestamp":"2025-07-28T01:26:32.612Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Succ<<<<<<<<<<<<<##"}
{"@timestamp":"2025-07-28T01:26:32.612Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Init##"}
{"@timestamp":"2025-07-28T01:26:32.612Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"com.xcwlkj.biz.config.BizInitConfig","rest":"####开始缓存配置信息####"}
{"@timestamp":"2025-07-28T01:26:32.612Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"com.xcwlkj.biz.config.BizInitConfig","rest":"####缓存配置信息缓存成功####"}
{"@timestamp":"2025-07-28T01:26:32.612Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"com.xcwlkj.biz.config.BizInitConfig","rest":"####消息队列初始化####"}
{"@timestamp":"2025-07-28T01:26:32.612Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"com.xcwlkj.biz.config.BizInitConfig","rest":"####消息队列初始化成功####"}
{"@timestamp":"2025-07-28T01:26:32.612Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Succ<<<<<<<<<<<<<##"}
{"@timestamp":"2025-07-28T01:26:32.613Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Init##"}
{"@timestamp":"2025-07-28T01:26:32.613Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.pubc.config.PubcBusiCacheInitConfig","rest":"####开始缓存公共服务配置信息####"}
{"@timestamp":"2025-07-28T01:26:32.613Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.pubc.config.PubcBusiCacheInitConfig","rest":"####缓存公共服务配置信息缓存成功####"}
{"@timestamp":"2025-07-28T01:26:32.614Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Succ<<<<<<<<<<<<<##"}
{"@timestamp":"2025-07-28T01:26:32.614Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Init##"}
{"@timestamp":"2025-07-28T01:26:32.614Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.e.c.EvaluationBusiCacheInitConfig","rest":"####开始缓存听评课配置信息####"}
{"@timestamp":"2025-07-28T01:26:32.614Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.e.c.EvaluationBusiCacheInitConfig","rest":"####缓存听评课配置信息缓存成功####"}
{"@timestamp":"2025-07-28T01:26:32.614Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.e.c.EvaluationBusiCacheInitConfig","rest":"####消息队列初始化####"}
{"@timestamp":"2025-07-28T01:26:32.614Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.e.c.EvaluationBusiCacheInitConfig","rest":"####消息队列初始化成功####"}
{"@timestamp":"2025-07-28T01:26:32.614Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Succ<<<<<<<<<<<<<##"}
{"@timestamp":"2025-07-28T01:26:32.614Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Init##"}
{"@timestamp":"2025-07-28T01:26:32.628Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####开始缓存校级身份核验平台配置信息####"}
{"@timestamp":"2025-07-28T01:26:33.423Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####服务器序列号为CENCGW100_SIV1c9f717de4acfc99"}
{"@timestamp":"2025-07-28T01:26:33.423Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####码表数据缓存初始化####"}
{"@timestamp":"2025-07-28T01:26:33.427Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.MbPzbMapper.selectByExample","rest":"==>  Preparing: SELECT mbywmc , zjzd , pxzd FROM mb_pzb WHERE ( ( sfqy = ? ) ) "}
{"@timestamp":"2025-07-28T01:26:33.427Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.MbPzbMapper.selectByExample","rest":"==> Parameters: 1(String)"}
{"@timestamp":"2025-07-28T01:26:33.436Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.MbPzbMapper.selectByExample","rest":"<==      Total: 10"}
{"@timestamp":"2025-07-28T01:26:33.439Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from gj_mzdmb order by DM asc "}
{"@timestamp":"2025-07-28T01:26:33.440Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-28T01:26:33.449Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 58"}
{"@timestamp":"2025-07-28T01:26:34.117Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from gj_sfzjdmb order by DM asc "}
{"@timestamp":"2025-07-28T01:26:34.117Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-28T01:26:34.124Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 12"}
{"@timestamp":"2025-07-28T01:26:34.269Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from jy_kskmdmb order by XM_DM,KM_DM asc "}
{"@timestamp":"2025-07-28T01:26:34.269Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-28T01:26:34.279Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 106"}
{"@timestamp":"2025-07-28T01:26:35.568Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from jy_rygwlxmb order by DM asc "}
{"@timestamp":"2025-07-28T01:26:35.568Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-28T01:26:35.575Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 3"}
{"@timestamp":"2025-07-28T01:26:35.618Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from jy_rygwzzmb order by SSGWM,DM asc "}
{"@timestamp":"2025-07-28T01:26:35.618Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-28T01:26:35.625Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 5"}
{"@timestamp":"2025-07-28T01:26:35.695Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from jy_wjlxmb order by DM asc "}
{"@timestamp":"2025-07-28T01:26:35.695Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-28T01:26:35.701Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 3"}
{"@timestamp":"2025-07-28T01:26:35.755Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from jy_wjxmmb order by LX_DM,XM_DM asc "}
{"@timestamp":"2025-07-28T01:26:35.756Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-28T01:26:35.764Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 20"}
{"@timestamp":"2025-07-28T01:26:36.007Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from jy_zcqswzdmb order by DM asc "}
{"@timestamp":"2025-07-28T01:26:36.008Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-28T01:26:36.015Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 4"}
{"@timestamp":"2025-07-28T01:26:36.067Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from jy_zwbjfsdmb order by DM asc "}
{"@timestamp":"2025-07-28T01:26:36.069Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-28T01:26:36.076Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 8"}
{"@timestamp":"2025-07-28T01:26:36.177Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from jy_zwplfsdmb order by DM asc "}
{"@timestamp":"2025-07-28T01:26:36.178Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-28T01:26:36.184Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 2"}
{"@timestamp":"2025-07-28T01:26:36.215Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####码表数据缓存初始化成功####"}
{"@timestamp":"2025-07-28T01:26:36.215Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####延迟队列初始化####"}
{"@timestamp":"2025-07-28T01:26:36.215Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"o.s.s.concurrent.ThreadPoolTaskExecutor","rest":"Initializing ExecutorService"}
{"@timestamp":"2025-07-28T01:26:36.216Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####延迟队列初始化成功####"}
{"@timestamp":"2025-07-28T01:26:36.216Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####数据下发任务初始化####"}
{"@timestamp":"2025-07-28T01:26:36.221Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.m.K.updateByExampleSelective","rest":"==>  Preparing: UPDATE ks_kssj_distribute_task SET complete = ?,complete_time = ? WHERE ( ( complete = ? ) ) "}
{"@timestamp":"2025-07-28T01:26:36.222Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.m.K.updateByExampleSelective","rest":"==> Parameters: 2(Integer), 2025-07-28 09:26:36.217(Timestamp), 1(Integer)"}
{"@timestamp":"2025-07-28T01:26:36.235Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.m.K.updateByExampleSelective","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-28T01:26:36.236Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####数据下发任务初始化成功####"}
{"@timestamp":"2025-07-28T01:26:36.236Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####数据库变更初始化####"}
{"@timestamp":"2025-07-28T01:26:36.236Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.service.impl.UtilsServiceImpl","rest":"dbName:[eeip_alone]"}
{"@timestamp":"2025-07-28T01:26:36.316Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-28T01:26:36.316Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksjh(String), cjlx(String)"}
{"@timestamp":"2025-07-28T01:26:36.324Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-28T01:26:36.325Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-28T01:26:36.325Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksjh(String), qydsxf(String)"}
{"@timestamp":"2025-07-28T01:26:36.332Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-28T01:26:36.333Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-28T01:26:36.333Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksjh(String), dsxfsj(String)"}
{"@timestamp":"2025-07-28T01:26:36.339Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-28T01:26:36.340Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-28T01:26:36.340Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksjh(String), dsxfzt(String)"}
{"@timestamp":"2025-07-28T01:26:36.347Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-28T01:26:36.349Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-28T01:26:36.349Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksjh(String), pack_jkrybpsj(String)"}
{"@timestamp":"2025-07-28T01:26:36.356Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-28T01:26:36.357Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-28T01:26:36.358Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksjh(String), pack_jkryjcsj(String)"}
{"@timestamp":"2025-07-28T01:26:36.364Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-28T01:26:36.364Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-28T01:26:36.365Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksjh(String), jkqdrs(String)"}
{"@timestamp":"2025-07-28T01:26:36.372Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-28T01:26:36.372Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-28T01:26:36.372Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksjh(String), dbms(String)"}
{"@timestamp":"2025-07-28T01:26:36.380Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-28T01:26:36.380Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-28T01:26:36.380Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_jkry_rcxx(String), jklsxh(String)"}
{"@timestamp":"2025-07-28T01:26:36.387Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-28T01:26:36.387Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-28T01:26:36.387Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_jkry_rcxx(String), xsd(String)"}
{"@timestamp":"2025-07-28T01:26:36.394Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-28T01:26:36.395Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-28T01:26:36.396Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_jkry_rcxx(String), sbzt(String)"}
{"@timestamp":"2025-07-28T01:26:36.403Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-28T01:26:36.403Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-28T01:26:36.403Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_jkry_rcxx(String), sbsj(String)"}
{"@timestamp":"2025-07-28T01:26:36.410Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-28T01:26:36.410Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-28T01:26:36.410Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_jkry_rcxx(String), tbzt(String)"}
{"@timestamp":"2025-07-28T01:26:36.417Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-28T01:26:36.417Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-28T01:26:36.418Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_jkry_rcxx(String), rgyzjg(String)"}
{"@timestamp":"2025-07-28T01:26:36.425Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-28T01:26:36.426Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.UtilsMapper.createCsDzgh","rest":"==>  Preparing: CREATE TABLE IF NOT EXISTS `cs_dzgh` ( `id` varchar(32) NOT NULL, `cslx` varchar(32) DEFAULT NULL COMMENT 'KD(考点) KC(考场)', `type` varchar(32) DEFAULT NULL COMMENT '类型KCWG(考场网关)', `classify` varchar(32) DEFAULT NULL COMMENT '分类DEVCFG(设备配置)', `key` varchar(32) DEFAULT NULL COMMENT 'key值（IP:ip地址,NETMASK:掩码,GATEWAY:网关IP地址,IPRANGE:ip范围,PCNUM:主机数）', `value` varchar(128) DEFAULT NULL COMMENT 'value值', `create_time` datetime DEFAULT NULL COMMENT '创建时间', `update_time` datetime DEFAULT NULL COMMENT '更新时间', `status` decimal(10,0) DEFAULT NULL COMMENT '1-启用 0-不启用', `sort` decimal(10,0) DEFAULT NULL COMMENT '排序', `jsh` varchar(32) DEFAULT NULL COMMENT '教室号', PRIMARY KEY (`id`), KEY `index_dzgh_1` (`jsh`,`value`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8 "}
{"@timestamp":"2025-07-28T01:26:36.427Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.UtilsMapper.createCsDzgh","rest":"==> Parameters: "}
{"@timestamp":"2025-07-28T01:26:36.439Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.UtilsMapper.createCsDzgh","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-28T01:26:36.440Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbIpdfp","rest":"==>  Preparing: CREATE TABLE IF NOT EXISTS `sb_ipdfp` ( `id` varchar(32) NOT NULL, `ipstart` varchar(32) DEFAULT NULL COMMENT 'IP起', `ipend` varchar(32) DEFAULT NULL COMMENT 'IP尾', `zwym` varchar(32) DEFAULT NULL COMMENT '子网掩码', `wg` varchar(32) DEFAULT NULL COMMENT '网关', `create_time` datetime DEFAULT NULL, `update_time` datetime DEFAULT NULL, PRIMARY KEY (`id`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 "}
{"@timestamp":"2025-07-28T01:26:36.440Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbIpdfp","rest":"==> Parameters: "}
{"@timestamp":"2025-07-28T01:26:36.453Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbIpdfp","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-28T01:26:36.454Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbSbjcb","rest":"==>  Preparing: CREATE TABLE IF NOT EXISTS `sb_sbjcb` ( `id` varchar(32) NOT NULL, `sbxxbh` varchar(32) DEFAULT NULL COMMENT '设备信息编号', `ip` varchar(32) DEFAULT NULL COMMENT 'ip', `xlh` varchar(128) DEFAULT NULL COMMENT '序列号', `detect_type` varchar(10) DEFAULT NULL COMMENT '检测类型1-ping 2-mqtt', `detect_result` varchar(10) DEFAULT NULL COMMENT '检测结果1-成功 -1001-ping失败 -2001-mqtt超时 -9999-其他错误', `detect_desc` varchar(255) DEFAULT NULL COMMENT '检测描述', `detect_exception` text COMMENT '检测异常', `detect_time` timestamp NULL DEFAULT NULL COMMENT '检测时间', PRIMARY KEY (`id`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 "}
{"@timestamp":"2025-07-28T01:26:36.454Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbSbjcb","rest":"==> Parameters: "}
{"@timestamp":"2025-07-28T01:26:36.468Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbSbjcb","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-28T01:26:36.469Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.m.UtilsMapper.createKsBmxxKstzz","rest":"==>  Preparing: CREATE TABLE IF NOT EXISTS `ks_bmxx_kstzz` ( `ksid` varchar(32) NOT NULL COMMENT '考生唯一标志号', `ksjhbh` varchar(32) NOT NULL COMMENT '考试计划标志号', `ksh` varchar(20) DEFAULT NULL COMMENT '考生号', `sfzjhm` varchar(20) NOT NULL COMMENT '身份证件号', `kszpid` varchar(128) DEFAULT NULL COMMENT '考生照片ID', `tzzzt` varchar(2) DEFAULT NULL COMMENT '0-未获取到特征值；1-已获取到特征值；2-获取特征值失败', `tzzid` varchar(128) DEFAULT NULL COMMENT '特征值ID', `tzzhqsj` datetime DEFAULT NULL COMMENT '特征值获取时间', `create_time` datetime DEFAULT NULL COMMENT '创建时间', `update_time` datetime DEFAULT NULL COMMENT '更新时间', `tzzfs` varchar(1) DEFAULT NULL COMMENT '特征值方式', `bmh` varchar(32) DEFAULT NULL COMMENT '报名号', PRIMARY KEY (`ksid`) USING BTREE ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='考生报名特征值' "}
{"@timestamp":"2025-07-28T01:26:36.469Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.m.UtilsMapper.createKsBmxxKstzz","rest":"==> Parameters: "}
{"@timestamp":"2025-07-28T01:26:36.482Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.m.UtilsMapper.createKsBmxxKstzz","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-28T01:26:36.482Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.m.UtilsMapper.createKsYdsbSbkszs","rest":"==>  Preparing: CREATE TABLE IF NOT EXISTS `ks_ydsb_sbkszs` ( `id` varchar(32) NOT NULL, `ksjhbh` varchar(32) NOT NULL COMMENT '考试计划编号', `sbxlh` varchar(64) NOT NULL COMMENT '设备序列号', `sbsj` datetime DEFAULT NULL COMMENT '上报时间', `kdmc` varchar(128) DEFAULT NULL COMMENT '考点名称', `sbkszs` int(10) DEFAULT NULL COMMENT '上报考生总数=入场人数+缺考人数', `create_time` datetime DEFAULT NULL COMMENT '创建时间', `update_time` datetime DEFAULT NULL COMMENT '更新时间', `ccm` varchar(10) DEFAULT NULL COMMENT '场次码', `kcbh` varchar(32) DEFAULT NULL COMMENT '考场编号', `ljkcbh` varchar(32) DEFAULT NULL COMMENT '逻辑考场编号', `sblx` varchar(10) DEFAULT NULL COMMENT '上报类型 BZ-标准 FB-非标', PRIMARY KEY (`id`), KEY `ks_ydsb_sbkszs__index_1` (`ksjhbh`,`ccm`,`sbxlh`,`kcbh`,`ljkcbh`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8 "}
{"@timestamp":"2025-07-28T01:26:36.483Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.m.UtilsMapper.createKsYdsbSbkszs","rest":"==> Parameters: "}
{"@timestamp":"2025-07-28T01:26:36.495Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.m.UtilsMapper.createKsYdsbSbkszs","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-28T01:26:36.496Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.m.UtilsMapper.createBizCsxxExt","rest":"==>  Preparing: create table if not exists biz_csxx_ext ( CSBH varchar(64) not null comment '场所编号' primary key, SBXLH varchar(128) null comment '设备序列号', YDZD_APP_VERSION varchar(128) null comment '移动终端app版本', ZHSBSJ datetime null comment '最后上报时间', XFZT int null comment '0-失败 1-下发中 2-成功' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 "}
{"@timestamp":"2025-07-28T01:26:36.496Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.m.UtilsMapper.createBizCsxxExt","rest":"==> Parameters: "}
{"@timestamp":"2025-07-28T01:26:36.509Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.m.UtilsMapper.createBizCsxxExt","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-28T01:26:36.510Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.m.UtilsMapper.createBizFsgjTask","rest":"==>  Preparing: create table if not exists biz_fsgj_task ( ID varchar(32) not null primary key, NAME varchar(128) null comment '任务名称', T_TYPE varchar(10) null comment '任务类型 1-移动终端APP', T_PROGRESS varchar(10) null comment '进度', COMPLETE int(10) null comment '完成状态 -1-失败 0-开始 1-进行中 2-完成', T_PROGRESS_DESC varchar(255) null comment '任务描述', T_PARAM longtext null comment '任务参数' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 "}
{"@timestamp":"2025-07-28T01:26:36.510Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.m.UtilsMapper.createBizFsgjTask","rest":"==> Parameters: "}
{"@timestamp":"2025-07-28T01:26:36.523Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.m.UtilsMapper.createBizFsgjTask","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-28T01:26:36.523Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.m.UtilsMapper.createBizFsgjTaskJg","rest":"==>  Preparing: create table if not exists biz_fsgj_task_jg ( ID varchar(32) not null primary key, TASK_ID varchar(32) null comment '任务id', CSBH varchar(64) null comment '场所编号', SBXLH varchar(64) null comment '设备序列号' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 "}
{"@timestamp":"2025-07-28T01:26:36.523Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.m.UtilsMapper.createBizFsgjTaskJg","rest":"==> Parameters: "}
{"@timestamp":"2025-07-28T01:26:36.536Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.m.UtilsMapper.createBizFsgjTaskJg","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-28T01:26:36.538Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.UtilsMapper.filterTables","rest":"==>  Preparing: select table_name from information_schema.tables where table_schema= ? AND table_name in ( ? , ? ) "}
{"@timestamp":"2025-07-28T01:26:36.538Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.UtilsMapper.filterTables","rest":"==> Parameters: eeip_alone(String), biz_send_command(String), biz_send_command_dev(String)"}
{"@timestamp":"2025-07-28T01:26:36.547Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.UtilsMapper.filterTables","rest":"<==      Total: 2"}
{"@timestamp":"2025-07-28T01:26:36.547Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-28T01:26:36.547Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), sb_sbxx(String), osbb(String)"}
{"@timestamp":"2025-07-28T01:26:36.554Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-28T01:26:36.555Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-28T01:26:36.555Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), sb_sbxx(String), ntp_enable(String)"}
{"@timestamp":"2025-07-28T01:26:36.562Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-28T01:26:36.562Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-28T01:26:36.562Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), sb_sbxx(String), ntp_source(String)"}
{"@timestamp":"2025-07-28T01:26:36.570Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-28T01:26:36.570Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-28T01:26:36.571Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), sb_sbxx(String), ntp_interval(String)"}
{"@timestamp":"2025-07-28T01:26:36.577Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-28T01:26:36.578Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbsj","rest":"==>  Preparing: create table if not exists sb_sbsj ( id varchar(32) charset utf8 not null primary key, sbxlh varchar(128) charset utf8 not null comment '设备序列号', sblx varchar(32) charset utf8 not null comment 'kdwg考点网关 kcwg考场网关 ydzd移动终端', event_type varchar(16) charset utf8 not null comment 'ONLINE-上线事件 USB-usb事件 WIFI-wifi事件 YDZDBATTERY-移动终端电池事件', event_time datetime not null comment '事件时间', csbh varchar(32) charset utf8 null comment '场所编号', event_detail text charset utf8 null comment '事件详情', create_time datetime null comment '创建时间', update_time datetime null comment '更新时间', status varchar(4) charset utf8 null comment '状态', `repeat` varchar(4) charset utf8 null comment '是否重复', repeat_count int(10) null comment '重复次数 ', `desc` text charset utf8 null comment '描述', param1 varchar(64) charset utf8 null comment '扩展1', param2 varchar(64) charset utf8 null comment '扩展2', param3 varchar(64) charset utf8 null comment '扩展3', event_time_start datetime null comment '事件开始时间', event_time_end datetime null comment '事件结束时间' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='设备事件' "}
{"@timestamp":"2025-07-28T01:26:36.578Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbsj","rest":"==> Parameters: "}
{"@timestamp":"2025-07-28T01:26:36.591Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbsj","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-28T01:26:36.592Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-28T01:26:36.592Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksrcxx(String), tbzt(String)"}
{"@timestamp":"2025-07-28T01:26:36.599Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-28T01:26:36.600Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.UtilsMapper.alterColumn","rest":"==>  Preparing: ALTER TABLE ks_ksrcxx modify COLUMN tbzt varchar(2) COMMENT ?; "}
{"@timestamp":"2025-07-28T01:26:36.600Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.UtilsMapper.alterColumn","rest":"==> Parameters: 同步状态(String)"}
{"@timestamp":"2025-07-28T01:26:36.617Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.UtilsMapper.alterColumn","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-28T01:26:36.617Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.service.impl.UtilsServiceImpl","rest":"数据库dbName:[eeip_alone] tbName:[ks_ksrcxx] columName:[tbzt]变更"}
{"@timestamp":"2025-07-28T01:26:36.617Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-28T01:26:36.618Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksrcxx(String), xsd(String)"}
{"@timestamp":"2025-07-28T01:26:36.625Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-28T01:26:36.626Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-28T01:26:36.626Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksrcxx(String), sc_sfzp(String)"}
{"@timestamp":"2025-07-28T01:26:36.634Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-28T01:26:36.634Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-28T01:26:36.635Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksrcxx(String), sc_rlzp(String)"}
{"@timestamp":"2025-07-28T01:26:36.641Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-28T01:26:36.642Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####数据库变更初始化成功####"}
{"@timestamp":"2025-07-28T01:26:36.642Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####缓存校级身份核验平台配置信息缓存成功####"}
{"@timestamp":"2025-07-28T01:26:36.642Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Succ<<<<<<<<<<<<<##"}
{"@timestamp":"2025-07-28T01:26:37.415Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"Thread-70","class":"com.xcwlkj.msgque.que.XcRocektMqConsumer","rest":"监听：JKYT_ATTENDANCE_JTXX,启动成功！"}
{"@timestamp":"2025-07-28T01:26:37.493Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"RMI TCP Connection(4)-**************","class":"o.s.web.servlet.DispatcherServlet","rest":"Initializing Servlet 'dispatcherServlet'"}
{"@timestamp":"2025-07-28T01:26:37.524Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"RMI TCP Connection(4)-**************","class":"o.s.web.servlet.DispatcherServlet","rest":"Completed initialization in 31 ms"}
{"@timestamp":"2025-07-28T01:26:37.919Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"RMI TCP Connection(2)-**************","class":"o.s.c.c.c.ConfigServicePropertySourceLocator","rest":"Fetching config from server at : http://localhost:8888"}
{"@timestamp":"2025-07-28T01:26:38.186Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"RMI TCP Connection(2)-**************","class":"o.s.c.c.c.ConfigServicePropertySourceLocator","rest":"Could not locate PropertySource: label not found"}
{"@timestamp":"2025-07-28T01:26:40.425Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"Thread-71","class":"com.xcwlkj.msgque.que.XcRocektMqConsumer","rest":"监听：JKYT_RSTJ_XXTS,启动成功！"}
{"@timestamp":"2025-07-28T01:26:47.811Z","severity":"INFO","service":"eeip-standalone-service","trace":"a7ae5b29a77621a5","span":"67e83dac82d6fb5d","parent":"a75963183f75eccb","exportable":"true","pid":"12424","thread":"ThreadPoolTaskExecutor-1","class":"c.x.b.u.m.service.impl.MqttMsgRevHandler","rest":"GenericMessage [payload={\"data\":{\"OPERATION\":\"NOTIFY_DEVEVENT_YDZDBATTERY\",\"TIMESTAMP\":1753666005596,\"Data\":{\"devType\":\"172\",\"sbcsbh\":\"K3307820091100001\",\"eventTime\":\"1753666005596\",\"batteryVal\":100,\"eventType\":\"YDZDBATTERY\",\"sn\":\"KEP20240707001\"}},\"sign\":\"\",\"messageId\":\"batteryVal_1753666005596_KEP20240707001\",\"timestamp\":1753666005596,\"token\":\"ee98d86d3ccc48a2a366770e8ffb7567\"}, headers={mqtt_receivedRetained=false, spanTraceId=a7ae5b29a77621a5, spanId=a7ae5b29a77621a5, nativeHeaders={spanTraceId=[a7ae5b29a77621a5], spanId=[a7ae5b29a77621a5], spanSampled=[1]}, mqtt_duplicate=false, id=5db73daf-1663-0fa8-7a9b-eeba4a2aaf48, spanSampled=1, mqtt_receivedTopic=/HISOME_ANDROID/KEP20240707001/event/REPORT_DEVEVENT, mqtt_receivedQos=1}]"}
{"@timestamp":"2025-07-28T01:26:47.817Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"ThreadPoolTaskExecutor-2","class":"c.x.r.s.impl.BasicMqttMsgRevHandler","rest":"topic[/HISOME_ANDROID/KEP20240707001/event/REPORT_DEVEVENT] 没有方法可以处理这个主题..."}
{"@timestamp":"2025-07-28T01:26:47.817Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.service.impl.BizMqttMsgRevHandler","rest":"topic[/HISOME_ANDROID/KEP20240707001/event/REPORT_DEVEVENT] 没有方法可以处理这个主题..."}
{"@timestamp":"2025-07-28T01:26:47.818Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"ThreadPoolTaskExecutor-1","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"judgeDeviceIdExist:KEP20240707001"}
{"@timestamp":"2025-07-28T01:26:47.824Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"ThreadPoolTaskExecutor-1","class":"c.x.i.s.i.m.MqttReportDeveventEvantHandler","rest":"处理mqtt上报设备事件消息:BaseOperationReq(operation=NOTIFY_DEVEVENT_YDZDBATTERY, timestamp=1753666005596, data={\"devType\":\"172\",\"sbcsbh\":\"K3307820091100001\",\"eventTime\":\"1753666005596\",\"batteryVal\":100,\"eventType\":\"YDZDBATTERY\",\"sn\":\"KEP20240707001\"})"}
{"@timestamp":"2025-07-28T01:26:47.844Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"ThreadPoolTaskExecutor-1","class":"c.x.i.m.S.updateZxztAndNtpByXlh","rest":"==>  Preparing: update sb_sbxx SET zxzt = ?, battery = ?, update_time = now() where xlh = ? "}
{"@timestamp":"2025-07-28T01:26:47.845Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"ThreadPoolTaskExecutor-1","class":"c.x.i.m.S.updateZxztAndNtpByXlh","rest":"==> Parameters: 1(String), 100(String), KEP20240707001(String)"}
{"@timestamp":"2025-07-28T01:26:47.861Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"ThreadPoolTaskExecutor-1","class":"c.x.i.m.S.updateZxztAndNtpByXlh","rest":"<==    Updates: 6"}
{"@timestamp":"2025-07-28T01:26:47.863Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"ThreadPoolTaskExecutor-1","class":"c.x.i.m.SbSbxxMapper.selectByExample","rest":"==>  Preparing: SELECT sbxxbh,sbmc,sbxh,sbbm,sbpp,sbcs,sblx,sblxmc,jspc,jssj,sbccrq,sbazrq,sbzbq,sbwldz,macdz,ipdz,dkh,czxt,kzdkh,appbb,firmwarebb,xlh,ramdiskbb,sbzt,jhzt,sbmy,sczt,zxzt,sbzpid,sbqm,sbjcjg,zhzxsj,create_time,update_time,sbztw,sfydsb,osbb,battery,ntp_enable,ntp_source,ntp_interval,ext_status FROM sb_sbxx WHERE ( ( sczt = ? and xlh = ? ) ) "}
{"@timestamp":"2025-07-28T01:26:47.864Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"ThreadPoolTaskExecutor-1","class":"c.x.i.m.SbSbxxMapper.selectByExample","rest":"==> Parameters: 0(String), KEP20240707001(String)"}
{"@timestamp":"2025-07-28T01:26:47.874Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"ThreadPoolTaskExecutor-1","class":"c.x.i.m.SbSbxxMapper.selectByExample","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-28T01:26:47.876Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"ThreadPoolTaskExecutor-1","class":"c.x.i.s.i.m.MqttReportDeveventEvantHandler","rest":"上报电量大于阈值, 不记录日志"}
{"@timestamp":"2025-07-28T01:26:58.452Z","severity":"INFO","service":"eeip-standalone-service","trace":"360144c01d7bb313","span":"360144c01d7bb313","parent":"","exportable":"false","pid":"12424","thread":"http-nio-8888-exec-2","class":"c.x.i.facade.manager.CxtjController","rest":"收到请求开始：[紧急呼叫查询][/manager/identityverify/cxtj/jjhjcx]reqModel:JjhjCxReq[ksjhbh=2507211439231396864138952572928,kcbh=<null>,clzt=<null>,startTime=<null>,endTime=<null>,pageNum=1,pageSize=10,msgId=<null>,transChannel=<null>,channelSerialNo=<null>,channelDate=<null>,channelTime=<null>,token=<null>,loginToken=<null>,transCode=<null>,clientIp=<null>,merchantId=<null>,appPackage=<null>]"}
{"@timestamp":"2025-07-28T01:26:58.489Z","severity":"INFO","service":"eeip-standalone-service","trace":"360144c01d7bb313","span":"360144c01d7bb313","parent":"","exportable":"false","pid":"12424","thread":"http-nio-8888-exec-2","class":"c.x.i.s.impl.KsKwMessageServiceImpl","rest":"紧急呼叫查询开始，查询条件：JjhjCxDTO[ksjhbh=2507211439231396864138952572928,kcbh=<null>,clzt=<null>,startTime=<null>,endTime=<null>,pageNum=1,pageSize=10]"}
{"@timestamp":"2025-07-28T01:26:58.545Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"360144c01d7bb313","span":"360144c01d7bb313","parent":"","exportable":"false","pid":"12424","thread":"http-nio-8888-exec-2","class":"c.x.i.m.K.selectJjhjList_COUNT","rest":"==>  Preparing: SELECT count(0) FROM ks_kw_message msg LEFT JOIN ks_kw_message reply ON msg.ksjhbh = reply.ksjhbh AND msg.id = reply.replay_id AND reply.sczt = '0' WHERE msg.sczt = '0' AND msg.msg_type = '2000' AND msg.origin_app_type = 'HYAPP' AND msg.operate = '0' AND msg.ksjhbh = ? "}
{"@timestamp":"2025-07-28T01:26:58.545Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"360144c01d7bb313","span":"360144c01d7bb313","parent":"","exportable":"false","pid":"12424","thread":"http-nio-8888-exec-2","class":"c.x.i.m.K.selectJjhjList_COUNT","rest":"==> Parameters: 2507211439231396864138952572928(String)"}
{"@timestamp":"2025-07-28T01:26:58.552Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"360144c01d7bb313","span":"360144c01d7bb313","parent":"","exportable":"false","pid":"12424","thread":"http-nio-8888-exec-2","class":"c.x.i.m.K.selectJjhjList_COUNT","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-28T01:26:58.555Z","severity":"INFO","service":"eeip-standalone-service","trace":"360144c01d7bb313","span":"360144c01d7bb313","parent":"","exportable":"false","pid":"12424","thread":"http-nio-8888-exec-2","class":"c.x.i.s.impl.KsKwMessageServiceImpl","rest":"紧急呼叫查询结束，共查询到0条记录"}
{"@timestamp":"2025-07-28T01:26:58.582Z","severity":"INFO","service":"eeip-standalone-service","trace":"360144c01d7bb313","span":"360144c01d7bb313","parent":"","exportable":"false","pid":"12424","thread":"http-nio-8888-exec-2","class":"c.x.i.facade.manager.CxtjController","rest":"处理请求结束：[紧急呼叫查询][/manager/identityverify/cxtj/jjhjcx]reqModel:JjhjCxReq[ksjhbh=2507211439231396864138952572928,kcbh=<null>,clzt=<null>,startTime=<null>,endTime=<null>,pageNum=1,pageSize=10,msgId=<null>,transChannel=<null>,channelSerialNo=<null>,channelDate=<null>,channelTime=<null>,token=<null>,loginToken=<null>,transCode=<null>,clientIp=<null>,merchantId=<null>,appPackage=<null>],respModel:JjhjCxResp[data=Page{count=true, pageNum=1, pageSize=10, startRow=0, endRow=10, total=0, pages=0, reasonable=true, pageSizeZero=false}[],totalRows=0,pageNum=1,pageSize=10,msgId=<null>,transChannel=<null>,channelSerialNo=<null>,transCode=<null>,respDate=<null>,respTime=<null>,ext=<null>]"}
{"@timestamp":"2025-07-28T01:27:21.855Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"idvScheduleTask-pool-0","class":"c.x.i.m.AttachmentMapper.selectByExample","rest":"==>  Preparing: SELECT id,version,created_by,creator_id,created_time,last_operator,last_operator_id,last_operator_time,ref_no,bucket_name,attachment_define_id,original_name,new_file_name,thumb_file_name,path,thumb_path,type,description,expire_time FROM attachment WHERE ( ( expire_time <= ? ) ) "}
{"@timestamp":"2025-07-28T01:27:21.855Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"idvScheduleTask-pool-0","class":"c.x.i.m.AttachmentMapper.selectByExample","rest":"==> Parameters: 2025-07-28 09:27:21(String)"}
{"@timestamp":"2025-07-28T01:27:21.867Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"idvScheduleTask-pool-0","class":"c.x.i.m.AttachmentMapper.selectByExample","rest":"<==      Total: 0"}
{"@timestamp":"2025-07-28T01:28:00.006Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"quartzScheduler_Worker-8","class":"c.x.i.util.schedule.ScheduleJobExecutor","rest":"任务准备执行，任务ID：23110110740401808639620095703040"}
{"@timestamp":"2025-07-28T01:28:00.023Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"quartzScheduler_Worker-8","class":"c.x.i.util.schedule.ScheduleJobExecutor","rest":"任务执行完毕，任务ID：23110110740401808639620095703040  总共耗时：17毫秒"}
{"@timestamp":"2025-07-28T01:28:00.026Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"quartzScheduler_Worker-8","class":"c.x.i.m.S.insertSelective","rest":"==>  Preparing: INSERT INTO schedule_job_log ( log_id,job_id,job_name,bean_name,result,times,create_time ) VALUES( ?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-28T01:28:00.026Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"quartzScheduler_Worker-8","class":"c.x.i.m.S.insertSelective","rest":"==> Parameters: 25072809280002268833922467001344(String), 23110110740401808639620095703040(String), jobOnlineStatus(String), onlineStatusTaskService(String), 0(Integer), 17(Integer), 2025-07-28 09:28:00.006(Timestamp)"}
{"@timestamp":"2025-07-28T01:28:01.467Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"MQTT Rec: identityVerify_test01","class":"o.s.i.m.outbound.MqttPahoMessageHandler","rest":"Lost connection; will attempt reconnect on next request"}
{"@timestamp":"2025-07-28T01:30:00.003Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"quartzScheduler_Worker-9","class":"c.x.i.util.schedule.ScheduleJobExecutor","rest":"任务准备执行，任务ID：23110111540401808639620095703040"}
{"@timestamp":"2025-07-28T01:30:00.004Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"quartzScheduler_Worker-10","class":"c.x.i.util.schedule.ScheduleJobExecutor","rest":"任务准备执行，任务ID：23112811035901828218591246338048"}
{"@timestamp":"2025-07-28T01:30:00.004Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"quartzScheduler_Worker-3","class":"c.x.i.util.schedule.ScheduleJobExecutor","rest":"任务准备执行，任务ID：24081610292802018092458168076288"}
{"@timestamp":"2025-07-28T01:30:00.019Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"quartzScheduler_Worker-10","class":"c.x.i.m.J.selectOneByExample","rest":"==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) "}
{"@timestamp":"2025-07-28T01:30:00.019Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"quartzScheduler_Worker-10","class":"c.x.i.m.J.selectOneByExample","rest":"==> Parameters: HISOME_subDevType(String)"}
{"@timestamp":"2025-07-28T01:30:00.029Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"quartzScheduler_Worker-10","class":"c.x.i.m.J.selectOneByExample","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-28T01:30:00.029Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"quartzScheduler_Worker-9","class":"c.x.i.m.SbSbxxMapper.selectByExample","rest":"==>  Preparing: SELECT sbxxbh,sbmc,sbxh,sbbm,sbpp,sbcs,sblx,sblxmc,jspc,jssj,sbccrq,sbazrq,sbzbq,sbwldz,macdz,ipdz,dkh,czxt,kzdkh,appbb,firmwarebb,xlh,ramdiskbb,sbzt,jhzt,sbmy,sczt,zxzt,sbzpid,sbqm,sbjcjg,zhzxsj,create_time,update_time,sbztw,sfydsb,osbb,battery,ntp_enable,ntp_source,ntp_interval,ext_status FROM sb_sbxx WHERE ( ( sczt = ? ) ) "}
{"@timestamp":"2025-07-28T01:30:00.029Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"quartzScheduler_Worker-9","class":"c.x.i.m.SbSbxxMapper.selectByExample","rest":"==> Parameters: 0(String)"}
{"@timestamp":"2025-07-28T01:30:00.045Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"quartzScheduler_Worker-9","class":"c.x.i.m.SbSbxxMapper.selectByExample","rest":"<==      Total: 16"}
{"@timestamp":"2025-07-28T01:30:00.045Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"quartzScheduler_Worker-3","class":"c.x.i.m.KsKsjhMapper.selectByExample","rest":"==>  Preparing: SELECT ksjhbh,mc,kssj,jssj,xn,xq,sfmr,kszt,sbjm,sbkqsj,sbgbsj,cjlx,kslx,qysbbmd,wifiqy,cjsj,xgsj,scztw,kzqy,qydsxf,dsxfsj,dsxfzt,jkqdrs,dbms FROM ks_ksjh WHERE ( ( scztw = ? and qydsxf = ? and dsxfzt = ? ) ) "}
{"@timestamp":"2025-07-28T01:30:00.045Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"quartzScheduler_Worker-3","class":"c.x.i.m.KsKsjhMapper.selectByExample","rest":"==> Parameters: 0(String), 1(Integer), 0(Integer)"}
{"@timestamp":"2025-07-28T01:30:00.059Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"quartzScheduler_Worker-9","class":"c.x.i.t.u.h.s.i.UnifyAccessServiceImpl","rest":"url http://*************:8844/device/category?terms[0].column=parentId&terms[0].value=-5-"}
{"@timestamp":"2025-07-28T01:30:00.269Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"quartzScheduler_Worker-3","class":"c.x.i.m.KsKsjhMapper.selectByExample","rest":"<==      Total: 0"}
{"@timestamp":"2025-07-28T01:30:00.269Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"quartzScheduler_Worker-3","class":"c.x.i.s.i.s.KsjhSjdsxfTaskServiceImpl","rest":"当前无下发任务！"}
{"@timestamp":"2025-07-28T01:30:00.269Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"quartzScheduler_Worker-10","class":"c.x.i.m.S.selectBySubStatusAndType","rest":"==>  Preparing: SELECT xlh FROM sb_sbxx WHERE sczt = '0' AND sbztw = '0' AND sblx IN ( ? , ? ) "}
{"@timestamp":"2025-07-28T01:30:00.269Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"quartzScheduler_Worker-3","class":"c.x.i.util.schedule.ScheduleJobExecutor","rest":"任务执行完毕，任务ID：24081610292802018092458168076288  总共耗时：265毫秒"}
{"@timestamp":"2025-07-28T01:30:00.274Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"quartzScheduler_Worker-10","class":"c.x.i.m.S.selectBySubStatusAndType","rest":"==> Parameters: kcdzbp(String), ydzd(String)"}
{"@timestamp":"2025-07-28T01:30:00.280Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"quartzScheduler_Worker-10","class":"c.x.i.m.S.selectBySubStatusAndType","rest":"<==      Total: 3"}
{"@timestamp":"2025-07-28T01:30:00.280Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"quartzScheduler_Worker-10","class":"c.x.i.s.i.s.SbxxReportTaskServiceImpl","rest":"设备信息上报，上报类型：[kcdzbp, ydzd]，上报设备序列号：[testYdzdXlh001, testYdzdXlh002, testYdzd004]"}
{"@timestamp":"2025-07-28T01:30:00.280Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"quartzScheduler_Worker-3","class":"c.x.i.m.S.insertSelective","rest":"==>  Preparing: INSERT INTO schedule_job_log ( log_id,job_id,job_name,bean_name,params,result,times,create_time ) VALUES( ?,?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-28T01:30:00.280Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"quartzScheduler_Worker-3","class":"c.x.i.m.S.insertSelective","rest":"==> Parameters: 25072809300002268834929083184129(String), 24081610292802018092458168076288(String), ksjhSjdsxf(String), ksjhSjdsxfTaskService(String), (String), 0(Integer), 265(Integer), 2025-07-28 09:30:00.004(Timestamp)"}
{"@timestamp":"2025-07-28T01:30:00.285Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"quartzScheduler_Worker-10","class":"c.x.i.m.S.getDevicePlaceRelation","rest":"==>  Preparing: SELECT sb_sbxx.xlh AS sbxlh, sb_sbxx.sbmc AS sbmc, sb_sbxx.sblx AS sblb, sb_sbxx.sbzbq AS sbzbjzsj, sb_sbxx.jssj AS cgsj, sb_sbxx.sbcs AS sbcsm, sb_sbxx.sbpp AS sbpp, sb_sbxx.sbzt AS ywzt, sb_sbxx.ipdz AS sbip, sb_sbxx.sfydsb AS sfydsb, csgx.csbh AS csdm, sb_sbxx.sbmy AS sbmy FROM sb_sbxx INNER JOIN sb_sbcsgx csgx ON sb_sbxx.sbxxbh = csgx.sbbh WHERE sb_sbxx.sczt = '0' AND sb_sbxx.sbzt != '3' AND sb_sbxx.xlh IN ( ? , ? , ? ) "}
{"@timestamp":"2025-07-28T01:30:00.285Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"quartzScheduler_Worker-10","class":"c.x.i.m.S.getDevicePlaceRelation","rest":"==> Parameters: testYdzdXlh001(String), testYdzdXlh002(String), testYdzd004(String)"}
{"@timestamp":"2025-07-28T01:30:00.295Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"quartzScheduler_Worker-10","class":"c.x.i.m.S.getDevicePlaceRelation","rest":"<==      Total: 0"}
{"@timestamp":"2025-07-28T01:30:00.295Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"quartzScheduler_Worker-10","class":"c.x.i.m.CsXxjbxxMapper.selectByExample","rest":"==>  Preparing: SELECT xxdm,xxmc,zzjgm,xxdz,xzqhm FROM cs_xxjbxx "}
{"@timestamp":"2025-07-28T01:30:00.295Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"quartzScheduler_Worker-10","class":"c.x.i.m.CsXxjbxxMapper.selectByExample","rest":"==> Parameters: "}
{"@timestamp":"2025-07-28T01:30:00.300Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"quartzScheduler_Worker-10","class":"c.x.i.m.CsXxjbxxMapper.selectByExample","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-28T01:30:00.300Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"quartzScheduler_Worker-10","class":"c.x.i.service.impl.SbSbxxServiceImpl","rest":"没有需要上报的设备"}
{"@timestamp":"2025-07-28T01:30:00.300Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"quartzScheduler_Worker-10","class":"c.x.i.util.schedule.ScheduleJobExecutor","rest":"任务执行完毕，任务ID：23112811035901828218591246338048  总共耗时：296毫秒"}
{"@timestamp":"2025-07-28T01:30:00.305Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"quartzScheduler_Worker-10","class":"c.x.i.m.S.insertSelective","rest":"==>  Preparing: INSERT INTO schedule_job_log ( log_id,job_id,job_name,bean_name,params,result,times,create_time ) VALUES( ?,?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-28T01:30:00.305Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"quartzScheduler_Worker-10","class":"c.x.i.m.S.insertSelective","rest":"==> Parameters: 25072809300002268834929083184128(String), 23112811035901828218591246338048(String), 设备信息上报(String), sbxxReportTaskService(String), (String), 0(Integer), 296(Integer), 2025-07-28 09:30:00.004(Timestamp)"}
{"@timestamp":"2025-07-28T01:30:00.406Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"quartzScheduler_Worker-9","class":"c.x.i.t.u.h.s.i.UnifyAccessServiceImpl","rest":"发送给接入返回结果result=JetlinksWrapper(message=success, status=200, code=200, timestamp=1753666199247, result=[DeviceCategoryRespModel(id=AIEdgeServer, parentId=-5-, key=AIEdgeServer, name=边缘计算服务器, productId=HISOME-DX000, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1747805356879, productName=汉尚多模态超脑), DeviceCategoryRespModel(id=cheating_prevention, parentId=-5-, key=CheatingPrevention, name=作弊防控设备, productId=HISOME-RRS3000, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1655261812143, productName=3536侦测服务器), DeviceCategoryRespModel(id=digital_hd_matrix, parentId=-5-, key=digital_hd_matrix, name=数字高清矩阵, productId=null, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1692077761801, productName=null), DeviceCategoryRespModel(id=ejjksb, parentId=-5-, key=ejjksb, name=耳机监考设备, productId=CENC_HEADPHONE, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1739411557430, productName=头戴式耳机), DeviceCategoryRespModel(id=hdd_box, parentId=-5-, key=hdd_box, name=视频存储盒子, productId=HISOME-HDD4000, sortIndex=1, level=2, creatorId=1199596756811550720, createTime=1680154724527, productName=视频数据存储终端), DeviceCategoryRespModel(id=hskpjmwxpb, parentId=-5-, key=hskpjmwxpb, name=汉尚考培加密无线平板, productId=null, sortIndex=1, level=1, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1740365256141, productName=null), DeviceCategoryRespModel(id=ipc, parentId=-5-, key=ipc, name=网络摄像机, productId=HISOME-IPC, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1691550353555, productName=IPC通用摄像机), DeviceCategoryRespModel(id=kcdzbp, parentId=-5-, key=kcdzbp, name=考场电子班牌, productId=PROD-DZBP, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1697418220328, productName=电子班牌), DeviceCategoryRespModel(id=kcwg, parentId=-5-, key=kcwg, name=考场网关, productId=CENCGW100_C, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1661844529888, productName=智能教室网关), DeviceCategoryRespModel(id=kdwg, parentId=-5-, key=kdwg, name=考点网关, productId=CENCGW100_S, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1661844519215, productName=智能校级网关), DeviceCategoryRespModel(id=lhsjkydzd, parentId=-5-, key=lhsjkydzd, name=理化生机考移动终端, productId=HISOME-PAD-101, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1740365218610, productName=汉尚理化生机考移动终端), DeviceCategoryRespModel(id=nvr, parentId=-5-, key=nvr, name=数字硬盘录像机, productId=HISOME-NVR4000, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1687656114753, productName=数字硬盘录像机), DeviceCategoryRespModel(id=sip, parentId=-5-, key=sip, name=SIP路由分发服务器, productId=SIP8000, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1689045772782, productName=SIP路由分发服务器), DeviceCategoryRespModel(id=tzsxj, parentId=-5-, key=tzsxj, name=同轴摄像机, productId=null, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1691552091707, productName=null), DeviceCategoryRespModel(id=ydzd, parentId=-5-, key=ydzd, name=移动终端, productId=HISOME_ANDROID, sortIndex=1, level=1, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1661844790208, productName=身份核验移动终端)])"}
{"@timestamp":"2025-07-28T01:30:00.409Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"quartzScheduler_Worker-9","class":"c.x.i.service.impl.SbSbxxServiceImpl","rest":""}
{"@timestamp":"2025-07-28T01:30:00.418Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"quartzScheduler_Worker-9","class":"c.x.i.t.u.h.s.i.UnifyAccessServiceImpl","rest":"Emqx消息桥接查询订阅 url=http://*************:8844/remote/access/emqx/subscribers/list"}
{"@timestamp":"2025-07-28T01:30:00.459Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"quartzScheduler_Worker-9","class":"c.x.i.t.u.h.s.i.UnifyAccessServiceImpl","rest":"Emqx消息桥接查询订阅 result=Wrapper(code=200, message=success, result=EmqxSubscribersListRespModel(subscribers=[/HISOME_ANDROID/testYdzdXlh001/function/invoke, /CENCGW100_C/DENCGW829_C04MB10BDJ027590/function/invoke, /HISOME_ANDROID/testYdzd004/function/invoke, /PROD-DZBP/test0012/function/invoke, /PROD-DZBP/FC1836988/function/invoke, /HISOME_ANDROID/ddcdb3ac5810283f/function/invoke, /PROD-DZBP/G04754406/properties/read, /PROD-DZBP/FC1836989/function/invoke, /HISOME_ANDROID/KEP20240707001/function/invoke, /PROD-DZBP/AB1602853/function/invoke, /HISOME_ANDROID/testYdzdXlh002/function/invoke, /CENCGW100_S/CENCGW100_SIV1c9f717de4acfc99/function/invoke, /CENCGW100_S/CENCGW100_SIVb33ad3ede4acfc99/function/invoke, /PROD-DZBP/AB1602853/properties/read, /HISOME_ANDROID/ATN240303000100YT55YBF/function/invoke, /PROD-DZBP/G16733836/function/invoke, /HISOME_ANDROID/S30SZA2023140181/function/invoke, /CENCGW100_C/DENCGW829_C04MB10BDJ07548e/function/invoke, /PROD-DZBP/G04754406/function/invoke, /CENCGW100_S/CENCGW100_SIVb770d1a6a1ec5b44/function/invoke]))"}
{"@timestamp":"2025-07-28T01:30:00.465Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"quartzScheduler_Worker-9","class":"c.x.i.util.schedule.ScheduleJobExecutor","rest":"任务执行完毕，任务ID：23110111540401808639620095703040  总共耗时：462毫秒"}
{"@timestamp":"2025-07-28T01:30:00.469Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"quartzScheduler_Worker-9","class":"c.x.i.m.S.insertSelective","rest":"==>  Preparing: INSERT INTO schedule_job_log ( log_id,job_id,job_name,bean_name,result,times,create_time ) VALUES( ?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-28T01:30:00.469Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"quartzScheduler_Worker-9","class":"c.x.i.m.S.insertSelective","rest":"==> Parameters: 25072809300002268834929074795520(String), 23110111540401808639620095703040(String), jobSubscribeMqtt(String), subscriberDevInfoToMqttTaskService(String), 0(Integer), 462(Integer), 2025-07-28 09:30:00.003(Timestamp)"}
{"@timestamp":"2025-07-28T01:33:34.419Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"Thread-129","class":"o.s.c.n.e.s.EurekaServiceRegistry","rest":"Unregistering application EEIP-STANDALONE-SERVICE with eureka with status DOWN"}
{"@timestamp":"2025-07-28T01:33:38.449Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"Thread-129","class":"o.s.i.m.i.MqttPahoMessageDrivenChannelAdapter","rest":"stopped inbound"}
{"@timestamp":"2025-07-28T01:33:38.450Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"Thread-129","class":"o.s.i.g.GatewayProxyFactoryBean$MethodInvocationGateway","rest":"stopped mqttGateway"}
{"@timestamp":"2025-07-28T01:33:38.450Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"Thread-129","class":"o.s.i.gateway.GatewayProxyFactoryBean","rest":"stopped mqttGateway"}
{"@timestamp":"2025-07-28T01:33:38.450Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"Thread-129","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel"}
{"@timestamp":"2025-07-28T01:33:38.451Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"Thread-129","class":"o.s.i.channel.PublishSubscribeChannel","rest":"Channel 'application-1.errorChannel' has 0 subscriber(s)."}
{"@timestamp":"2025-07-28T01:33:38.451Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"Thread-129","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"stopped _org.springframework.integration.errorLogger"}
{"@timestamp":"2025-07-28T01:33:38.451Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"Thread-129","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"Removing {message-handler:mqttConfig.handler.serviceActivator} as a subscriber to the 'mqttInputChannel' channel"}
{"@timestamp":"2025-07-28T01:33:38.451Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"Thread-129","class":"o.s.integration.channel.ExecutorChannel","rest":"Channel 'application-1.mqttInputChannel' has 0 subscriber(s)."}
{"@timestamp":"2025-07-28T01:33:38.451Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"Thread-129","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"stopped mqttConfig.handler.serviceActivator"}
{"@timestamp":"2025-07-28T01:33:38.451Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"Thread-129","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"Removing {message-handler:mqttConfig.mqttOutbound.serviceActivator} as a subscriber to the 'mqttOutboundChannel' channel"}
{"@timestamp":"2025-07-28T01:33:38.451Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"Thread-129","class":"o.s.integration.channel.ExecutorChannel","rest":"Channel 'application-1.mqttOutboundChannel' has 0 subscriber(s)."}
{"@timestamp":"2025-07-28T01:33:38.451Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"Thread-129","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"stopped mqttConfig.mqttOutbound.serviceActivator"}
{"@timestamp":"2025-07-28T01:33:40.510Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"Thread-129","class":"o.s.s.concurrent.ThreadPoolTaskScheduler","rest":"Shutting down ExecutorService 'taskScheduler'"}
{"@timestamp":"2025-07-28T01:33:40.510Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"Thread-129","class":"o.s.s.quartz.SchedulerFactoryBean","rest":"Shutting down Quartz Scheduler"}
{"@timestamp":"2025-07-28T01:33:40.512Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"Thread-129","class":"o.s.s.concurrent.ThreadPoolTaskExecutor","rest":"Shutting down ExecutorService"}
{"@timestamp":"2025-07-28T01:33:42.330Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12424","thread":"pool-11-thread-2","class":"c.x.i.t.d.DistributeRedisRateLimiter","rest":"applicaitonContext属性未注入, 请在applicationContext.xml中定义SpringBeanUtil"}
{"@timestamp":"2025-07-28T01:33:53.806Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$bd2e7cfa] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-28T01:33:54.280Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"o.s.c.c.c.ConfigServicePropertySourceLocator","rest":"Fetching config from server at : http://localhost:8888"}
{"@timestamp":"2025-07-28T01:33:56.425Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"o.s.c.c.c.ConfigServicePropertySourceLocator","rest":"Connect Timeout Exception on Url - http://localhost:8888. Will be trying the next url if available"}
{"@timestamp":"2025-07-28T01:33:56.426Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"o.s.c.c.c.ConfigServicePropertySourceLocator","rest":"Could not locate PropertySource: I/O error on GET request for \"http://localhost:8888/eeip-standalone-service/default\": Connection refused: connect; nested exception is java.net.ConnectException: Connection refused: connect"}
{"@timestamp":"2025-07-28T01:33:56.429Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.standalone.StandaloneApplication","rest":"The following profiles are active: alone"}
{"@timestamp":"2025-07-28T01:34:01.511Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"o.s.d.r.c.RepositoryConfigurationDelegate","rest":"Multiple Spring Data modules found, entering strict repository configuration mode!"}
{"@timestamp":"2025-07-28T01:34:01.515Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"o.s.d.r.c.RepositoryConfigurationDelegate","rest":"Bootstrapping Spring Data Redis repositories in DEFAULT mode."}
{"@timestamp":"2025-07-28T01:34:01.784Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"o.s.d.r.c.RepositoryConfigurationDelegate","rest":"Finished Spring Data repository scanning in 247ms. Found 0 Redis repository interfaces."}
{"@timestamp":"2025-07-28T01:34:01.962Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"o.s.boot.actuate.endpoint.EndpointId","rest":"Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format."}
{"@timestamp":"2025-07-28T01:34:02.193Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"o.s.boot.actuate.endpoint.EndpointId","rest":"Endpoint ID 'hystrix.stream' contains invalid characters, please migrate to a valid format."}
{"@timestamp":"2025-07-28T01:34:02.613Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"o.s.cloud.context.scope.GenericScope","rest":"BeanFactory id=5b395cf1-ddbf-331b-876f-aa2c7020b748"}
{"@timestamp":"2025-07-28T01:34:02.636Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor","rest":"No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created."}
{"@timestamp":"2025-07-28T01:34:02.645Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor","rest":"No bean named 'taskScheduler' has been explicitly defined. Therefore, a default ThreadPoolTaskScheduler will be created."}
{"@timestamp":"2025-07-28T01:34:02.657Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor","rest":"No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created."}
{"@timestamp":"2025-07-28T01:34:02.747Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'configProperties' of type [com.xcwlkj.core.config.properties.ConfigProperties$$EnhancerBySpringCGLIB$$fe0c5993] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-28T01:34:02.748Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'asyncTaskExecutorConfiguration' of type [com.xcwlkj.core.config.AsyncTaskExecutorConfiguration$$EnhancerBySpringCGLIB$$b73e9ec3] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-28T01:34:02.815Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$96d81222] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-28T01:34:03.091Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$a11479fd] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-28T01:34:03.145Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'redisConfig' of type [com.xcwlkj.standalone.config.RedisConfig$$EnhancerBySpringCGLIB$$7cb7577a] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-28T01:34:03.187Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'appConfig' of type [com.xcwlkj.standalone.config.AppConfig$$EnhancerBySpringCGLIB$$b8d88920] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-28T01:34:03.215Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'integrationDisposableAutoCreatedBeans' of type [org.springframework.integration.config.annotation.Disposables] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-28T01:34:03.258Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'com.xcwlkj.biz.core.feignclient.FeignClientConfig' of type [com.xcwlkj.biz.core.feignclient.FeignClientConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-28T01:34:03.266Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.integration.config.IntegrationManagementConfiguration' of type [org.springframework.integration.config.IntegrationManagementConfiguration$$EnhancerBySpringCGLIB$$c06352c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-28T01:34:03.299Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$bd2e7cfa] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-28T01:34:03.885Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"o.s.b.w.embedded.tomcat.TomcatWebServer","rest":"Tomcat initialized with port(s): 8888 (http)"}
{"@timestamp":"2025-07-28T01:34:04.027Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"o.s.web.context.ContextLoader","rest":"Root WebApplicationContext: initialization completed in 7574 ms"}
{"@timestamp":"2025-07-28T01:34:07.278Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"o.s.b.a.e.web.ServletEndpointRegistrar","rest":"Registered '/actuator/hystrix.stream' to hystrix.stream-actuator-endpoint"}
{"@timestamp":"2025-07-28T01:34:07.372Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"o.s.s.concurrent.ThreadPoolTaskExecutor","rest":"Initializing ExecutorService"}
{"@timestamp":"2025-07-28T01:34:07.514Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"o.s.s.concurrent.ThreadPoolTaskExecutor","rest":"Initializing ExecutorService"}
{"@timestamp":"2025-07-28T01:34:07.564Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"o.s.s.concurrent.ThreadPoolTaskExecutor","rest":"Initializing ExecutorService"}
{"@timestamp":"2025-07-28T01:34:07.571Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsServerUrl=http://************:8811"}
{"@timestamp":"2025-07-28T01:34:07.571Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsChannel=ZJKSZHPT"}
{"@timestamp":"2025-07-28T01:34:07.571Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsAppId=e046e7c7e7fbf1f1a4183e00c76e0182"}
{"@timestamp":"2025-07-28T01:34:07.571Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsAppSecret=6d0dc6d02af7bab1f1ed39a7baadbb55"}
{"@timestamp":"2025-07-28T01:34:08.471Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.core.interceptor.SqlLogInterceptor","rest":"[打印SQL拦截器创建]noticeTime=5.0秒"}
{"@timestamp":"2025-07-28T01:34:10.576Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.c.c.AsyncTaskExecutorConfiguration","rest":"Creating Async Task Executor"}
{"@timestamp":"2025-07-28T01:34:10.577Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"o.s.s.concurrent.ThreadPoolTaskExecutor","rest":"Initializing ExecutorService"}
{"@timestamp":"2025-07-28T01:34:12.093Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"com.xcwlkj.pubc.config.SmsConfig","rest":"SMS Bean IAcsClient Start"}
{"@timestamp":"2025-07-28T01:34:12.104Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"com.xcwlkj.pubc.config.SmsConfig","rest":"加载SMS Bean IAcsClient OK"}
{"@timestamp":"2025-07-28T01:34:19.796Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化####"}
{"@timestamp":"2025-07-28T01:34:19.804Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化成功####"}
{"@timestamp":"2025-07-28T01:34:21.252Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.taskcenter.DefaultHandleFactory","rest":"配置线程池工作线程数量[16]"}
{"@timestamp":"2025-07-28T01:34:21.348Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"开始检查平台类型和初始化上级平台DFS客户端..."}
{"@timestamp":"2025-07-28T01:34:21.574Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) "}
{"@timestamp":"2025-07-28T01:34:21.586Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==> Parameters: defaultPlat(String)"}
{"@timestamp":"2025-07-28T01:34:21.603Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-28T01:34:21.607Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"当前平台类型为：HISOME，开始初始化上级平台DFS客户端"}
{"@timestamp":"2025-07-28T01:34:21.608Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) "}
{"@timestamp":"2025-07-28T01:34:21.609Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==> Parameters: HISOME_fileServerUrl(String)"}
{"@timestamp":"2025-07-28T01:34:21.617Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-28T01:34:21.618Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) "}
{"@timestamp":"2025-07-28T01:34:21.619Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==> Parameters: HISOME_fileServerChannel(String)"}
{"@timestamp":"2025-07-28T01:34:21.625Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-28T01:34:21.627Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) "}
{"@timestamp":"2025-07-28T01:34:21.627Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==> Parameters: HISOME_fileServiceAppId(String)"}
{"@timestamp":"2025-07-28T01:34:21.633Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-28T01:34:21.634Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) "}
{"@timestamp":"2025-07-28T01:34:21.634Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==> Parameters: HISOME_fileServiceAppSecret(String)"}
{"@timestamp":"2025-07-28T01:34:21.642Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-28T01:34:21.652Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"上级平台DFS客户端初始化成功"}
{"@timestamp":"2025-07-28T01:34:21.652Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"上级平台DFS客户端自动初始化完成"}
{"@timestamp":"2025-07-28T01:34:23.082Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动中...."}
{"@timestamp":"2025-07-28T01:34:23.082Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.ScheduleJobMapper.selectAll","rest":"==>  Preparing: SELECT job_id,job_name,bean_name,params,cron_expression,status,remark,create_time,update_time FROM schedule_job "}
{"@timestamp":"2025-07-28T01:34:23.083Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.ScheduleJobMapper.selectAll","rest":"==> Parameters: "}
{"@timestamp":"2025-07-28T01:34:23.091Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.ScheduleJobMapper.selectAll","rest":"<==      Total: 10"}
{"@timestamp":"2025-07-28T01:34:23.101Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[teskTask-jobTest111]"}
{"@timestamp":"2025-07-28T01:34:23.102Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[sbzxztCxAndGxTaskService-jobSbzxztGx]"}
{"@timestamp":"2025-07-28T01:34:23.102Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[onlineStatusTaskService-jobOnlineStatus]"}
{"@timestamp":"2025-07-28T01:34:23.102Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[subscriberDevInfoToMqttTaskService-jobSubscribeMqtt]"}
{"@timestamp":"2025-07-28T01:34:23.102Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[sjxfsbCxxfTaskService-sjxfsbCxxf]"}
{"@timestamp":"2025-07-28T01:34:23.103Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[sbxxReportTaskService-设备信息上报]"}
{"@timestamp":"2025-07-28T01:34:23.103Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[ksjhSjdsxfTaskService-ksjhSjdsxf]"}
{"@timestamp":"2025-07-28T01:34:23.103Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[deviceVersionUpdateTaskService-设备固件版本更新]"}
{"@timestamp":"2025-07-28T01:34:23.103Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[deviceEventDeleteTaskService-设备事件删除]"}
{"@timestamp":"2025-07-28T01:34:23.104Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[kssjDsscsjptTaskService-考试数据定时上传上级平台]"}
{"@timestamp":"2025-07-28T01:34:23.106Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动完成"}
{"@timestamp":"2025-07-28T01:34:24.738Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"o.s.s.concurrent.ThreadPoolTaskScheduler","rest":"Initializing ExecutorService 'taskScheduler'"}
{"@timestamp":"2025-07-28T01:34:25.089Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.c.f.FeignHystrixConcurrencyStrategyIntellif","rest":"Current Hystrix plugins configuration is [concurrencyStrategy [com.netflix.hystrix.strategy.concurrency.HystrixConcurrencyStrategyDefault@3898ee5b],eventNotifier [com.netflix.hystrix.strategy.eventnotifier.HystrixEventNotifierDefault@623c43bb],metricPublisher [com.netflix.hystrix.strategy.metrics.HystrixMetricsPublisherDefault@2a8068ad],propertiesStrategy [com.netflix.hystrix.strategy.properties.HystrixPropertiesStrategyDefault@68b512c2],]"}
{"@timestamp":"2025-07-28T01:34:25.089Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.c.f.FeignHystrixConcurrencyStrategyIntellif","rest":"Registering Sleuth Hystrix Concurrency Strategy."}
{"@timestamp":"2025-07-28T01:34:29.548Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"com.xcwlkj.standalone.config.MqttConfig","rest":"######监听MQTT主题[/CENCGW100_S/CENCGW100_SIV1c9f717de4acfc99/function/invoke]"}
{"@timestamp":"2025-07-28T01:34:29.548Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"com.xcwlkj.standalone.config.MqttConfig","rest":"######监听MQTT主题[/CENCGW100_S/CENCGW100_SIV1c9f717de4acfc99/token/get/reply]"}
{"@timestamp":"2025-07-28T01:34:29.548Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"com.xcwlkj.standalone.config.MqttConfig","rest":"######监听MQTT主题[/+/+/event/+]"}
{"@timestamp":"2025-07-28T01:34:29.575Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.xcwlkj.standalone.config.XxlJobConfig","rest":">>>>>>>>>>> xxl-job config init."}
{"@timestamp":"2025-07-28T01:34:30.757Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"o.s.b.a.f.FreeMarkerAutoConfiguration","rest":"Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)"}
{"@timestamp":"2025-07-28T01:34:32.055Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"o.s.b.a.e.web.EndpointLinksResolver","rest":"Exposing 21 endpoint(s) beneath base path '/actuator'"}
{"@timestamp":"2025-07-28T01:34:33.464Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel"}
{"@timestamp":"2025-07-28T01:34:33.465Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"o.s.i.channel.PublishSubscribeChannel","rest":"Channel 'application-1.errorChannel' has 1 subscriber(s)."}
{"@timestamp":"2025-07-28T01:34:33.465Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"started _org.springframework.integration.errorLogger"}
{"@timestamp":"2025-07-28T01:34:33.465Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"Adding {message-handler:mqttConfig.mqttOutbound.serviceActivator} as a subscriber to the 'mqttOutboundChannel' channel"}
{"@timestamp":"2025-07-28T01:34:33.466Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"o.s.integration.channel.ExecutorChannel","rest":"Channel 'application-1.mqttOutboundChannel' has 1 subscriber(s)."}
{"@timestamp":"2025-07-28T01:34:33.466Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"started mqttConfig.mqttOutbound.serviceActivator"}
{"@timestamp":"2025-07-28T01:34:33.466Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"Adding {message-handler:mqttConfig.handler.serviceActivator} as a subscriber to the 'mqttInputChannel' channel"}
{"@timestamp":"2025-07-28T01:34:33.466Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"o.s.integration.channel.ExecutorChannel","rest":"Channel 'application-1.mqttInputChannel' has 1 subscriber(s)."}
{"@timestamp":"2025-07-28T01:34:33.466Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"started mqttConfig.handler.serviceActivator"}
{"@timestamp":"2025-07-28T01:34:33.466Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"o.s.i.g.GatewayProxyFactoryBean$MethodInvocationGateway","rest":"started mqttGateway"}
{"@timestamp":"2025-07-28T01:34:33.466Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"o.s.i.gateway.GatewayProxyFactoryBean","rest":"started mqttGateway"}
{"@timestamp":"2025-07-28T01:34:33.492Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"o.s.c.netflix.eureka.InstanceInfoFactory","rest":"Setting initial instance status as: STARTING"}
{"@timestamp":"2025-07-28T01:34:36.278Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"o.s.c.n.e.s.EurekaServiceRegistry","rest":"Registering application EEIP-STANDALONE-SERVICE with eureka with status UP"}
{"@timestamp":"2025-07-28T01:34:36.672Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"o.s.i.m.i.MqttPahoMessageDrivenChannelAdapter","rest":"started inbound"}
{"@timestamp":"2025-07-28T01:34:36.672Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"o.s.s.quartz.SchedulerFactoryBean","rest":"Starting Quartz Scheduler now"}
{"@timestamp":"2025-07-28T01:34:36.774Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"o.s.b.w.embedded.tomcat.TomcatWebServer","rest":"Tomcat started on port(s): 8888 (http) with context path ''"}
{"@timestamp":"2025-07-28T01:34:36.775Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"o.s.c.n.e.s.EurekaAutoServiceRegistration","rest":"Updating port to 8888"}
{"@timestamp":"2025-07-28T01:34:37.064Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.standalone.StandaloneApplication","rest":"Started StandaloneApplication in 44.957 seconds (JVM running for 45.74)"}
{"@timestamp":"2025-07-28T01:34:37.092Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"com.xcwlkj.util.YmlUtil","rest":"开始从application-alone.yml加载配置信息"}
{"@timestamp":"2025-07-28T01:34:37.092Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"com.xcwlkj.util.YmlUtil","rest":"读取外部文件失败D:\\code\\HS_CUEEIP\\trunk\\provider/config/application-alone.yml"}
{"@timestamp":"2025-07-28T01:34:37.096Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"com.xcwlkj.util.YmlUtil","rest":"开始从application-alone.yml加载配置信息【成功】"}
{"@timestamp":"2025-07-28T01:34:37.146Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"com.xcwlkj.cache.AbstractRedisDataCache","rest":"[PkgDataFileServices]cleanup cache finished"}
{"@timestamp":"2025-07-28T01:34:37.150Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.m.K.updateByExampleSelective","rest":"==>  Preparing: UPDATE ks_kssj_pkg_task SET complete = ? WHERE ( ( complete in ( ? , ? ) ) ) "}
{"@timestamp":"2025-07-28T01:34:37.151Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.m.K.updateByExampleSelective","rest":"==> Parameters: -1(Integer), 0(Integer), 1(Integer)"}
{"@timestamp":"2025-07-28T01:34:37.187Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.m.K.updateByExampleSelective","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-28T01:34:37.187Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.m.KsKssjPkgStatusMapper.clearDoing","rest":"==>  Preparing: UPDATE ks_kssj_pkg_status SET kssjb_qk = case when kssjb_qk = 1 then -10 else kssjb_qk end, jkryjbsjb_qk = case when jkryjbsjb_qk = 1 then -10 else jkryjbsjb_qk end, jkrybpsjb_qk = case when jkrybpsjb_qk = 1 then -10 else jkrybpsjb_qk end, pzsjb_qk = case when pzsjb_qk = 1 then -10 else pzsjb_qk end, kszpsjb_qk = case when kszpsjb_qk = 1 then -10 else kszpsjb_qk end, jkryzpsjb_qk = case when jkryzpsjb_qk = 1 then -10 else jkryzpsjb_qk end "}
{"@timestamp":"2025-07-28T01:34:37.188Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.m.KsKssjPkgStatusMapper.clearDoing","rest":"==> Parameters: "}
{"@timestamp":"2025-07-28T01:34:37.201Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.m.KsKssjPkgStatusMapper.clearDoing","rest":"<==    Updates: 295"}
{"@timestamp":"2025-07-28T01:34:37.204Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SnowflakeIdGenerator，init. workerId=0,datacenterId=0"}
{"@timestamp":"2025-07-28T01:34:37.204Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SnowflakeIdGenerator，finish<<<<<<<<<<<<<"}
{"@timestamp":"2025-07-28T01:34:37.205Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###ZookeeperInitRunner，init. HostAddress=**************:8888, applicationName=eeip-standalone-service"}
{"@timestamp":"2025-07-28T01:34:37.214Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.c.z.r.z.ZookeeperRegistryCenter","rest":"Elastic job: zookeeper registry center init, server lists is: **************:2181."}
{"@timestamp":"2025-07-28T01:34:37.495Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###ZookeeperInitRunner，finish<<<<<<<<<<<<<"}
{"@timestamp":"2025-07-28T01:34:37.499Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Init##"}
{"@timestamp":"2025-07-28T01:34:37.499Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.b.c.BasicinfoBusiCacheInitConfig","rest":"####开始缓存商户配置信息####"}
{"@timestamp":"2025-07-28T01:34:37.499Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.b.c.BasicinfoBusiCacheInitConfig","rest":"####缓存商户配置信息缓存成功####"}
{"@timestamp":"2025-07-28T01:34:37.499Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Succ<<<<<<<<<<<<<##"}
{"@timestamp":"2025-07-28T01:34:37.499Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Init##"}
{"@timestamp":"2025-07-28T01:34:37.499Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"com.xcwlkj.biz.config.BizInitConfig","rest":"####开始缓存配置信息####"}
{"@timestamp":"2025-07-28T01:34:37.499Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"com.xcwlkj.biz.config.BizInitConfig","rest":"####缓存配置信息缓存成功####"}
{"@timestamp":"2025-07-28T01:34:37.500Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"com.xcwlkj.biz.config.BizInitConfig","rest":"####消息队列初始化####"}
{"@timestamp":"2025-07-28T01:34:37.500Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"com.xcwlkj.biz.config.BizInitConfig","rest":"####消息队列初始化成功####"}
{"@timestamp":"2025-07-28T01:34:37.500Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Succ<<<<<<<<<<<<<##"}
{"@timestamp":"2025-07-28T01:34:37.500Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Init##"}
{"@timestamp":"2025-07-28T01:34:37.500Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.pubc.config.PubcBusiCacheInitConfig","rest":"####开始缓存公共服务配置信息####"}
{"@timestamp":"2025-07-28T01:34:37.500Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.pubc.config.PubcBusiCacheInitConfig","rest":"####缓存公共服务配置信息缓存成功####"}
{"@timestamp":"2025-07-28T01:34:37.500Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Succ<<<<<<<<<<<<<##"}
{"@timestamp":"2025-07-28T01:34:37.500Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Init##"}
{"@timestamp":"2025-07-28T01:34:37.500Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.e.c.EvaluationBusiCacheInitConfig","rest":"####开始缓存听评课配置信息####"}
{"@timestamp":"2025-07-28T01:34:37.500Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.e.c.EvaluationBusiCacheInitConfig","rest":"####缓存听评课配置信息缓存成功####"}
{"@timestamp":"2025-07-28T01:34:37.501Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.e.c.EvaluationBusiCacheInitConfig","rest":"####消息队列初始化####"}
{"@timestamp":"2025-07-28T01:34:37.501Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.e.c.EvaluationBusiCacheInitConfig","rest":"####消息队列初始化成功####"}
{"@timestamp":"2025-07-28T01:34:37.501Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Succ<<<<<<<<<<<<<##"}
{"@timestamp":"2025-07-28T01:34:37.501Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Init##"}
{"@timestamp":"2025-07-28T01:34:37.515Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####开始缓存校级身份核验平台配置信息####"}
{"@timestamp":"2025-07-28T01:34:38.293Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####服务器序列号为CENCGW100_SIV1c9f717de4acfc99"}
{"@timestamp":"2025-07-28T01:34:38.293Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####码表数据缓存初始化####"}
{"@timestamp":"2025-07-28T01:34:38.298Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.MbPzbMapper.selectByExample","rest":"==>  Preparing: SELECT mbywmc , zjzd , pxzd FROM mb_pzb WHERE ( ( sfqy = ? ) ) "}
{"@timestamp":"2025-07-28T01:34:38.299Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.MbPzbMapper.selectByExample","rest":"==> Parameters: 1(String)"}
{"@timestamp":"2025-07-28T01:34:38.306Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.MbPzbMapper.selectByExample","rest":"<==      Total: 10"}
{"@timestamp":"2025-07-28T01:34:38.309Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from gj_mzdmb order by DM asc "}
{"@timestamp":"2025-07-28T01:34:38.310Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-28T01:34:38.318Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 58"}
{"@timestamp":"2025-07-28T01:34:38.965Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from gj_sfzjdmb order by DM asc "}
{"@timestamp":"2025-07-28T01:34:38.965Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-28T01:34:38.971Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 12"}
{"@timestamp":"2025-07-28T01:34:39.112Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from jy_kskmdmb order by XM_DM,KM_DM asc "}
{"@timestamp":"2025-07-28T01:34:39.112Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-28T01:34:39.121Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 106"}
{"@timestamp":"2025-07-28T01:34:40.368Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from jy_rygwlxmb order by DM asc "}
{"@timestamp":"2025-07-28T01:34:40.368Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-28T01:34:40.375Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 3"}
{"@timestamp":"2025-07-28T01:34:40.416Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from jy_rygwzzmb order by SSGWM,DM asc "}
{"@timestamp":"2025-07-28T01:34:40.416Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-28T01:34:40.423Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 5"}
{"@timestamp":"2025-07-28T01:34:40.485Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from jy_wjlxmb order by DM asc "}
{"@timestamp":"2025-07-28T01:34:40.485Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-28T01:34:40.491Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 3"}
{"@timestamp":"2025-07-28T01:34:40.545Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from jy_wjxmmb order by LX_DM,XM_DM asc "}
{"@timestamp":"2025-07-28T01:34:40.546Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-28T01:34:40.553Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 20"}
{"@timestamp":"2025-07-28T01:34:40.782Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from jy_zcqswzdmb order by DM asc "}
{"@timestamp":"2025-07-28T01:34:40.782Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-28T01:34:40.790Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 4"}
{"@timestamp":"2025-07-28T01:34:40.842Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from jy_zwbjfsdmb order by DM asc "}
{"@timestamp":"2025-07-28T01:34:40.843Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-28T01:34:40.851Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 8"}
{"@timestamp":"2025-07-28T01:34:40.946Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from jy_zwplfsdmb order by DM asc "}
{"@timestamp":"2025-07-28T01:34:40.947Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-28T01:34:40.954Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 2"}
{"@timestamp":"2025-07-28T01:34:40.983Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####码表数据缓存初始化成功####"}
{"@timestamp":"2025-07-28T01:34:40.983Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####延迟队列初始化####"}
{"@timestamp":"2025-07-28T01:34:40.983Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"o.s.s.concurrent.ThreadPoolTaskExecutor","rest":"Initializing ExecutorService"}
{"@timestamp":"2025-07-28T01:34:40.984Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####延迟队列初始化成功####"}
{"@timestamp":"2025-07-28T01:34:40.984Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####数据下发任务初始化####"}
{"@timestamp":"2025-07-28T01:34:40.988Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.m.K.updateByExampleSelective","rest":"==>  Preparing: UPDATE ks_kssj_distribute_task SET complete = ?,complete_time = ? WHERE ( ( complete = ? ) ) "}
{"@timestamp":"2025-07-28T01:34:40.989Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.m.K.updateByExampleSelective","rest":"==> Parameters: 2(Integer), 2025-07-28 09:34:40.985(Timestamp), 1(Integer)"}
{"@timestamp":"2025-07-28T01:34:41.004Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.m.K.updateByExampleSelective","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-28T01:34:41.004Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####数据下发任务初始化成功####"}
{"@timestamp":"2025-07-28T01:34:41.004Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####数据库变更初始化####"}
{"@timestamp":"2025-07-28T01:34:41.004Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.service.impl.UtilsServiceImpl","rest":"dbName:[eeip_alone]"}
{"@timestamp":"2025-07-28T01:34:41.071Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-28T01:34:41.072Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksjh(String), cjlx(String)"}
{"@timestamp":"2025-07-28T01:34:41.079Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-28T01:34:41.080Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-28T01:34:41.080Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksjh(String), qydsxf(String)"}
{"@timestamp":"2025-07-28T01:34:41.087Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-28T01:34:41.087Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-28T01:34:41.087Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksjh(String), dsxfsj(String)"}
{"@timestamp":"2025-07-28T01:34:41.095Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-28T01:34:41.095Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-28T01:34:41.096Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksjh(String), dsxfzt(String)"}
{"@timestamp":"2025-07-28T01:34:41.103Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-28T01:34:41.104Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-28T01:34:41.104Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksjh(String), pack_jkrybpsj(String)"}
{"@timestamp":"2025-07-28T01:34:41.111Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-28T01:34:41.111Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-28T01:34:41.112Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksjh(String), pack_jkryjcsj(String)"}
{"@timestamp":"2025-07-28T01:34:41.119Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-28T01:34:41.119Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-28T01:34:41.120Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksjh(String), jkqdrs(String)"}
{"@timestamp":"2025-07-28T01:34:41.127Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-28T01:34:41.127Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-28T01:34:41.127Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksjh(String), dbms(String)"}
{"@timestamp":"2025-07-28T01:34:41.134Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-28T01:34:41.135Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-28T01:34:41.135Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_jkry_rcxx(String), jklsxh(String)"}
{"@timestamp":"2025-07-28T01:34:41.143Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-28T01:34:41.143Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-28T01:34:41.143Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_jkry_rcxx(String), xsd(String)"}
{"@timestamp":"2025-07-28T01:34:41.183Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-28T01:34:41.184Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-28T01:34:41.184Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_jkry_rcxx(String), sbzt(String)"}
{"@timestamp":"2025-07-28T01:34:41.192Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-28T01:34:41.192Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-28T01:34:41.192Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_jkry_rcxx(String), sbsj(String)"}
{"@timestamp":"2025-07-28T01:34:41.199Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-28T01:34:41.200Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-28T01:34:41.200Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_jkry_rcxx(String), tbzt(String)"}
{"@timestamp":"2025-07-28T01:34:41.207Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-28T01:34:41.208Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-28T01:34:41.208Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_jkry_rcxx(String), rgyzjg(String)"}
{"@timestamp":"2025-07-28T01:34:41.214Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-28T01:34:41.215Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.UtilsMapper.createCsDzgh","rest":"==>  Preparing: CREATE TABLE IF NOT EXISTS `cs_dzgh` ( `id` varchar(32) NOT NULL, `cslx` varchar(32) DEFAULT NULL COMMENT 'KD(考点) KC(考场)', `type` varchar(32) DEFAULT NULL COMMENT '类型KCWG(考场网关)', `classify` varchar(32) DEFAULT NULL COMMENT '分类DEVCFG(设备配置)', `key` varchar(32) DEFAULT NULL COMMENT 'key值（IP:ip地址,NETMASK:掩码,GATEWAY:网关IP地址,IPRANGE:ip范围,PCNUM:主机数）', `value` varchar(128) DEFAULT NULL COMMENT 'value值', `create_time` datetime DEFAULT NULL COMMENT '创建时间', `update_time` datetime DEFAULT NULL COMMENT '更新时间', `status` decimal(10,0) DEFAULT NULL COMMENT '1-启用 0-不启用', `sort` decimal(10,0) DEFAULT NULL COMMENT '排序', `jsh` varchar(32) DEFAULT NULL COMMENT '教室号', PRIMARY KEY (`id`), KEY `index_dzgh_1` (`jsh`,`value`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8 "}
{"@timestamp":"2025-07-28T01:34:41.216Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.UtilsMapper.createCsDzgh","rest":"==> Parameters: "}
{"@timestamp":"2025-07-28T01:34:41.228Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.UtilsMapper.createCsDzgh","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-28T01:34:41.229Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbIpdfp","rest":"==>  Preparing: CREATE TABLE IF NOT EXISTS `sb_ipdfp` ( `id` varchar(32) NOT NULL, `ipstart` varchar(32) DEFAULT NULL COMMENT 'IP起', `ipend` varchar(32) DEFAULT NULL COMMENT 'IP尾', `zwym` varchar(32) DEFAULT NULL COMMENT '子网掩码', `wg` varchar(32) DEFAULT NULL COMMENT '网关', `create_time` datetime DEFAULT NULL, `update_time` datetime DEFAULT NULL, PRIMARY KEY (`id`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 "}
{"@timestamp":"2025-07-28T01:34:41.229Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbIpdfp","rest":"==> Parameters: "}
{"@timestamp":"2025-07-28T01:34:41.242Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbIpdfp","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-28T01:34:41.242Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbSbjcb","rest":"==>  Preparing: CREATE TABLE IF NOT EXISTS `sb_sbjcb` ( `id` varchar(32) NOT NULL, `sbxxbh` varchar(32) DEFAULT NULL COMMENT '设备信息编号', `ip` varchar(32) DEFAULT NULL COMMENT 'ip', `xlh` varchar(128) DEFAULT NULL COMMENT '序列号', `detect_type` varchar(10) DEFAULT NULL COMMENT '检测类型1-ping 2-mqtt', `detect_result` varchar(10) DEFAULT NULL COMMENT '检测结果1-成功 -1001-ping失败 -2001-mqtt超时 -9999-其他错误', `detect_desc` varchar(255) DEFAULT NULL COMMENT '检测描述', `detect_exception` text COMMENT '检测异常', `detect_time` timestamp NULL DEFAULT NULL COMMENT '检测时间', PRIMARY KEY (`id`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 "}
{"@timestamp":"2025-07-28T01:34:41.242Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbSbjcb","rest":"==> Parameters: "}
{"@timestamp":"2025-07-28T01:34:41.256Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbSbjcb","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-28T01:34:41.256Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.m.UtilsMapper.createKsBmxxKstzz","rest":"==>  Preparing: CREATE TABLE IF NOT EXISTS `ks_bmxx_kstzz` ( `ksid` varchar(32) NOT NULL COMMENT '考生唯一标志号', `ksjhbh` varchar(32) NOT NULL COMMENT '考试计划标志号', `ksh` varchar(20) DEFAULT NULL COMMENT '考生号', `sfzjhm` varchar(20) NOT NULL COMMENT '身份证件号', `kszpid` varchar(128) DEFAULT NULL COMMENT '考生照片ID', `tzzzt` varchar(2) DEFAULT NULL COMMENT '0-未获取到特征值；1-已获取到特征值；2-获取特征值失败', `tzzid` varchar(128) DEFAULT NULL COMMENT '特征值ID', `tzzhqsj` datetime DEFAULT NULL COMMENT '特征值获取时间', `create_time` datetime DEFAULT NULL COMMENT '创建时间', `update_time` datetime DEFAULT NULL COMMENT '更新时间', `tzzfs` varchar(1) DEFAULT NULL COMMENT '特征值方式', `bmh` varchar(32) DEFAULT NULL COMMENT '报名号', PRIMARY KEY (`ksid`) USING BTREE ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='考生报名特征值' "}
{"@timestamp":"2025-07-28T01:34:41.256Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.m.UtilsMapper.createKsBmxxKstzz","rest":"==> Parameters: "}
{"@timestamp":"2025-07-28T01:34:41.271Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.m.UtilsMapper.createKsBmxxKstzz","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-28T01:34:41.272Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.m.UtilsMapper.createKsYdsbSbkszs","rest":"==>  Preparing: CREATE TABLE IF NOT EXISTS `ks_ydsb_sbkszs` ( `id` varchar(32) NOT NULL, `ksjhbh` varchar(32) NOT NULL COMMENT '考试计划编号', `sbxlh` varchar(64) NOT NULL COMMENT '设备序列号', `sbsj` datetime DEFAULT NULL COMMENT '上报时间', `kdmc` varchar(128) DEFAULT NULL COMMENT '考点名称', `sbkszs` int(10) DEFAULT NULL COMMENT '上报考生总数=入场人数+缺考人数', `create_time` datetime DEFAULT NULL COMMENT '创建时间', `update_time` datetime DEFAULT NULL COMMENT '更新时间', `ccm` varchar(10) DEFAULT NULL COMMENT '场次码', `kcbh` varchar(32) DEFAULT NULL COMMENT '考场编号', `ljkcbh` varchar(32) DEFAULT NULL COMMENT '逻辑考场编号', `sblx` varchar(10) DEFAULT NULL COMMENT '上报类型 BZ-标准 FB-非标', PRIMARY KEY (`id`), KEY `ks_ydsb_sbkszs__index_1` (`ksjhbh`,`ccm`,`sbxlh`,`kcbh`,`ljkcbh`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8 "}
{"@timestamp":"2025-07-28T01:34:41.272Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.m.UtilsMapper.createKsYdsbSbkszs","rest":"==> Parameters: "}
{"@timestamp":"2025-07-28T01:34:41.284Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.m.UtilsMapper.createKsYdsbSbkszs","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-28T01:34:41.286Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.m.UtilsMapper.createBizCsxxExt","rest":"==>  Preparing: create table if not exists biz_csxx_ext ( CSBH varchar(64) not null comment '场所编号' primary key, SBXLH varchar(128) null comment '设备序列号', YDZD_APP_VERSION varchar(128) null comment '移动终端app版本', ZHSBSJ datetime null comment '最后上报时间', XFZT int null comment '0-失败 1-下发中 2-成功' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 "}
{"@timestamp":"2025-07-28T01:34:41.286Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.m.UtilsMapper.createBizCsxxExt","rest":"==> Parameters: "}
{"@timestamp":"2025-07-28T01:34:41.298Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.m.UtilsMapper.createBizCsxxExt","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-28T01:34:41.299Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.m.UtilsMapper.createBizFsgjTask","rest":"==>  Preparing: create table if not exists biz_fsgj_task ( ID varchar(32) not null primary key, NAME varchar(128) null comment '任务名称', T_TYPE varchar(10) null comment '任务类型 1-移动终端APP', T_PROGRESS varchar(10) null comment '进度', COMPLETE int(10) null comment '完成状态 -1-失败 0-开始 1-进行中 2-完成', T_PROGRESS_DESC varchar(255) null comment '任务描述', T_PARAM longtext null comment '任务参数' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 "}
{"@timestamp":"2025-07-28T01:34:41.299Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.m.UtilsMapper.createBizFsgjTask","rest":"==> Parameters: "}
{"@timestamp":"2025-07-28T01:34:41.312Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.m.UtilsMapper.createBizFsgjTask","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-28T01:34:41.313Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.m.UtilsMapper.createBizFsgjTaskJg","rest":"==>  Preparing: create table if not exists biz_fsgj_task_jg ( ID varchar(32) not null primary key, TASK_ID varchar(32) null comment '任务id', CSBH varchar(64) null comment '场所编号', SBXLH varchar(64) null comment '设备序列号' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 "}
{"@timestamp":"2025-07-28T01:34:41.313Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.m.UtilsMapper.createBizFsgjTaskJg","rest":"==> Parameters: "}
{"@timestamp":"2025-07-28T01:34:41.325Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.m.UtilsMapper.createBizFsgjTaskJg","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-28T01:34:41.327Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.UtilsMapper.filterTables","rest":"==>  Preparing: select table_name from information_schema.tables where table_schema= ? AND table_name in ( ? , ? ) "}
{"@timestamp":"2025-07-28T01:34:41.327Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.UtilsMapper.filterTables","rest":"==> Parameters: eeip_alone(String), biz_send_command(String), biz_send_command_dev(String)"}
{"@timestamp":"2025-07-28T01:34:41.334Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.UtilsMapper.filterTables","rest":"<==      Total: 2"}
{"@timestamp":"2025-07-28T01:34:41.335Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-28T01:34:41.335Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), sb_sbxx(String), osbb(String)"}
{"@timestamp":"2025-07-28T01:34:41.342Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-28T01:34:41.342Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-28T01:34:41.343Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), sb_sbxx(String), ntp_enable(String)"}
{"@timestamp":"2025-07-28T01:34:41.349Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-28T01:34:41.349Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-28T01:34:41.350Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), sb_sbxx(String), ntp_source(String)"}
{"@timestamp":"2025-07-28T01:34:41.357Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-28T01:34:41.357Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-28T01:34:41.357Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), sb_sbxx(String), ntp_interval(String)"}
{"@timestamp":"2025-07-28T01:34:41.364Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-28T01:34:41.365Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbsj","rest":"==>  Preparing: create table if not exists sb_sbsj ( id varchar(32) charset utf8 not null primary key, sbxlh varchar(128) charset utf8 not null comment '设备序列号', sblx varchar(32) charset utf8 not null comment 'kdwg考点网关 kcwg考场网关 ydzd移动终端', event_type varchar(16) charset utf8 not null comment 'ONLINE-上线事件 USB-usb事件 WIFI-wifi事件 YDZDBATTERY-移动终端电池事件', event_time datetime not null comment '事件时间', csbh varchar(32) charset utf8 null comment '场所编号', event_detail text charset utf8 null comment '事件详情', create_time datetime null comment '创建时间', update_time datetime null comment '更新时间', status varchar(4) charset utf8 null comment '状态', `repeat` varchar(4) charset utf8 null comment '是否重复', repeat_count int(10) null comment '重复次数 ', `desc` text charset utf8 null comment '描述', param1 varchar(64) charset utf8 null comment '扩展1', param2 varchar(64) charset utf8 null comment '扩展2', param3 varchar(64) charset utf8 null comment '扩展3', event_time_start datetime null comment '事件开始时间', event_time_end datetime null comment '事件结束时间' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='设备事件' "}
{"@timestamp":"2025-07-28T01:34:41.365Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbsj","rest":"==> Parameters: "}
{"@timestamp":"2025-07-28T01:34:41.377Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbsj","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-28T01:34:41.379Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-28T01:34:41.379Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksrcxx(String), tbzt(String)"}
{"@timestamp":"2025-07-28T01:34:41.385Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-28T01:34:41.387Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.UtilsMapper.alterColumn","rest":"==>  Preparing: ALTER TABLE ks_ksrcxx modify COLUMN tbzt varchar(2) COMMENT ?; "}
{"@timestamp":"2025-07-28T01:34:41.387Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.UtilsMapper.alterColumn","rest":"==> Parameters: 同步状态(String)"}
{"@timestamp":"2025-07-28T01:34:41.404Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.UtilsMapper.alterColumn","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-28T01:34:41.404Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.service.impl.UtilsServiceImpl","rest":"数据库dbName:[eeip_alone] tbName:[ks_ksrcxx] columName:[tbzt]变更"}
{"@timestamp":"2025-07-28T01:34:41.404Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-28T01:34:41.405Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksrcxx(String), xsd(String)"}
{"@timestamp":"2025-07-28T01:34:41.413Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-28T01:34:41.413Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-28T01:34:41.413Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksrcxx(String), sc_sfzp(String)"}
{"@timestamp":"2025-07-28T01:34:41.421Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-28T01:34:41.422Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-28T01:34:41.422Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksrcxx(String), sc_rlzp(String)"}
{"@timestamp":"2025-07-28T01:34:41.429Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-28T01:34:41.429Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####数据库变更初始化成功####"}
{"@timestamp":"2025-07-28T01:34:41.429Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####缓存校级身份核验平台配置信息缓存成功####"}
{"@timestamp":"2025-07-28T01:34:41.429Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Succ<<<<<<<<<<<<<##"}
{"@timestamp":"2025-07-28T01:34:42.033Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"RMI TCP Connection(5)-**************","class":"o.s.web.servlet.DispatcherServlet","rest":"Initializing Servlet 'dispatcherServlet'"}
{"@timestamp":"2025-07-28T01:34:42.071Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"RMI TCP Connection(5)-**************","class":"o.s.web.servlet.DispatcherServlet","rest":"Completed initialization in 37 ms"}
{"@timestamp":"2025-07-28T01:34:42.279Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"Thread-63","class":"com.xcwlkj.msgque.que.XcRocektMqConsumer","rest":"监听：JKYT_RSTJ_XXTS,启动成功！"}
{"@timestamp":"2025-07-28T01:34:42.510Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"RMI TCP Connection(4)-**************","class":"o.s.c.c.c.ConfigServicePropertySourceLocator","rest":"Fetching config from server at : http://localhost:8888"}
{"@timestamp":"2025-07-28T01:34:42.803Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"RMI TCP Connection(4)-**************","class":"o.s.c.c.c.ConfigServicePropertySourceLocator","rest":"Could not locate PropertySource: label not found"}
{"@timestamp":"2025-07-28T01:34:45.279Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"Thread-62","class":"com.xcwlkj.msgque.que.XcRocektMqConsumer","rest":"监听：JKYT_ATTENDANCE_JTXX,启动成功！"}
{"@timestamp":"2025-07-28T01:34:56.311Z","severity":"INFO","service":"eeip-standalone-service","trace":"7f535ce512050344","span":"7f535ce512050344","parent":"","exportable":"false","pid":"15324","thread":"http-nio-8888-exec-4","class":"c.x.i.facade.manager.CxtjController","rest":"收到请求开始：[紧急呼叫查询][/manager/identityverify/cxtj/jjhjcx]reqModel:JjhjCxReq[ksjhbh=2507211439231396864138952572928,kcbh=<null>,clzt=<null>,startTime=<null>,endTime=<null>,pageNum=1,pageSize=10,msgId=<null>,transChannel=<null>,channelSerialNo=<null>,channelDate=<null>,channelTime=<null>,token=<null>,loginToken=<null>,transCode=<null>,clientIp=<null>,merchantId=<null>,appPackage=<null>]"}
{"@timestamp":"2025-07-28T01:34:56.347Z","severity":"INFO","service":"eeip-standalone-service","trace":"7f535ce512050344","span":"7f535ce512050344","parent":"","exportable":"false","pid":"15324","thread":"http-nio-8888-exec-4","class":"c.x.i.s.impl.KsKwMessageServiceImpl","rest":"紧急呼叫查询开始，查询条件：JjhjCxDTO[ksjhbh=2507211439231396864138952572928,kcbh=<null>,clzt=<null>,startTime=<null>,endTime=<null>,pageNum=1,pageSize=10]"}
{"@timestamp":"2025-07-28T01:34:56.406Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"7f535ce512050344","span":"7f535ce512050344","parent":"","exportable":"false","pid":"15324","thread":"http-nio-8888-exec-4","class":"c.x.i.m.K.selectJjhjList_COUNT","rest":"==>  Preparing: SELECT count(0) FROM ks_kw_message msg LEFT JOIN ks_kw_message reply ON msg.ksjhbh = reply.ksjhbh AND msg.id = reply.replay_id AND reply.sczt = '0' WHERE msg.sczt = '0' AND msg.msg_type = '2000' AND msg.origin_app_type = 'HYAPP' AND msg.operate = '0' AND msg.ksjhbh = ? "}
{"@timestamp":"2025-07-28T01:34:56.406Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"7f535ce512050344","span":"7f535ce512050344","parent":"","exportable":"false","pid":"15324","thread":"http-nio-8888-exec-4","class":"c.x.i.m.K.selectJjhjList_COUNT","rest":"==> Parameters: 2507211439231396864138952572928(String)"}
{"@timestamp":"2025-07-28T01:34:56.416Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"7f535ce512050344","span":"7f535ce512050344","parent":"","exportable":"false","pid":"15324","thread":"http-nio-8888-exec-4","class":"c.x.i.m.K.selectJjhjList_COUNT","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-28T01:34:56.419Z","severity":"INFO","service":"eeip-standalone-service","trace":"7f535ce512050344","span":"7f535ce512050344","parent":"","exportable":"false","pid":"15324","thread":"http-nio-8888-exec-4","class":"c.x.i.s.impl.KsKwMessageServiceImpl","rest":"紧急呼叫查询结束，共查询到0条记录"}
{"@timestamp":"2025-07-28T01:34:56.445Z","severity":"INFO","service":"eeip-standalone-service","trace":"7f535ce512050344","span":"7f535ce512050344","parent":"","exportable":"false","pid":"15324","thread":"http-nio-8888-exec-4","class":"c.x.i.facade.manager.CxtjController","rest":"处理请求结束：[紧急呼叫查询][/manager/identityverify/cxtj/jjhjcx]reqModel:JjhjCxReq[ksjhbh=2507211439231396864138952572928,kcbh=<null>,clzt=<null>,startTime=<null>,endTime=<null>,pageNum=1,pageSize=10,msgId=<null>,transChannel=<null>,channelSerialNo=<null>,channelDate=<null>,channelTime=<null>,token=<null>,loginToken=<null>,transCode=<null>,clientIp=<null>,merchantId=<null>,appPackage=<null>],respModel:JjhjCxResp[data=Page{count=true, pageNum=1, pageSize=10, startRow=0, endRow=10, total=0, pages=0, reasonable=true, pageSizeZero=false}[],totalRows=0,pageNum=1,pageSize=10,msgId=<null>,transChannel=<null>,channelSerialNo=<null>,transCode=<null>,respDate=<null>,respTime=<null>,ext=<null>]"}
{"@timestamp":"2025-07-28T01:35:00.011Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-9","class":"c.x.i.util.schedule.ScheduleJobExecutor","rest":"任务准备执行，任务ID：24081610292802018092458168076288"}
{"@timestamp":"2025-07-28T01:35:00.011Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-8","class":"c.x.i.util.schedule.ScheduleJobExecutor","rest":"任务准备执行，任务ID：23110110740401808639620095703040"}
{"@timestamp":"2025-07-28T01:35:00.016Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-9","class":"c.x.i.m.KsKsjhMapper.selectByExample","rest":"==>  Preparing: SELECT ksjhbh,mc,kssj,jssj,xn,xq,sfmr,kszt,sbjm,sbkqsj,sbgbsj,cjlx,kslx,qysbbmd,wifiqy,cjsj,xgsj,scztw,kzqy,qydsxf,dsxfsj,dsxfzt,jkqdrs,dbms FROM ks_ksjh WHERE ( ( scztw = ? and qydsxf = ? and dsxfzt = ? ) ) "}
{"@timestamp":"2025-07-28T01:35:00.016Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-9","class":"c.x.i.m.KsKsjhMapper.selectByExample","rest":"==> Parameters: 0(String), 1(Integer), 0(Integer)"}
{"@timestamp":"2025-07-28T01:35:00.025Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-9","class":"c.x.i.m.KsKsjhMapper.selectByExample","rest":"<==      Total: 0"}
{"@timestamp":"2025-07-28T01:35:00.025Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-9","class":"c.x.i.s.i.s.KsjhSjdsxfTaskServiceImpl","rest":"当前无下发任务！"}
{"@timestamp":"2025-07-28T01:35:00.025Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-9","class":"c.x.i.util.schedule.ScheduleJobExecutor","rest":"任务执行完毕，任务ID：24081610292802018092458168076288  总共耗时：14毫秒"}
{"@timestamp":"2025-07-28T01:35:00.028Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-9","class":"c.x.i.m.S.insertSelective","rest":"==>  Preparing: INSERT INTO schedule_job_log ( log_id,job_id,job_name,bean_name,params,result,times,create_time ) VALUES( ?,?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-28T01:35:00.029Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-9","class":"c.x.i.m.S.insertSelective","rest":"==> Parameters: 25072809350002268837445724304385(String), 24081610292802018092458168076288(String), ksjhSjdsxf(String), ksjhSjdsxfTaskService(String), (String), 0(Integer), 14(Integer), 2025-07-28 09:35:00.011(Timestamp)"}
{"@timestamp":"2025-07-28T01:35:00.044Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-8","class":"c.x.i.util.schedule.ScheduleJobExecutor","rest":"任务执行完毕，任务ID：23110110740401808639620095703040  总共耗时：33毫秒"}
{"@timestamp":"2025-07-28T01:35:00.044Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-8","class":"c.x.i.m.S.insertSelective","rest":"==>  Preparing: INSERT INTO schedule_job_log ( log_id,job_id,job_name,bean_name,result,times,create_time ) VALUES( ?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-28T01:35:00.049Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-8","class":"c.x.i.m.S.insertSelective","rest":"==> Parameters: 25072809350002268837445724304384(String), 23110110740401808639620095703040(String), jobOnlineStatus(String), onlineStatusTaskService(String), 0(Integer), 33(Integer), 2025-07-28 09:35:00.011(Timestamp)"}
{"@timestamp":"2025-07-28T01:35:00.425Z","severity":"INFO","service":"eeip-standalone-service","trace":"22e9159078229daf","span":"22e9159078229daf","parent":"","exportable":"false","pid":"15324","thread":"http-nio-8888-exec-3","class":"c.x.i.facade.manager.CxtjController","rest":"收到请求开始：[紧急呼叫查询][/manager/identityverify/cxtj/jjhjcx]reqModel:JjhjCxReq[ksjhbh=<null>,kcbh=<null>,clzt=<null>,startTime=<null>,endTime=<null>,pageNum=1,pageSize=10,msgId=<null>,transChannel=<null>,channelSerialNo=<null>,channelDate=<null>,channelTime=<null>,token=<null>,loginToken=<null>,transCode=<null>,clientIp=<null>,merchantId=<null>,appPackage=<null>]"}
{"@timestamp":"2025-07-28T01:35:00.441Z","severity":"INFO","service":"eeip-standalone-service","trace":"22e9159078229daf","span":"22e9159078229daf","parent":"","exportable":"false","pid":"15324","thread":"http-nio-8888-exec-3","class":"c.x.i.s.impl.KsKwMessageServiceImpl","rest":"紧急呼叫查询开始，查询条件：JjhjCxDTO[ksjhbh=<null>,kcbh=<null>,clzt=<null>,startTime=<null>,endTime=<null>,pageNum=1,pageSize=10]"}
{"@timestamp":"2025-07-28T01:35:00.449Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"22e9159078229daf","span":"22e9159078229daf","parent":"","exportable":"false","pid":"15324","thread":"http-nio-8888-exec-3","class":"c.x.i.m.K.selectJjhjList_COUNT","rest":"==>  Preparing: SELECT count(0) FROM ks_kw_message msg LEFT JOIN ks_kw_message reply ON msg.ksjhbh = reply.ksjhbh AND msg.id = reply.replay_id AND reply.sczt = '0' WHERE msg.sczt = '0' AND msg.msg_type = '2000' AND msg.origin_app_type = 'HYAPP' AND msg.operate = '0' "}
{"@timestamp":"2025-07-28T01:35:00.449Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"22e9159078229daf","span":"22e9159078229daf","parent":"","exportable":"false","pid":"15324","thread":"http-nio-8888-exec-3","class":"c.x.i.m.K.selectJjhjList_COUNT","rest":"==> Parameters: "}
{"@timestamp":"2025-07-28T01:35:00.453Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"22e9159078229daf","span":"22e9159078229daf","parent":"","exportable":"false","pid":"15324","thread":"http-nio-8888-exec-3","class":"c.x.i.m.K.selectJjhjList_COUNT","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-28T01:35:00.458Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"22e9159078229daf","span":"22e9159078229daf","parent":"","exportable":"false","pid":"15324","thread":"http-nio-8888-exec-3","class":"c.x.i.m.KsKwMessageMapper.selectJjhjList","rest":"==>  Preparing: SELECT msg.id, DATE_FORMAT(msg.send_time, '%Y-%m-%d %H:%i:%s') as fbsj, msg.origin_kcbh as kcbh, msg.comment as jjhjnr, reply.comment as hfnr, DATE_FORMAT(reply.send_time, '%Y-%m-%d %H:%i:%s') as hfsj, CASE WHEN reply.id IS NULL THEN '0' ELSE '1' END as clzt FROM ks_kw_message msg LEFT JOIN ks_kw_message reply ON msg.ksjhbh = reply.ksjhbh AND msg.id = reply.replay_id AND reply.sczt = '0' WHERE msg.sczt = '0' AND msg.msg_type = '2000' AND msg.origin_app_type = 'HYAPP' AND msg.operate = '0' ORDER BY msg.send_time DESC LIMIT ? "}
{"@timestamp":"2025-07-28T01:35:00.458Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"22e9159078229daf","span":"22e9159078229daf","parent":"","exportable":"false","pid":"15324","thread":"http-nio-8888-exec-3","class":"c.x.i.m.KsKwMessageMapper.selectJjhjList","rest":"==> Parameters: 10(Integer)"}
{"@timestamp":"2025-07-28T01:35:00.470Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"22e9159078229daf","span":"22e9159078229daf","parent":"","exportable":"false","pid":"15324","thread":"http-nio-8888-exec-3","class":"c.x.i.m.KsKwMessageMapper.selectJjhjList","rest":"<==      Total: 10"}
{"@timestamp":"2025-07-28T01:35:00.470Z","severity":"INFO","service":"eeip-standalone-service","trace":"22e9159078229daf","span":"22e9159078229daf","parent":"","exportable":"false","pid":"15324","thread":"http-nio-8888-exec-3","class":"c.x.i.s.impl.KsKwMessageServiceImpl","rest":"紧急呼叫查询结束，共查询到53条记录"}
{"@timestamp":"2025-07-28T01:35:00.498Z","severity":"INFO","service":"eeip-standalone-service","trace":"22e9159078229daf","span":"22e9159078229daf","parent":"","exportable":"false","pid":"15324","thread":"http-nio-8888-exec-3","class":"c.x.i.facade.manager.CxtjController","rest":"处理请求结束：[紧急呼叫查询][/manager/identityverify/cxtj/jjhjcx]reqModel:JjhjCxReq[ksjhbh=<null>,kcbh=<null>,clzt=<null>,startTime=<null>,endTime=<null>,pageNum=1,pageSize=10,msgId=<null>,transChannel=<null>,channelSerialNo=<null>,channelDate=<null>,channelTime=<null>,token=<null>,loginToken=<null>,transCode=<null>,clientIp=<null>,merchantId=<null>,appPackage=<null>],respModel:JjhjCxResp[data=Page{count=true, pageNum=1, pageSize=10, startRow=0, endRow=10, total=53, pages=6, reasonable=true, pageSizeZero=false}[JjhjCxVO[id=2507071451291391793755551432704,fbsj=2025-07-07 14:51:29,kcbh=003,007,jjhjnr=混合考场情况,hfnr=已收到，正派人处理,hfsj=2025-07-07 14:52:14,clzt=1], JjhjCxVO[id=2507071142321391746204223668224,fbsj=2025-07-07 11:42:32,kcbh=003,007,jjhjnr=电子班牌有误,hfnr=<null>,hfsj=<null>,clzt=0], JjhjCxVO[id=2507062126101391530691837034496,fbsj=2025-07-06 21:26:09,kcbh=003,007,jjhjnr=电子班牌有误,hfnr=已收到，正派人处理,hfsj=2025-07-06 21:27:24,clzt=1], JjhjCxVO[id=2505271725281376974603104026624,fbsj=2025-05-27 17:25:29,kcbh=003,jjhjnr=电子班牌有误,hfnr=收到，设备有问题，可先进行人工核验。保持考场秩序,hfsj=2025-05-27 17:25:43,clzt=1], JjhjCxVO[id=24122709074502114446502591008768,fbsj=2024-12-27 09:07:45,kcbh=001,jjhjnr=电子班牌有误,hfnr=<null>,hfsj=<null>,clzt=0], JjhjCxVO[id=24111914244302087064562724573184,fbsj=2024-11-19 14:24:42,kcbh=003,jjhjnr=混合考场情况,hfnr=<null>,hfsj=<null>,clzt=0], JjhjCxVO[id=24111914244302087064562816978944,fbsj=2024-11-19 14:24:42,kcbh=003,jjhjnr=混合考场情况,hfnr=<null>,hfsj=<null>,clzt=0], JjhjCxVO[id=24111914241302087064306008002560,fbsj=2024-11-19 14:24:12,kcbh=003,jjhjnr=其它,hfnr=<null>,hfsj=<null>,clzt=0], JjhjCxVO[id=24111914241302087064306167517184,fbsj=2024-11-19 14:24:12,kcbh=003,jjhjnr=其它,hfnr=<null>,hfsj=<null>,clzt=0], JjhjCxVO[id=24111914391602087071879906004992,fbsj=2024-11-19 14:10:16,kcbh=003,jjhjnr=其它,hfnr=<null>,hfsj=<null>,clzt=0]],totalRows=53,pageNum=1,pageSize=10,msgId=<null>,transChannel=<null>,channelSerialNo=<null>,transCode=<null>,respDate=<null>,respTime=<null>,ext=<null>]"}
{"@timestamp":"2025-07-28T01:35:01.463Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"MQTT Rec: identityVerify_test01","class":"o.s.i.m.outbound.MqttPahoMessageHandler","rest":"Lost connection; will attempt reconnect on next request"}
{"@timestamp":"2025-07-28T01:35:26.717Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"idvScheduleTask-pool-0","class":"c.x.i.m.AttachmentMapper.selectByExample","rest":"==>  Preparing: SELECT id,version,created_by,creator_id,created_time,last_operator,last_operator_id,last_operator_time,ref_no,bucket_name,attachment_define_id,original_name,new_file_name,thumb_file_name,path,thumb_path,type,description,expire_time FROM attachment WHERE ( ( expire_time <= ? ) ) "}
{"@timestamp":"2025-07-28T01:35:26.719Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"idvScheduleTask-pool-0","class":"c.x.i.m.AttachmentMapper.selectByExample","rest":"==> Parameters: 2025-07-28 09:35:26(String)"}
{"@timestamp":"2025-07-28T01:35:26.732Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"idvScheduleTask-pool-0","class":"c.x.i.m.AttachmentMapper.selectByExample","rest":"<==      Total: 0"}
{"@timestamp":"2025-07-28T01:40:00.004Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-10","class":"c.x.i.util.schedule.ScheduleJobExecutor","rest":"任务准备执行，任务ID：23110110440401808639620095703040"}
{"@timestamp":"2025-07-28T01:40:00.004Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-1","class":"c.x.i.util.schedule.ScheduleJobExecutor","rest":"任务准备执行，任务ID：23110111540401808639620095703040"}
{"@timestamp":"2025-07-28T01:40:00.005Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-3","class":"c.x.i.util.schedule.ScheduleJobExecutor","rest":"任务准备执行，任务ID：24081610292802018092458168076288"}
{"@timestamp":"2025-07-28T01:40:00.005Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.util.schedule.ScheduleJobExecutor","rest":"任务准备执行，任务ID：2502130940211339531603881230336"}
{"@timestamp":"2025-07-28T01:40:00.009Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-10","class":"c.x.i.service.impl.SbSbxxServiceImpl","rest":"=======开始查询和更新设备在线状态========="}
{"@timestamp":"2025-07-28T01:40:00.009Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.service.impl.SbSbxxServiceImpl","rest":"固件版本更新"}
{"@timestamp":"2025-07-28T01:40:00.014Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.t.u.h.s.i.UnifyAccessServiceImpl","rest":"url[http://*************:8844/device/firmware/_query]参数[JetlinksDeviceFirewareReqModel(paging=true, parallelPager=true, pageSize=1, pageIndex=0)]"}
{"@timestamp":"2025-07-28T01:40:00.017Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-3","class":"c.x.i.m.KsKsjhMapper.selectByExample","rest":"==>  Preparing: SELECT ksjhbh,mc,kssj,jssj,xn,xq,sfmr,kszt,sbjm,sbkqsj,sbgbsj,cjlx,kslx,qysbbmd,wifiqy,cjsj,xgsj,scztw,kzqy,qydsxf,dsxfsj,dsxfzt,jkqdrs,dbms FROM ks_ksjh WHERE ( ( scztw = ? and qydsxf = ? and dsxfzt = ? ) ) "}
{"@timestamp":"2025-07-28T01:40:00.017Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-1","class":"c.x.i.m.SbSbxxMapper.selectByExample","rest":"==>  Preparing: SELECT sbxxbh,sbmc,sbxh,sbbm,sbpp,sbcs,sblx,sblxmc,jspc,jssj,sbccrq,sbazrq,sbzbq,sbwldz,macdz,ipdz,dkh,czxt,kzdkh,appbb,firmwarebb,xlh,ramdiskbb,sbzt,jhzt,sbmy,sczt,zxzt,sbzpid,sbqm,sbjcjg,zhzxsj,create_time,update_time,sbztw,sfydsb,osbb,battery,ntp_enable,ntp_source,ntp_interval,ext_status FROM sb_sbxx WHERE ( ( sczt = ? ) ) "}
{"@timestamp":"2025-07-28T01:40:00.019Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-1","class":"c.x.i.m.SbSbxxMapper.selectByExample","rest":"==> Parameters: 0(String)"}
{"@timestamp":"2025-07-28T01:40:00.019Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-3","class":"c.x.i.m.KsKsjhMapper.selectByExample","rest":"==> Parameters: 0(String), 1(Integer), 0(Integer)"}
{"@timestamp":"2025-07-28T01:40:00.027Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-3","class":"c.x.i.m.KsKsjhMapper.selectByExample","rest":"<==      Total: 0"}
{"@timestamp":"2025-07-28T01:40:00.027Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-3","class":"c.x.i.s.i.s.KsjhSjdsxfTaskServiceImpl","rest":"当前无下发任务！"}
{"@timestamp":"2025-07-28T01:40:00.027Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-10","class":"c.x.i.m.S.selectByExample_COUNT","rest":"==>  Preparing: SELECT count(0) FROM sb_sbxx WHERE ((sczt = ? AND sblx IN (?, ?, ?, ?))) "}
{"@timestamp":"2025-07-28T01:40:00.027Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-3","class":"c.x.i.util.schedule.ScheduleJobExecutor","rest":"任务执行完毕，任务ID：24081610292802018092458168076288  总共耗时：22毫秒"}
{"@timestamp":"2025-07-28T01:40:00.027Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-10","class":"c.x.i.m.S.selectByExample_COUNT","rest":"==> Parameters: 0(String), 170(String), 171(String), 172(String), 175(String)"}
{"@timestamp":"2025-07-28T01:40:00.030Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-1","class":"c.x.i.m.SbSbxxMapper.selectByExample","rest":"<==      Total: 16"}
{"@timestamp":"2025-07-28T01:40:00.030Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-3","class":"c.x.i.m.S.insertSelective","rest":"==>  Preparing: INSERT INTO schedule_job_log ( log_id,job_id,job_name,bean_name,params,result,times,create_time ) VALUES( ?,?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-28T01:40:00.030Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-3","class":"c.x.i.m.S.insertSelective","rest":"==> Parameters: 25072809400002268839962256372736(String), 24081610292802018092458168076288(String), ksjhSjdsxf(String), ksjhSjdsxfTaskService(String), (String), 0(Integer), 22(Integer), 2025-07-28 09:40:00.005(Timestamp)"}
{"@timestamp":"2025-07-28T01:40:00.031Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-1","class":"c.x.i.t.u.h.s.i.UnifyAccessServiceImpl","rest":"url http://*************:8844/device/category?terms[0].column=parentId&terms[0].value=-5-"}
{"@timestamp":"2025-07-28T01:40:00.034Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-10","class":"c.x.i.m.S.selectByExample_COUNT","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-28T01:40:00.035Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-10","class":"c.x.i.t.u.h.s.i.UnifyAccessServiceImpl","rest":"url[http://*************:8844/remote/access/device/listByDeviceNames]参数[DeviceStatusReqModel(deviceNames=[])]"}
{"@timestamp":"2025-07-28T01:40:00.043Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-10","class":"c.x.i.t.u.h.s.i.UnifyAccessServiceImpl","rest":"登录参数为空使用默认appId[RDbKcP5Z3mGwxeJB],accessKey[SNTiZ8mHKKhWFhNFApFRPMtk]"}
{"@timestamp":"2025-07-28T01:40:00.043Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-10","class":"c.x.i.t.u.h.s.i.UnifyAccessServiceImpl","rest":"url[http://*************:8844/remote/access/user/login]参数[PlatLoginReqModel(appId=RDbKcP5Z3mGwxeJB, accessKey=SNTiZ8mHKKhWFhNFApFRPMtk, timestamp=2025-07-28 09:40:00, sign=null)]"}
{"@timestamp":"2025-07-28T01:40:00.066Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.t.u.h.s.i.UnifyAccessServiceImpl","rest":"发送给接入返回结果result=JetlinksWrapper(message=success, status=200, code=200, timestamp=1753666798917, result=JetlinksPageRespModel(pageIndex=0, pageSize=1, total=22, data=[JetlinksDeviceFirewareRespModel(id=00024700113e, deviceName=办公室超脑, productId=HISOME-DX000, version=2025.05.20.1807)]))"}
{"@timestamp":"2025-07-28T01:40:00.067Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.t.u.h.s.i.UnifyAccessServiceImpl","rest":"url[http://*************:8844/device/firmware/_query]参数[JetlinksDeviceFirewareReqModel(paging=true, parallelPager=true, pageSize=1000, pageIndex=0)]"}
{"@timestamp":"2025-07-28T01:40:00.089Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-10","class":"c.x.i.t.u.h.s.i.UnifyAccessServiceImpl","rest":"url[http://*************:8844/remote/access/user/login]响应[<200,Wrapper(code=200, message=success, result=PlatLoginRespModel(token=RDbKcP5Z3mGwxeJB_015eff457c2541d58e24612429ba559f)),[Vary:\"Origin\", \"Access-Control-Request-Method\", \"Access-Control-Request-Headers\", Content-Type:\"application/json\", Content-Length:\"144\"]>]"}
{"@timestamp":"2025-07-28T01:40:00.111Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-1","class":"c.x.i.t.u.h.s.i.UnifyAccessServiceImpl","rest":"发送给接入返回结果result=JetlinksWrapper(message=success, status=200, code=200, timestamp=1753666798960, result=[DeviceCategoryRespModel(id=AIEdgeServer, parentId=-5-, key=AIEdgeServer, name=边缘计算服务器, productId=HISOME-DX000, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1747805356879, productName=汉尚多模态超脑), DeviceCategoryRespModel(id=cheating_prevention, parentId=-5-, key=CheatingPrevention, name=作弊防控设备, productId=HISOME-RRS3000, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1655261812143, productName=3536侦测服务器), DeviceCategoryRespModel(id=digital_hd_matrix, parentId=-5-, key=digital_hd_matrix, name=数字高清矩阵, productId=null, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1692077761801, productName=null), DeviceCategoryRespModel(id=ejjksb, parentId=-5-, key=ejjksb, name=耳机监考设备, productId=CENC_HEADPHONE, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1739411557430, productName=头戴式耳机), DeviceCategoryRespModel(id=hdd_box, parentId=-5-, key=hdd_box, name=视频存储盒子, productId=HISOME-HDD4000, sortIndex=1, level=2, creatorId=1199596756811550720, createTime=1680154724527, productName=视频数据存储终端), DeviceCategoryRespModel(id=hskpjmwxpb, parentId=-5-, key=hskpjmwxpb, name=汉尚考培加密无线平板, productId=null, sortIndex=1, level=1, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1740365256141, productName=null), DeviceCategoryRespModel(id=ipc, parentId=-5-, key=ipc, name=网络摄像机, productId=HISOME-IPC, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1691550353555, productName=IPC通用摄像机), DeviceCategoryRespModel(id=kcdzbp, parentId=-5-, key=kcdzbp, name=考场电子班牌, productId=PROD-DZBP, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1697418220328, productName=电子班牌), DeviceCategoryRespModel(id=kcwg, parentId=-5-, key=kcwg, name=考场网关, productId=CENCGW100_C, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1661844529888, productName=智能教室网关), DeviceCategoryRespModel(id=kdwg, parentId=-5-, key=kdwg, name=考点网关, productId=CENCGW100_S, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1661844519215, productName=智能校级网关), DeviceCategoryRespModel(id=lhsjkydzd, parentId=-5-, key=lhsjkydzd, name=理化生机考移动终端, productId=HISOME-PAD-101, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1740365218610, productName=汉尚理化生机考移动终端), DeviceCategoryRespModel(id=nvr, parentId=-5-, key=nvr, name=数字硬盘录像机, productId=HISOME-NVR4000, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1687656114753, productName=数字硬盘录像机), DeviceCategoryRespModel(id=sip, parentId=-5-, key=sip, name=SIP路由分发服务器, productId=SIP8000, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1689045772782, productName=SIP路由分发服务器), DeviceCategoryRespModel(id=tzsxj, parentId=-5-, key=tzsxj, name=同轴摄像机, productId=null, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1691552091707, productName=null), DeviceCategoryRespModel(id=ydzd, parentId=-5-, key=ydzd, name=移动终端, productId=HISOME_ANDROID, sortIndex=1, level=1, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1661844790208, productName=身份核验移动终端)])"}
{"@timestamp":"2025-07-28T01:40:00.113Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-1","class":"c.x.i.service.impl.SbSbxxServiceImpl","rest":""}
{"@timestamp":"2025-07-28T01:40:00.114Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.t.u.h.s.i.UnifyAccessServiceImpl","rest":"发送给接入返回结果result=JetlinksWrapper(message=success, status=200, code=200, timestamp=1753666798963, result=JetlinksPageRespModel(pageIndex=0, pageSize=1000, total=22, data=[JetlinksDeviceFirewareRespModel(id=00024700113e, deviceName=办公室超脑, productId=HISOME-DX000, version=2025.05.20.1807), JetlinksDeviceFirewareRespModel(id=00024720d16a, deviceName=36路办公室超脑, productId=HISOME-DX000, version=2025.05.24.2002), JetlinksDeviceFirewareRespModel(id=AB1602853, deviceName=考场电子班牌, productId=PROD-DZBP, version=1.5.6), JetlinksDeviceFirewareRespModel(id=ATN240303000100YT55YBF, deviceName=ATN240303000100YT55YBF, productId=HISOME_ANDROID, version=MI.T0.LP4X.4G#2.1.3_test3), JetlinksDeviceFirewareRespModel(id=ddcdb3ac5810283f, deviceName=ddcdb3ac5810283f, productId=HISOME_ANDROID, version=MI.T0.LP4X.4G#2.1.8), JetlinksDeviceFirewareRespModel(id=DENCGW829_C04MB10BDJ027590, deviceName=柴行楼教学楼103_DENCGW829_C04MB10BDJ027590, productId=CENCGW100_C, version=2025.01.27), JetlinksDeviceFirewareRespModel(id=DENCGW829_C04MB10BDJ07548e, deviceName=柴行楼柴行楼zq002_DENCGW829_C04MB10BDJ07548e, productId=CENCGW100_C, version=2025.03.18), JetlinksDeviceFirewareRespModel(id=FC1836988, deviceName=DI思源楼1201考务FC1836988, productId=PROD-DZBP, version=1.3.3), JetlinksDeviceFirewareRespModel(id=FC1836989, deviceName=DI思源楼1203考场FC1836989, productId=PROD-DZBP, version=1.5.6), JetlinksDeviceFirewareRespModel(id=G04754401, deviceName=G04754401, productId=PROD-DZBP, version=1.1), JetlinksDeviceFirewareRespModel(id=G04754402, deviceName=G04754402, productId=PROD-DZBP, version=1.1), JetlinksDeviceFirewareRespModel(id=G04754403, deviceName=G04754403, productId=PROD-DZBP, version=1.1), JetlinksDeviceFirewareRespModel(id=G04754404, deviceName=G04754404, productId=PROD-DZBP, version=1.1), JetlinksDeviceFirewareRespModel(id=G04754405, deviceName=G04754405, productId=PROD-DZBP, version=1.1), JetlinksDeviceFirewareRespModel(id=G04754406, deviceName=致远楼致远楼2105_G04754406, productId=PROD-DZBP, version=1.5.6_c), JetlinksDeviceFirewareRespModel(id=G04754407, deviceName=G04754407, productId=PROD-DZBP, version=1.1), JetlinksDeviceFirewareRespModel(id=G04754408, deviceName=G04754408, productId=PROD-DZBP, version=1.1), JetlinksDeviceFirewareRespModel(id=G04754409, deviceName=G04754409, productId=PROD-DZBP, version=1.1), JetlinksDeviceFirewareRespModel(id=G16733827, deviceName=志勤楼教学楼207_G16733827, productId=PROD-DZBP, version=1.1.5), JetlinksDeviceFirewareRespModel(id=G16733836, deviceName=电子班牌测试, productId=PROD-DZBP, version=1.3.3_c), JetlinksDeviceFirewareRespModel(id=KEP20240707001, deviceName=黄河交通学院_身份核验移动终端2, productId=HISOME_ANDROID, version=20241224-1122#2.1.8), JetlinksDeviceFirewareRespModel(id=PID388E2207007544, deviceName=像素设备, productId=PROD-DZBP, version=1.1.6)]))"}
{"@timestamp":"2025-07-28T01:40:00.119Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.m.S.updateByExampleSelective","rest":"==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) "}
{"@timestamp":"2025-07-28T01:40:00.120Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.m.S.updateByExampleSelective","rest":"==> Parameters: 2025.05.20.1807(String), 2025-07-28 09:40:00.114(Timestamp), (String), 0(Integer), 0(String), 00024700113e(String)"}
{"@timestamp":"2025-07-28T01:40:00.120Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-1","class":"c.x.i.t.u.h.s.i.UnifyAccessServiceImpl","rest":"Emqx消息桥接查询订阅 url=http://*************:8844/remote/access/emqx/subscribers/list"}
{"@timestamp":"2025-07-28T01:40:00.133Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.m.S.updateByExampleSelective","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-28T01:40:00.134Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.m.S.updateByExampleSelective","rest":"==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) "}
{"@timestamp":"2025-07-28T01:40:00.135Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.m.S.updateByExampleSelective","rest":"==> Parameters: 2025.05.24.2002(String), 2025-07-28 09:40:00.133(Timestamp), (String), 0(Integer), 0(String), 00024720d16a(String)"}
{"@timestamp":"2025-07-28T01:40:00.147Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-1","class":"c.x.i.t.u.h.s.i.UnifyAccessServiceImpl","rest":"Emqx消息桥接查询订阅 result=Wrapper(code=200, message=success, result=EmqxSubscribersListRespModel(subscribers=[/HISOME_ANDROID/testYdzdXlh001/function/invoke, /CENCGW100_C/DENCGW829_C04MB10BDJ027590/function/invoke, /HISOME_ANDROID/testYdzd004/function/invoke, /PROD-DZBP/test0012/function/invoke, /PROD-DZBP/FC1836988/function/invoke, /HISOME_ANDROID/ddcdb3ac5810283f/function/invoke, /PROD-DZBP/G04754406/properties/read, /PROD-DZBP/FC1836989/function/invoke, /HISOME_ANDROID/KEP20240707001/function/invoke, /PROD-DZBP/AB1602853/function/invoke, /HISOME_ANDROID/testYdzdXlh002/function/invoke, /CENCGW100_S/CENCGW100_SIV1c9f717de4acfc99/function/invoke, /CENCGW100_S/CENCGW100_SIVb33ad3ede4acfc99/function/invoke, /PROD-DZBP/AB1602853/properties/read, /HISOME_ANDROID/ATN240303000100YT55YBF/function/invoke, /PROD-DZBP/G16733836/function/invoke, /HISOME_ANDROID/S30SZA2023140181/function/invoke, /CENCGW100_C/DENCGW829_C04MB10BDJ07548e/function/invoke, /PROD-DZBP/G04754406/function/invoke, /CENCGW100_S/CENCGW100_SIVb770d1a6a1ec5b44/function/invoke]))"}
{"@timestamp":"2025-07-28T01:40:00.148Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.m.S.updateByExampleSelective","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-28T01:40:00.149Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-10","class":"c.x.i.t.u.h.s.i.UnifyAccessServiceImpl","rest":"发送给接入返回结果result=Wrapper(code=200, message=success, result={deviceList=[{deviceName=00024700113e, status=0}, {deviceName=00024720d16a, status=0}, {deviceName=1231241157, status=0}, {deviceName=AB1602853, status=1}, {deviceName=ATN240303000100YT55YBF, status=0}, {deviceName=CENCGW100_S01MA00BBA000D1000E, status=0}, {deviceName=CENCGW100_SIVb770d1a6a1ec5b44, status=1}, {deviceName=cillum, status=0}, {deviceName=ddcdb3ac5810283f, status=0}, {deviceName=DENCGW829_C04MB10BDJ027590, status=1}, {deviceName=DENCGW829_C04MB10BDJ07548e, status=0}, {deviceName=FC1836988, status=1}, {deviceName=FC1836989, status=1}, {deviceName=G04754406, status=1}, {deviceName=G16733827, status=0}, {deviceName=G16733836, status=1}, {deviceName=ipsumadk, status=0}, {deviceName=KEP20240707001, status=1}, {deviceName=PID388E2207007544, status=0}, {deviceName=S30SZA2023140181, status=0}, {deviceName=test0012, status=0}, {deviceName=test01-xlh, status=0}, {deviceName=test091903, status=0}, {deviceName=testYdzd004, status=0}, {deviceName=testYdzdXlh001, status=0}, {deviceName=testYdzdXlh002, status=0}], totalRows=26})"}
{"@timestamp":"2025-07-28T01:40:00.150Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.m.S.updateByExampleSelective","rest":"==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) "}
{"@timestamp":"2025-07-28T01:40:00.150Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.m.S.updateByExampleSelective","rest":"==> Parameters: 1.5.6(String), 2025-07-28 09:40:00.148(Timestamp), (String), 0(Integer), 0(String), AB1602853(String)"}
{"@timestamp":"2025-07-28T01:40:00.151Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-1","class":"c.x.i.util.schedule.ScheduleJobExecutor","rest":"任务执行完毕，任务ID：23110111540401808639620095703040  总共耗时：147毫秒"}
{"@timestamp":"2025-07-28T01:40:00.151Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-1","class":"c.x.i.m.S.insertSelective","rest":"==>  Preparing: INSERT INTO schedule_job_log ( log_id,job_id,job_name,bean_name,result,times,create_time ) VALUES( ?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-28T01:40:00.152Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-1","class":"c.x.i.m.S.insertSelective","rest":"==> Parameters: 25072809400002268839962247984129(String), 23110111540401808639620095703040(String), jobSubscribeMqtt(String), subscriberDevInfoToMqttTaskService(String), 0(Integer), 147(Integer), 2025-07-28 09:40:00.004(Timestamp)"}
{"@timestamp":"2025-07-28T01:40:00.156Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-10","class":"c.x.i.m.S.updateByExampleSelective","rest":"==>  Preparing: UPDATE sb_sbxx SET zxzt = ?,zhzxsj = ?,ext_status = ? WHERE ( ( xlh in ( ? , ? , ? , ? , ? , ? , ? , ? ) ) ) "}
{"@timestamp":"2025-07-28T01:40:00.157Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-10","class":"c.x.i.m.S.updateByExampleSelective","rest":"==> Parameters: 1(String), 2025-07-28 09:40:00.155(Timestamp), 0(Integer), AB1602853(String), CENCGW100_SIVb770d1a6a1ec5b44(String), DENCGW829_C04MB10BDJ027590(String), FC1836988(String), FC1836989(String), G04754406(String), G16733836(String), KEP20240707001(String)"}
{"@timestamp":"2025-07-28T01:40:00.168Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.m.S.updateByExampleSelective","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-28T01:40:00.170Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.m.S.updateByExampleSelective","rest":"==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) "}
{"@timestamp":"2025-07-28T01:40:00.170Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.m.S.updateByExampleSelective","rest":"==> Parameters: 2.1.3_test3(String), 2025-07-28 09:40:00.168(Timestamp), MI.T0.LP4X.4G(String), 0(Integer), 0(String), ATN240303000100YT55YBF(String)"}
{"@timestamp":"2025-07-28T01:40:00.173Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-10","class":"c.x.i.m.S.updateByExampleSelective","rest":"<==    Updates: 40"}
{"@timestamp":"2025-07-28T01:40:00.174Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-10","class":"c.x.i.m.S.updateByExampleSelective","rest":"==>  Preparing: UPDATE sb_sbxx SET zxzt = ?,ext_status = ? WHERE ( ( xlh in ( ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? ) ) ) "}
{"@timestamp":"2025-07-28T01:40:00.175Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-10","class":"c.x.i.m.S.updateByExampleSelective","rest":"==> Parameters: 0(String), 0(Integer), 00024700113e(String), 00024720d16a(String), 1231241157(String), ATN240303000100YT55YBF(String), CENCGW100_S01MA00BBA000D1000E(String), cillum(String), ddcdb3ac5810283f(String), DENCGW829_C04MB10BDJ07548e(String), G16733827(String), ipsumadk(String), PID388E2207007544(String), S30SZA2023140181(String), test0012(String), test01-xlh(String), test091903(String), testYdzd004(String), testYdzdXlh001(String), testYdzdXlh002(String)"}
{"@timestamp":"2025-07-28T01:40:00.184Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.m.S.updateByExampleSelective","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-28T01:40:00.185Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.m.S.updateByExampleSelective","rest":"==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) "}
{"@timestamp":"2025-07-28T01:40:00.185Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.m.S.updateByExampleSelective","rest":"==> Parameters: 2.1.8(String), 2025-07-28 09:40:00.184(Timestamp), MI.T0.LP4X.4G(String), 0(Integer), 0(String), ddcdb3ac5810283f(String)"}
{"@timestamp":"2025-07-28T01:40:00.190Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-10","class":"c.x.i.m.S.updateByExampleSelective","rest":"<==    Updates: 31"}
{"@timestamp":"2025-07-28T01:40:00.190Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-10","class":"c.x.i.service.impl.SbSbxxServiceImpl","rest":"getCssbZxzt - 开始获取场所设备在线状态."}
{"@timestamp":"2025-07-28T01:40:00.193Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-10","class":"c.x.i.m.SbSbxxMapper.getCssbZxzt_COUNT","rest":"==>  Preparing: SELECT count(0) FROM sb_sbxx sb LEFT JOIN sb_sbcsgx gx ON sb.sbxxbh = gx.sbbh WHERE sb.sczt = '0' AND sb.sblx = '171' AND gx.csbh IS NOT NULL AND sb.xlh IS NOT NULL "}
{"@timestamp":"2025-07-28T01:40:00.193Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-10","class":"c.x.i.m.SbSbxxMapper.getCssbZxzt_COUNT","rest":"==> Parameters: "}
{"@timestamp":"2025-07-28T01:40:00.199Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.m.S.updateByExampleSelective","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-28T01:40:00.199Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-10","class":"c.x.i.m.SbSbxxMapper.getCssbZxzt_COUNT","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-28T01:40:00.200Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-10","class":"c.x.i.m.KsKsccMapper.selectByExample","rest":"==>  Preparing: SELECT bh,ksjhbh,ccm,kmm,ccmc,kmmc,kssj,jssj,yxcdsj,kssbqksj,ksrcsj,zc,kspch,cjsj,xgsj,xn,xq,scztw FROM ks_kscc WHERE ( ( scztw = ? and kssj <= ? and jssj >= ? ) ) "}
{"@timestamp":"2025-07-28T01:40:00.200Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-10","class":"c.x.i.m.KsKsccMapper.selectByExample","rest":"==> Parameters: 0(String), 2025-07-28 09:40:00.2(Timestamp), 2025-07-28 09:40:00.2(Timestamp)"}
{"@timestamp":"2025-07-28T01:40:00.202Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.m.S.updateByExampleSelective","rest":"==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) "}
{"@timestamp":"2025-07-28T01:40:00.202Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.m.S.updateByExampleSelective","rest":"==> Parameters: 2025.01.27(String), 2025-07-28 09:40:00.199(Timestamp), (String), 0(Integer), 0(String), DENCGW829_C04MB10BDJ027590(String)"}
{"@timestamp":"2025-07-28T01:40:00.207Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-10","class":"c.x.i.m.KsKsccMapper.selectByExample","rest":"<==      Total: 0"}
{"@timestamp":"2025-07-28T01:40:00.207Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-10","class":"c.x.i.service.impl.SbSbxxServiceImpl","rest":"getCssbZxzt - 获取场所设备在线状态. [OK]"}
{"@timestamp":"2025-07-28T01:40:00.207Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-10","class":"c.x.i.service.impl.SbSbxxServiceImpl","rest":"========设备在线状态更新完成========="}
{"@timestamp":"2025-07-28T01:40:00.207Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-10","class":"c.x.i.util.schedule.ScheduleJobExecutor","rest":"任务执行完毕，任务ID：23110110440401808639620095703040  总共耗时：203毫秒"}
{"@timestamp":"2025-07-28T01:40:00.209Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-10","class":"c.x.i.m.S.insertSelective","rest":"==>  Preparing: INSERT INTO schedule_job_log ( log_id,job_id,job_name,bean_name,result,times,create_time ) VALUES( ?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-28T01:40:00.209Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-10","class":"c.x.i.m.S.insertSelective","rest":"==> Parameters: 25072809400002268839962247984128(String), 23110110440401808639620095703040(String), jobSbzxztGx(String), sbzxztCxAndGxTaskService(String), 0(Integer), 203(Integer), 2025-07-28 09:40:00.004(Timestamp)"}
{"@timestamp":"2025-07-28T01:40:00.216Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.m.S.updateByExampleSelective","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-28T01:40:00.218Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.m.S.updateByExampleSelective","rest":"==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) "}
{"@timestamp":"2025-07-28T01:40:00.218Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.m.S.updateByExampleSelective","rest":"==> Parameters: 2025.03.18(String), 2025-07-28 09:40:00.217(Timestamp), (String), 0(Integer), 0(String), DENCGW829_C04MB10BDJ07548e(String)"}
{"@timestamp":"2025-07-28T01:40:00.233Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.m.S.updateByExampleSelective","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-28T01:40:00.234Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.m.S.updateByExampleSelective","rest":"==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) "}
{"@timestamp":"2025-07-28T01:40:00.235Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.m.S.updateByExampleSelective","rest":"==> Parameters: 1.3.3(String), 2025-07-28 09:40:00.234(Timestamp), (String), 0(Integer), 0(String), FC1836988(String)"}
{"@timestamp":"2025-07-28T01:40:00.250Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.m.S.updateByExampleSelective","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-28T01:40:00.251Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.m.S.updateByExampleSelective","rest":"==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) "}
{"@timestamp":"2025-07-28T01:40:00.251Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.m.S.updateByExampleSelective","rest":"==> Parameters: 1.5.6(String), 2025-07-28 09:40:00.25(Timestamp), (String), 0(Integer), 0(String), FC1836989(String)"}
{"@timestamp":"2025-07-28T01:40:00.266Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.m.S.updateByExampleSelective","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-28T01:40:00.268Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.m.S.updateByExampleSelective","rest":"==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) "}
{"@timestamp":"2025-07-28T01:40:00.268Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.m.S.updateByExampleSelective","rest":"==> Parameters: 1.1(String), 2025-07-28 09:40:00.266(Timestamp), (String), 0(Integer), 0(String), G04754401(String)"}
{"@timestamp":"2025-07-28T01:40:00.280Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.m.S.updateByExampleSelective","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-28T01:40:00.281Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.m.S.updateByExampleSelective","rest":"==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) "}
{"@timestamp":"2025-07-28T01:40:00.281Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.m.S.updateByExampleSelective","rest":"==> Parameters: 1.1(String), 2025-07-28 09:40:00.28(Timestamp), (String), 0(Integer), 0(String), G04754402(String)"}
{"@timestamp":"2025-07-28T01:40:00.294Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.m.S.updateByExampleSelective","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-28T01:40:00.295Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.m.S.updateByExampleSelective","rest":"==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) "}
{"@timestamp":"2025-07-28T01:40:00.296Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.m.S.updateByExampleSelective","rest":"==> Parameters: 1.1(String), 2025-07-28 09:40:00.294(Timestamp), (String), 0(Integer), 0(String), G04754403(String)"}
{"@timestamp":"2025-07-28T01:40:00.308Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.m.S.updateByExampleSelective","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-28T01:40:00.309Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.m.S.updateByExampleSelective","rest":"==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) "}
{"@timestamp":"2025-07-28T01:40:00.309Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.m.S.updateByExampleSelective","rest":"==> Parameters: 1.1(String), 2025-07-28 09:40:00.309(Timestamp), (String), 0(Integer), 0(String), G04754404(String)"}
{"@timestamp":"2025-07-28T01:40:00.322Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.m.S.updateByExampleSelective","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-28T01:40:00.323Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.m.S.updateByExampleSelective","rest":"==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) "}
{"@timestamp":"2025-07-28T01:40:00.323Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.m.S.updateByExampleSelective","rest":"==> Parameters: 1.1(String), 2025-07-28 09:40:00.322(Timestamp), (String), 0(Integer), 0(String), G04754405(String)"}
{"@timestamp":"2025-07-28T01:40:00.335Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.m.S.updateByExampleSelective","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-28T01:40:00.336Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.m.S.updateByExampleSelective","rest":"==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) "}
{"@timestamp":"2025-07-28T01:40:00.337Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.m.S.updateByExampleSelective","rest":"==> Parameters: 1.5.6_c(String), 2025-07-28 09:40:00.336(Timestamp), (String), 0(Integer), 0(String), G04754406(String)"}
{"@timestamp":"2025-07-28T01:40:00.352Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.m.S.updateByExampleSelective","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-28T01:40:00.353Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.m.S.updateByExampleSelective","rest":"==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) "}
{"@timestamp":"2025-07-28T01:40:00.354Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.m.S.updateByExampleSelective","rest":"==> Parameters: 1.1(String), 2025-07-28 09:40:00.352(Timestamp), (String), 0(Integer), 0(String), G04754407(String)"}
{"@timestamp":"2025-07-28T01:40:00.367Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.m.S.updateByExampleSelective","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-28T01:40:00.369Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.m.S.updateByExampleSelective","rest":"==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) "}
{"@timestamp":"2025-07-28T01:40:00.369Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.m.S.updateByExampleSelective","rest":"==> Parameters: 1.1(String), 2025-07-28 09:40:00.368(Timestamp), (String), 0(Integer), 0(String), G04754408(String)"}
{"@timestamp":"2025-07-28T01:40:00.382Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.m.S.updateByExampleSelective","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-28T01:40:00.383Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.m.S.updateByExampleSelective","rest":"==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) "}
{"@timestamp":"2025-07-28T01:40:00.384Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.m.S.updateByExampleSelective","rest":"==> Parameters: 1.1(String), 2025-07-28 09:40:00.382(Timestamp), (String), 0(Integer), 0(String), G04754409(String)"}
{"@timestamp":"2025-07-28T01:40:00.397Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.m.S.updateByExampleSelective","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-28T01:40:00.398Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.m.S.updateByExampleSelective","rest":"==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) "}
{"@timestamp":"2025-07-28T01:40:00.398Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.m.S.updateByExampleSelective","rest":"==> Parameters: 1.1.5(String), 2025-07-28 09:40:00.397(Timestamp), (String), 0(Integer), 0(String), G16733827(String)"}
{"@timestamp":"2025-07-28T01:40:00.411Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.m.S.updateByExampleSelective","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-28T01:40:00.412Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.m.S.updateByExampleSelective","rest":"==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) "}
{"@timestamp":"2025-07-28T01:40:00.412Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.m.S.updateByExampleSelective","rest":"==> Parameters: 1.3.3_c(String), 2025-07-28 09:40:00.411(Timestamp), (String), 0(Integer), 0(String), G16733836(String)"}
{"@timestamp":"2025-07-28T01:40:00.427Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.m.S.updateByExampleSelective","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-28T01:40:00.429Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.m.S.updateByExampleSelective","rest":"==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) "}
{"@timestamp":"2025-07-28T01:40:00.429Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.m.S.updateByExampleSelective","rest":"==> Parameters: 2.1.8(String), 2025-07-28 09:40:00.428(Timestamp), 20241224-1122(String), 0(Integer), 0(String), KEP20240707001(String)"}
{"@timestamp":"2025-07-28T01:40:00.444Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.m.S.updateByExampleSelective","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-28T01:40:00.445Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.m.S.updateByExampleSelective","rest":"==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) "}
{"@timestamp":"2025-07-28T01:40:00.445Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.m.S.updateByExampleSelective","rest":"==> Parameters: 1.1.6(String), 2025-07-28 09:40:00.444(Timestamp), (String), 0(Integer), 0(String), PID388E2207007544(String)"}
{"@timestamp":"2025-07-28T01:40:00.458Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.m.S.updateByExampleSelective","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-28T01:40:00.458Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.service.impl.SbSbxxServiceImpl","rest":"固件版本更新10条"}
{"@timestamp":"2025-07-28T01:40:00.459Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.service.impl.SbSbxxServiceImpl","rest":"固件版本更新 OK, 共计更新10条"}
{"@timestamp":"2025-07-28T01:40:00.459Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.util.schedule.ScheduleJobExecutor","rest":"任务执行完毕，任务ID：2502130940211339531603881230336  总共耗时：454毫秒"}
{"@timestamp":"2025-07-28T01:40:00.459Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.m.S.insertSelective","rest":"==>  Preparing: INSERT INTO schedule_job_log ( log_id,job_id,job_name,bean_name,params,result,times,create_time ) VALUES( ?,?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-28T01:40:00.459Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.m.S.insertSelective","rest":"==> Parameters: 25072809400002268839962256372737(String), 2502130940211339531603881230336(String), 设备固件版本更新(String), deviceVersionUpdateTaskService(String), (String), 0(Integer), 454(Integer), 2025-07-28 09:40:00.005(Timestamp)"}
{"@timestamp":"2025-07-28T01:40:49.026Z","severity":"INFO","service":"eeip-standalone-service","trace":"bcf5da029679f519","span":"bcf5da029679f519","parent":"","exportable":"false","pid":"15324","thread":"http-nio-8888-exec-6","class":"c.x.i.facade.manager.CxtjController","rest":"收到请求开始：[紧急呼叫查询][/manager/identityverify/cxtj/jjhjcx]reqModel:JjhjCxReq[ksjhbh=2507211439231396864138952572928,kcbh=<null>,clzt=<null>,startTime=<null>,endTime=<null>,pageNum=1,pageSize=10,msgId=<null>,transChannel=<null>,channelSerialNo=<null>,channelDate=<null>,channelTime=<null>,token=<null>,loginToken=<null>,transCode=<null>,clientIp=<null>,merchantId=<null>,appPackage=<null>]"}
{"@timestamp":"2025-07-28T01:40:49.039Z","severity":"INFO","service":"eeip-standalone-service","trace":"bcf5da029679f519","span":"bcf5da029679f519","parent":"","exportable":"false","pid":"15324","thread":"http-nio-8888-exec-6","class":"c.x.i.s.impl.KsKwMessageServiceImpl","rest":"紧急呼叫查询开始，查询条件：JjhjCxDTO[ksjhbh=2507211439231396864138952572928,kcbh=<null>,clzt=<null>,startTime=<null>,endTime=<null>,pageNum=1,pageSize=10]"}
{"@timestamp":"2025-07-28T01:40:49.041Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"bcf5da029679f519","span":"bcf5da029679f519","parent":"","exportable":"false","pid":"15324","thread":"http-nio-8888-exec-6","class":"c.x.i.m.K.selectJjhjList_COUNT","rest":"==>  Preparing: SELECT count(0) FROM ks_kw_message msg LEFT JOIN ks_kw_message reply ON msg.ksjhbh = reply.ksjhbh AND msg.id = reply.replay_id AND reply.sczt = '0' WHERE msg.sczt = '0' AND msg.msg_type = '2000' AND msg.origin_app_type = 'HYAPP' AND msg.operate = '0' AND msg.ksjhbh = ? "}
{"@timestamp":"2025-07-28T01:40:49.041Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"bcf5da029679f519","span":"bcf5da029679f519","parent":"","exportable":"false","pid":"15324","thread":"http-nio-8888-exec-6","class":"c.x.i.m.K.selectJjhjList_COUNT","rest":"==> Parameters: 2507211439231396864138952572928(String)"}
{"@timestamp":"2025-07-28T01:40:49.049Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"bcf5da029679f519","span":"bcf5da029679f519","parent":"","exportable":"false","pid":"15324","thread":"http-nio-8888-exec-6","class":"c.x.i.m.K.selectJjhjList_COUNT","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-28T01:40:49.049Z","severity":"INFO","service":"eeip-standalone-service","trace":"bcf5da029679f519","span":"bcf5da029679f519","parent":"","exportable":"false","pid":"15324","thread":"http-nio-8888-exec-6","class":"c.x.i.s.impl.KsKwMessageServiceImpl","rest":"紧急呼叫查询结束，共查询到0条记录"}
{"@timestamp":"2025-07-28T01:40:49.073Z","severity":"INFO","service":"eeip-standalone-service","trace":"bcf5da029679f519","span":"bcf5da029679f519","parent":"","exportable":"false","pid":"15324","thread":"http-nio-8888-exec-6","class":"c.x.i.facade.manager.CxtjController","rest":"处理请求结束：[紧急呼叫查询][/manager/identityverify/cxtj/jjhjcx]reqModel:JjhjCxReq[ksjhbh=2507211439231396864138952572928,kcbh=<null>,clzt=<null>,startTime=<null>,endTime=<null>,pageNum=1,pageSize=10,msgId=<null>,transChannel=<null>,channelSerialNo=<null>,channelDate=<null>,channelTime=<null>,token=<null>,loginToken=<null>,transCode=<null>,clientIp=<null>,merchantId=<null>,appPackage=<null>],respModel:JjhjCxResp[data=Page{count=true, pageNum=1, pageSize=10, startRow=0, endRow=10, total=0, pages=0, reasonable=true, pageSizeZero=false}[],totalRows=0,pageNum=1,pageSize=10,msgId=<null>,transChannel=<null>,channelSerialNo=<null>,transCode=<null>,respDate=<null>,respTime=<null>,ext=<null>]"}
{"@timestamp":"2025-07-28T01:42:00.015Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-5","class":"c.x.i.util.schedule.ScheduleJobExecutor","rest":"任务准备执行，任务ID：23110110740401808639620095703040"}
{"@timestamp":"2025-07-28T01:42:00.023Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-5","class":"c.x.i.util.schedule.ScheduleJobExecutor","rest":"任务执行完毕，任务ID：23110110740401808639620095703040  总共耗时：8毫秒"}
{"@timestamp":"2025-07-28T01:42:00.030Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-5","class":"c.x.i.m.S.insertSelective","rest":"==>  Preparing: INSERT INTO schedule_job_log ( log_id,job_id,job_name,bean_name,result,times,create_time ) VALUES( ?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-28T01:42:00.030Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-5","class":"c.x.i.m.S.insertSelective","rest":"==> Parameters: 25072809420002268840968973218816(String), 23110110740401808639620095703040(String), jobOnlineStatus(String), onlineStatusTaskService(String), 0(Integer), 8(Integer), 2025-07-28 09:42:00.015(Timestamp)"}
{"@timestamp":"2025-07-28T01:42:01.466Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"MQTT Rec: identityVerify_test01","class":"o.s.i.m.outbound.MqttPahoMessageHandler","rest":"Lost connection; will attempt reconnect on next request"}
{"@timestamp":"2025-07-28T01:45:00.002Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-2","class":"c.x.i.util.schedule.ScheduleJobExecutor","rest":"任务准备执行，任务ID：24081610292802018092458168076288"}
{"@timestamp":"2025-07-28T01:45:00.009Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-2","class":"c.x.i.m.KsKsjhMapper.selectByExample","rest":"==>  Preparing: SELECT ksjhbh,mc,kssj,jssj,xn,xq,sfmr,kszt,sbjm,sbkqsj,sbgbsj,cjlx,kslx,qysbbmd,wifiqy,cjsj,xgsj,scztw,kzqy,qydsxf,dsxfsj,dsxfzt,jkqdrs,dbms FROM ks_ksjh WHERE ( ( scztw = ? and qydsxf = ? and dsxfzt = ? ) ) "}
{"@timestamp":"2025-07-28T01:45:00.009Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-2","class":"c.x.i.m.KsKsjhMapper.selectByExample","rest":"==> Parameters: 0(String), 1(Integer), 0(Integer)"}
{"@timestamp":"2025-07-28T01:45:00.016Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-2","class":"c.x.i.m.KsKsjhMapper.selectByExample","rest":"<==      Total: 0"}
{"@timestamp":"2025-07-28T01:45:00.017Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-2","class":"c.x.i.s.i.s.KsjhSjdsxfTaskServiceImpl","rest":"当前无下发任务！"}
{"@timestamp":"2025-07-28T01:45:00.017Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-2","class":"c.x.i.util.schedule.ScheduleJobExecutor","rest":"任务执行完毕，任务ID：24081610292802018092458168076288  总共耗时：15毫秒"}
{"@timestamp":"2025-07-28T01:45:00.017Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-2","class":"c.x.i.m.S.insertSelective","rest":"==>  Preparing: INSERT INTO schedule_job_log ( log_id,job_id,job_name,bean_name,params,result,times,create_time ) VALUES( ?,?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-28T01:45:00.017Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-2","class":"c.x.i.m.S.insertSelective","rest":"==> Parameters: 25072809450002268842478813606912(String), 24081610292802018092458168076288(String), ksjhSjdsxf(String), ksjhSjdsxfTaskService(String), (String), 0(Integer), 15(Integer), 2025-07-28 09:45:00.002(Timestamp)"}
{"@timestamp":"2025-07-28T01:46:47.797Z","severity":"INFO","service":"eeip-standalone-service","trace":"b976db99a6c599ae","span":"162491289376f0a6","parent":"1b04efd2aa2b9310","exportable":"false","pid":"15324","thread":"ThreadPoolTaskExecutor-1","class":"c.x.b.u.m.service.impl.MqttMsgRevHandler","rest":"GenericMessage [payload={\"data\":{\"OPERATION\":\"NOTIFY_DEVEVENT_YDZDBATTERY\",\"TIMESTAMP\":1753667205596,\"Data\":{\"devType\":\"172\",\"sbcsbh\":\"K3307820091100001\",\"eventTime\":\"1753667205596\",\"batteryVal\":100,\"eventType\":\"YDZDBATTERY\",\"sn\":\"KEP20240707001\"}},\"sign\":\"\",\"messageId\":\"batteryVal_1753667205596_KEP20240707001\",\"timestamp\":1753667205596,\"token\":\"ee98d86d3ccc48a2a366770e8ffb7567\"}, headers={mqtt_receivedRetained=false, spanTraceId=b976db99a6c599ae, spanId=b976db99a6c599ae, nativeHeaders={spanTraceId=[b976db99a6c599ae], spanId=[b976db99a6c599ae], spanSampled=[0]}, mqtt_duplicate=false, id=b23c7ee1-0533-bfac-18f1-a02d386b0134, spanSampled=0, mqtt_receivedTopic=/HISOME_ANDROID/KEP20240707001/event/REPORT_DEVEVENT, mqtt_receivedQos=1}]"}
{"@timestamp":"2025-07-28T01:46:47.804Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"ThreadPoolTaskExecutor-2","class":"c.x.r.s.impl.BasicMqttMsgRevHandler","rest":"topic[/HISOME_ANDROID/KEP20240707001/event/REPORT_DEVEVENT] 没有方法可以处理这个主题..."}
{"@timestamp":"2025-07-28T01:46:47.804Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"ThreadPoolTaskExecutor-3","class":"c.x.a.service.impl.BizMqttMsgRevHandler","rest":"topic[/HISOME_ANDROID/KEP20240707001/event/REPORT_DEVEVENT] 没有方法可以处理这个主题..."}
{"@timestamp":"2025-07-28T01:46:47.805Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"ThreadPoolTaskExecutor-1","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"judgeDeviceIdExist:KEP20240707001"}
{"@timestamp":"2025-07-28T01:46:47.809Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"ThreadPoolTaskExecutor-1","class":"c.x.i.s.i.m.MqttReportDeveventEvantHandler","rest":"处理mqtt上报设备事件消息:BaseOperationReq(operation=NOTIFY_DEVEVENT_YDZDBATTERY, timestamp=1753667205596, data={\"devType\":\"172\",\"sbcsbh\":\"K3307820091100001\",\"eventTime\":\"1753667205596\",\"batteryVal\":100,\"eventType\":\"YDZDBATTERY\",\"sn\":\"KEP20240707001\"})"}
{"@timestamp":"2025-07-28T01:46:47.827Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"ThreadPoolTaskExecutor-1","class":"c.x.i.m.S.updateZxztAndNtpByXlh","rest":"==>  Preparing: update sb_sbxx SET zxzt = ?, battery = ?, update_time = now() where xlh = ? "}
{"@timestamp":"2025-07-28T01:46:47.829Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"ThreadPoolTaskExecutor-1","class":"c.x.i.m.S.updateZxztAndNtpByXlh","rest":"==> Parameters: 1(String), 100(String), KEP20240707001(String)"}
{"@timestamp":"2025-07-28T01:46:47.844Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"ThreadPoolTaskExecutor-1","class":"c.x.i.m.S.updateZxztAndNtpByXlh","rest":"<==    Updates: 6"}
{"@timestamp":"2025-07-28T01:46:47.845Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"ThreadPoolTaskExecutor-1","class":"c.x.i.m.SbSbxxMapper.selectByExample","rest":"==>  Preparing: SELECT sbxxbh,sbmc,sbxh,sbbm,sbpp,sbcs,sblx,sblxmc,jspc,jssj,sbccrq,sbazrq,sbzbq,sbwldz,macdz,ipdz,dkh,czxt,kzdkh,appbb,firmwarebb,xlh,ramdiskbb,sbzt,jhzt,sbmy,sczt,zxzt,sbzpid,sbqm,sbjcjg,zhzxsj,create_time,update_time,sbztw,sfydsb,osbb,battery,ntp_enable,ntp_source,ntp_interval,ext_status FROM sb_sbxx WHERE ( ( sczt = ? and xlh = ? ) ) "}
{"@timestamp":"2025-07-28T01:46:47.845Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"ThreadPoolTaskExecutor-1","class":"c.x.i.m.SbSbxxMapper.selectByExample","rest":"==> Parameters: 0(String), KEP20240707001(String)"}
{"@timestamp":"2025-07-28T01:46:47.853Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"ThreadPoolTaskExecutor-1","class":"c.x.i.m.SbSbxxMapper.selectByExample","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-28T01:46:47.853Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"ThreadPoolTaskExecutor-1","class":"c.x.i.s.i.m.MqttReportDeveventEvantHandler","rest":"上报电量大于阈值, 不记录日志"}
{"@timestamp":"2025-07-28T01:49:00.013Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-6","class":"c.x.i.util.schedule.ScheduleJobExecutor","rest":"任务准备执行，任务ID：23110110740401808639620095703040"}
{"@timestamp":"2025-07-28T01:49:00.019Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-6","class":"c.x.i.util.schedule.ScheduleJobExecutor","rest":"任务执行完毕，任务ID：23110110740401808639620095703040  总共耗时：6毫秒"}
{"@timestamp":"2025-07-28T01:49:00.029Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-6","class":"c.x.i.m.S.insertSelective","rest":"==>  Preparing: INSERT INTO schedule_job_log ( log_id,job_id,job_name,bean_name,result,times,create_time ) VALUES( ?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-28T01:49:00.029Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-6","class":"c.x.i.m.S.insertSelective","rest":"==> Parameters: 25072809490002268844492171801600(String), 23110110740401808639620095703040(String), jobOnlineStatus(String), onlineStatusTaskService(String), 0(Integer), 6(Integer), 2025-07-28 09:49:00.013(Timestamp)"}
{"@timestamp":"2025-07-28T01:49:01.471Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"MQTT Rec: identityVerify_test01","class":"o.s.i.m.outbound.MqttPahoMessageHandler","rest":"Lost connection; will attempt reconnect on next request"}
{"@timestamp":"2025-07-28T01:50:00.006Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-4","class":"c.x.i.util.schedule.ScheduleJobExecutor","rest":"任务准备执行，任务ID：23110111540401808639620095703040"}
{"@timestamp":"2025-07-28T01:50:00.006Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-9","class":"c.x.i.util.schedule.ScheduleJobExecutor","rest":"任务准备执行，任务ID：24081610292802018092458168076288"}
{"@timestamp":"2025-07-28T01:50:00.006Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-4","class":"c.x.i.m.SbSbxxMapper.selectByExample","rest":"==>  Preparing: SELECT sbxxbh,sbmc,sbxh,sbbm,sbpp,sbcs,sblx,sblxmc,jspc,jssj,sbccrq,sbazrq,sbzbq,sbwldz,macdz,ipdz,dkh,czxt,kzdkh,appbb,firmwarebb,xlh,ramdiskbb,sbzt,jhzt,sbmy,sczt,zxzt,sbzpid,sbqm,sbjcjg,zhzxsj,create_time,update_time,sbztw,sfydsb,osbb,battery,ntp_enable,ntp_source,ntp_interval,ext_status FROM sb_sbxx WHERE ( ( sczt = ? ) ) "}
{"@timestamp":"2025-07-28T01:50:00.006Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-4","class":"c.x.i.m.SbSbxxMapper.selectByExample","rest":"==> Parameters: 0(String)"}
{"@timestamp":"2025-07-28T01:50:00.015Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-9","class":"c.x.i.m.KsKsjhMapper.selectByExample","rest":"==>  Preparing: SELECT ksjhbh,mc,kssj,jssj,xn,xq,sfmr,kszt,sbjm,sbkqsj,sbgbsj,cjlx,kslx,qysbbmd,wifiqy,cjsj,xgsj,scztw,kzqy,qydsxf,dsxfsj,dsxfzt,jkqdrs,dbms FROM ks_ksjh WHERE ( ( scztw = ? and qydsxf = ? and dsxfzt = ? ) ) "}
{"@timestamp":"2025-07-28T01:50:00.015Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-9","class":"c.x.i.m.KsKsjhMapper.selectByExample","rest":"==> Parameters: 0(String), 1(Integer), 0(Integer)"}
{"@timestamp":"2025-07-28T01:50:00.019Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-4","class":"c.x.i.m.SbSbxxMapper.selectByExample","rest":"<==      Total: 16"}
{"@timestamp":"2025-07-28T01:50:00.019Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-4","class":"c.x.i.t.u.h.s.i.UnifyAccessServiceImpl","rest":"url http://*************:8844/device/category?terms[0].column=parentId&terms[0].value=-5-"}
{"@timestamp":"2025-07-28T01:50:00.022Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-9","class":"c.x.i.m.KsKsjhMapper.selectByExample","rest":"<==      Total: 0"}
{"@timestamp":"2025-07-28T01:50:00.022Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-9","class":"c.x.i.s.i.s.KsjhSjdsxfTaskServiceImpl","rest":"当前无下发任务！"}
{"@timestamp":"2025-07-28T01:50:00.022Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-9","class":"c.x.i.util.schedule.ScheduleJobExecutor","rest":"任务执行完毕，任务ID：24081610292802018092458168076288  总共耗时：16毫秒"}
{"@timestamp":"2025-07-28T01:50:00.022Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-9","class":"c.x.i.m.S.insertSelective","rest":"==>  Preparing: INSERT INTO schedule_job_log ( log_id,job_id,job_name,bean_name,params,result,times,create_time ) VALUES( ?,?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-28T01:50:00.022Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-9","class":"c.x.i.m.S.insertSelective","rest":"==> Parameters: 25072809500002268844995429561345(String), 24081610292802018092458168076288(String), ksjhSjdsxf(String), ksjhSjdsxfTaskService(String), (String), 0(Integer), 16(Integer), 2025-07-28 09:50:00.006(Timestamp)"}
{"@timestamp":"2025-07-28T01:50:00.102Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-4","class":"c.x.i.t.u.h.s.i.UnifyAccessServiceImpl","rest":"发送给接入返回结果result=JetlinksWrapper(message=success, status=200, code=200, timestamp=1753667398942, result=[DeviceCategoryRespModel(id=AIEdgeServer, parentId=-5-, key=AIEdgeServer, name=边缘计算服务器, productId=HISOME-DX000, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1747805356879, productName=汉尚多模态超脑), DeviceCategoryRespModel(id=cheating_prevention, parentId=-5-, key=CheatingPrevention, name=作弊防控设备, productId=HISOME-RRS3000, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1655261812143, productName=3536侦测服务器), DeviceCategoryRespModel(id=digital_hd_matrix, parentId=-5-, key=digital_hd_matrix, name=数字高清矩阵, productId=null, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1692077761801, productName=null), DeviceCategoryRespModel(id=ejjksb, parentId=-5-, key=ejjksb, name=耳机监考设备, productId=CENC_HEADPHONE, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1739411557430, productName=头戴式耳机), DeviceCategoryRespModel(id=hdd_box, parentId=-5-, key=hdd_box, name=视频存储盒子, productId=HISOME-HDD4000, sortIndex=1, level=2, creatorId=1199596756811550720, createTime=1680154724527, productName=视频数据存储终端), DeviceCategoryRespModel(id=hskpjmwxpb, parentId=-5-, key=hskpjmwxpb, name=汉尚考培加密无线平板, productId=null, sortIndex=1, level=1, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1740365256141, productName=null), DeviceCategoryRespModel(id=ipc, parentId=-5-, key=ipc, name=网络摄像机, productId=HISOME-IPC, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1691550353555, productName=IPC通用摄像机), DeviceCategoryRespModel(id=kcdzbp, parentId=-5-, key=kcdzbp, name=考场电子班牌, productId=PROD-DZBP, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1697418220328, productName=电子班牌), DeviceCategoryRespModel(id=kcwg, parentId=-5-, key=kcwg, name=考场网关, productId=CENCGW100_C, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1661844529888, productName=智能教室网关), DeviceCategoryRespModel(id=kdwg, parentId=-5-, key=kdwg, name=考点网关, productId=CENCGW100_S, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1661844519215, productName=智能校级网关), DeviceCategoryRespModel(id=lhsjkydzd, parentId=-5-, key=lhsjkydzd, name=理化生机考移动终端, productId=HISOME-PAD-101, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1740365218610, productName=汉尚理化生机考移动终端), DeviceCategoryRespModel(id=nvr, parentId=-5-, key=nvr, name=数字硬盘录像机, productId=HISOME-NVR4000, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1687656114753, productName=数字硬盘录像机), DeviceCategoryRespModel(id=sip, parentId=-5-, key=sip, name=SIP路由分发服务器, productId=SIP8000, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1689045772782, productName=SIP路由分发服务器), DeviceCategoryRespModel(id=tzsxj, parentId=-5-, key=tzsxj, name=同轴摄像机, productId=null, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1691552091707, productName=null), DeviceCategoryRespModel(id=ydzd, parentId=-5-, key=ydzd, name=移动终端, productId=HISOME_ANDROID, sortIndex=1, level=1, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1661844790208, productName=身份核验移动终端)])"}
{"@timestamp":"2025-07-28T01:50:00.103Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-4","class":"c.x.i.service.impl.SbSbxxServiceImpl","rest":""}
{"@timestamp":"2025-07-28T01:50:00.109Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-4","class":"c.x.i.t.u.h.s.i.UnifyAccessServiceImpl","rest":"Emqx消息桥接查询订阅 url=http://*************:8844/remote/access/emqx/subscribers/list"}
{"@timestamp":"2025-07-28T01:50:00.135Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-4","class":"c.x.i.t.u.h.s.i.UnifyAccessServiceImpl","rest":"Emqx消息桥接查询订阅 result=Wrapper(code=200, message=success, result=EmqxSubscribersListRespModel(subscribers=[/HISOME_ANDROID/testYdzdXlh001/function/invoke, /CENCGW100_C/DENCGW829_C04MB10BDJ027590/function/invoke, /HISOME_ANDROID/testYdzd004/function/invoke, /PROD-DZBP/test0012/function/invoke, /PROD-DZBP/FC1836988/function/invoke, /HISOME_ANDROID/ddcdb3ac5810283f/function/invoke, /PROD-DZBP/G04754406/properties/read, /PROD-DZBP/FC1836989/function/invoke, /HISOME_ANDROID/KEP20240707001/function/invoke, /PROD-DZBP/AB1602853/function/invoke, /HISOME_ANDROID/testYdzdXlh002/function/invoke, /CENCGW100_S/CENCGW100_SIV1c9f717de4acfc99/function/invoke, /CENCGW100_S/CENCGW100_SIVb33ad3ede4acfc99/function/invoke, /PROD-DZBP/AB1602853/properties/read, /HISOME_ANDROID/ATN240303000100YT55YBF/function/invoke, /PROD-DZBP/G16733836/function/invoke, /HISOME_ANDROID/S30SZA2023140181/function/invoke, /CENCGW100_C/DENCGW829_C04MB10BDJ07548e/function/invoke, /PROD-DZBP/G04754406/function/invoke, /CENCGW100_S/CENCGW100_SIVb770d1a6a1ec5b44/function/invoke]))"}
{"@timestamp":"2025-07-28T01:50:00.135Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-4","class":"c.x.i.util.schedule.ScheduleJobExecutor","rest":"任务执行完毕，任务ID：23110111540401808639620095703040  总共耗时：129毫秒"}
{"@timestamp":"2025-07-28T01:50:00.135Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-4","class":"c.x.i.m.S.insertSelective","rest":"==>  Preparing: INSERT INTO schedule_job_log ( log_id,job_id,job_name,bean_name,result,times,create_time ) VALUES( ?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-28T01:50:00.135Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-4","class":"c.x.i.m.S.insertSelective","rest":"==> Parameters: 25072809500002268844995429561344(String), 23110111540401808639620095703040(String), jobSubscribeMqtt(String), subscriberDevInfoToMqttTaskService(String), 0(Integer), 129(Integer), 2025-07-28 09:50:00.006(Timestamp)"}
{"@timestamp":"2025-07-28T01:54:49.464Z","severity":"INFO","service":"eeip-standalone-service","trace":"e16a279223e6fe57","span":"e16a279223e6fe57","parent":"","exportable":"false","pid":"15324","thread":"http-nio-8888-exec-9","class":"c.x.i.facade.manager.CxtjController","rest":"收到请求开始：[紧急呼叫查询][/manager/identityverify/cxtj/jjhjcx]reqModel:JjhjCxReq[ksjhbh=2507071431551391788832445169664,kcbh=<null>,clzt=<null>,startTime=<null>,endTime=<null>,pageNum=1,pageSize=10,msgId=<null>,transChannel=<null>,channelSerialNo=<null>,channelDate=<null>,channelTime=<null>,token=<null>,loginToken=<null>,transCode=<null>,clientIp=<null>,merchantId=<null>,appPackage=<null>]"}
{"@timestamp":"2025-07-28T01:54:49.485Z","severity":"INFO","service":"eeip-standalone-service","trace":"e16a279223e6fe57","span":"e16a279223e6fe57","parent":"","exportable":"false","pid":"15324","thread":"http-nio-8888-exec-9","class":"c.x.i.s.impl.KsKwMessageServiceImpl","rest":"紧急呼叫查询开始，查询条件：JjhjCxDTO[ksjhbh=2507071431551391788832445169664,kcbh=<null>,clzt=<null>,startTime=<null>,endTime=<null>,pageNum=1,pageSize=10]"}
{"@timestamp":"2025-07-28T01:54:49.489Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"e16a279223e6fe57","span":"e16a279223e6fe57","parent":"","exportable":"false","pid":"15324","thread":"http-nio-8888-exec-9","class":"c.x.i.m.K.selectJjhjList_COUNT","rest":"==>  Preparing: SELECT count(0) FROM ks_kw_message msg LEFT JOIN ks_kw_message reply ON msg.ksjhbh = reply.ksjhbh AND msg.id = reply.replay_id AND reply.sczt = '0' WHERE msg.sczt = '0' AND msg.msg_type = '2000' AND msg.origin_app_type = 'HYAPP' AND msg.operate = '0' AND msg.ksjhbh = ? "}
{"@timestamp":"2025-07-28T01:54:49.489Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"e16a279223e6fe57","span":"e16a279223e6fe57","parent":"","exportable":"false","pid":"15324","thread":"http-nio-8888-exec-9","class":"c.x.i.m.K.selectJjhjList_COUNT","rest":"==> Parameters: 2507071431551391788832445169664(String)"}
{"@timestamp":"2025-07-28T01:54:49.497Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"e16a279223e6fe57","span":"e16a279223e6fe57","parent":"","exportable":"false","pid":"15324","thread":"http-nio-8888-exec-9","class":"c.x.i.m.K.selectJjhjList_COUNT","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-28T01:54:49.498Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"e16a279223e6fe57","span":"e16a279223e6fe57","parent":"","exportable":"false","pid":"15324","thread":"http-nio-8888-exec-9","class":"c.x.i.m.KsKwMessageMapper.selectJjhjList","rest":"==>  Preparing: SELECT msg.id, DATE_FORMAT(msg.send_time, '%Y-%m-%d %H:%i:%s') as fbsj, msg.origin_kcbh as kcbh, msg.comment as jjhjnr, reply.comment as hfnr, DATE_FORMAT(reply.send_time, '%Y-%m-%d %H:%i:%s') as hfsj, CASE WHEN reply.id IS NULL THEN '0' ELSE '1' END as clzt FROM ks_kw_message msg LEFT JOIN ks_kw_message reply ON msg.ksjhbh = reply.ksjhbh AND msg.id = reply.replay_id AND reply.sczt = '0' WHERE msg.sczt = '0' AND msg.msg_type = '2000' AND msg.origin_app_type = 'HYAPP' AND msg.operate = '0' AND msg.ksjhbh = ? ORDER BY msg.send_time DESC LIMIT ? "}
{"@timestamp":"2025-07-28T01:54:49.498Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"e16a279223e6fe57","span":"e16a279223e6fe57","parent":"","exportable":"false","pid":"15324","thread":"http-nio-8888-exec-9","class":"c.x.i.m.KsKwMessageMapper.selectJjhjList","rest":"==> Parameters: 2507071431551391788832445169664(String), 10(Integer)"}
{"@timestamp":"2025-07-28T01:54:49.506Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"e16a279223e6fe57","span":"e16a279223e6fe57","parent":"","exportable":"false","pid":"15324","thread":"http-nio-8888-exec-9","class":"c.x.i.m.KsKwMessageMapper.selectJjhjList","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-28T01:54:49.506Z","severity":"INFO","service":"eeip-standalone-service","trace":"e16a279223e6fe57","span":"e16a279223e6fe57","parent":"","exportable":"false","pid":"15324","thread":"http-nio-8888-exec-9","class":"c.x.i.s.impl.KsKwMessageServiceImpl","rest":"紧急呼叫查询结束，共查询到1条记录"}
{"@timestamp":"2025-07-28T01:54:49.531Z","severity":"INFO","service":"eeip-standalone-service","trace":"e16a279223e6fe57","span":"e16a279223e6fe57","parent":"","exportable":"false","pid":"15324","thread":"http-nio-8888-exec-9","class":"c.x.i.facade.manager.CxtjController","rest":"处理请求结束：[紧急呼叫查询][/manager/identityverify/cxtj/jjhjcx]reqModel:JjhjCxReq[ksjhbh=2507071431551391788832445169664,kcbh=<null>,clzt=<null>,startTime=<null>,endTime=<null>,pageNum=1,pageSize=10,msgId=<null>,transChannel=<null>,channelSerialNo=<null>,channelDate=<null>,channelTime=<null>,token=<null>,loginToken=<null>,transCode=<null>,clientIp=<null>,merchantId=<null>,appPackage=<null>],respModel:JjhjCxResp[data=Page{count=true, pageNum=1, pageSize=10, startRow=0, endRow=10, total=1, pages=1, reasonable=true, pageSizeZero=false}[JjhjCxVO[id=2507071451291391793755551432704,fbsj=2025-07-07 14:51:29,kcbh=003,007,jjhjnr=混合考场情况,hfnr=已收到，正派人处理,hfsj=2025-07-07 14:52:14,clzt=1]],totalRows=1,pageNum=1,pageSize=10,msgId=<null>,transChannel=<null>,channelSerialNo=<null>,transCode=<null>,respDate=<null>,respTime=<null>,ext=<null>]"}
{"@timestamp":"2025-07-28T01:55:00.004Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-8","class":"c.x.i.util.schedule.ScheduleJobExecutor","rest":"任务准备执行，任务ID：24081610292802018092458168076288"}
{"@timestamp":"2025-07-28T01:55:00.005Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-8","class":"c.x.i.m.KsKsjhMapper.selectByExample","rest":"==>  Preparing: SELECT ksjhbh,mc,kssj,jssj,xn,xq,sfmr,kszt,sbjm,sbkqsj,sbgbsj,cjlx,kslx,qysbbmd,wifiqy,cjsj,xgsj,scztw,kzqy,qydsxf,dsxfsj,dsxfzt,jkqdrs,dbms FROM ks_ksjh WHERE ( ( scztw = ? and qydsxf = ? and dsxfzt = ? ) ) "}
{"@timestamp":"2025-07-28T01:55:00.005Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-8","class":"c.x.i.m.KsKsjhMapper.selectByExample","rest":"==> Parameters: 0(String), 1(Integer), 0(Integer)"}
{"@timestamp":"2025-07-28T01:55:00.009Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-8","class":"c.x.i.m.KsKsjhMapper.selectByExample","rest":"<==      Total: 0"}
{"@timestamp":"2025-07-28T01:55:00.009Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-8","class":"c.x.i.s.i.s.KsjhSjdsxfTaskServiceImpl","rest":"当前无下发任务！"}
{"@timestamp":"2025-07-28T01:55:00.009Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-8","class":"c.x.i.util.schedule.ScheduleJobExecutor","rest":"任务执行完毕，任务ID：24081610292802018092458168076288  总共耗时：5毫秒"}
{"@timestamp":"2025-07-28T01:55:00.009Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-8","class":"c.x.i.m.S.insertSelective","rest":"==>  Preparing: INSERT INTO schedule_job_log ( log_id,job_id,job_name,bean_name,params,result,times,create_time ) VALUES( ?,?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-28T01:55:00.009Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-8","class":"c.x.i.m.S.insertSelective","rest":"==> Parameters: 25072809550002268847511995184128(String), 24081610292802018092458168076288(String), ksjhSjdsxf(String), ksjhSjdsxfTaskService(String), (String), 0(Integer), 5(Integer), 2025-07-28 09:55:00.004(Timestamp)"}
{"@timestamp":"2025-07-28T01:56:00.015Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-3","class":"c.x.i.util.schedule.ScheduleJobExecutor","rest":"任务准备执行，任务ID：23110110740401808639620095703040"}
{"@timestamp":"2025-07-28T01:56:00.019Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-3","class":"c.x.i.util.schedule.ScheduleJobExecutor","rest":"任务执行完毕，任务ID：23110110740401808639620095703040  总共耗时：4毫秒"}
{"@timestamp":"2025-07-28T01:56:00.019Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-3","class":"c.x.i.m.S.insertSelective","rest":"==>  Preparing: INSERT INTO schedule_job_log ( log_id,job_id,job_name,bean_name,result,times,create_time ) VALUES( ?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-28T01:56:00.024Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-3","class":"c.x.i.m.S.insertSelective","rest":"==> Parameters: 25072809560002268848015395550208(String), 23110110740401808639620095703040(String), jobOnlineStatus(String), onlineStatusTaskService(String), 0(Integer), 4(Integer), 2025-07-28 09:56:00.014(Timestamp)"}
{"@timestamp":"2025-07-28T01:56:01.469Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"MQTT Rec: identityVerify_test01","class":"o.s.i.m.outbound.MqttPahoMessageHandler","rest":"Lost connection; will attempt reconnect on next request"}
{"@timestamp":"2025-07-28T02:00:00.007Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-1","class":"c.x.i.util.schedule.ScheduleJobExecutor","rest":"任务准备执行，任务ID：23110110440401808639620095703040"}
{"@timestamp":"2025-07-28T02:00:00.007Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-10","class":"c.x.i.util.schedule.ScheduleJobExecutor","rest":"任务准备执行，任务ID：23110110740401808639620095703040"}
{"@timestamp":"2025-07-28T02:00:00.007Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-1","class":"c.x.i.service.impl.SbSbxxServiceImpl","rest":"=======开始查询和更新设备在线状态========="}
{"@timestamp":"2025-07-28T02:00:00.009Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.util.schedule.ScheduleJobExecutor","rest":"任务准备执行，任务ID：23110111540401808639620095703040"}
{"@timestamp":"2025-07-28T02:00:00.009Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-5","class":"c.x.i.util.schedule.ScheduleJobExecutor","rest":"任务准备执行，任务ID：23112811035901828218591246338048"}
{"@timestamp":"2025-07-28T02:00:00.009Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-2","class":"c.x.i.util.schedule.ScheduleJobExecutor","rest":"任务准备执行，任务ID：24081610292802018092458168076288"}
{"@timestamp":"2025-07-28T02:00:00.010Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-6","class":"c.x.i.util.schedule.ScheduleJobExecutor","rest":"任务准备执行，任务ID：2502130940211339531603881230336"}
{"@timestamp":"2025-07-28T02:00:00.010Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-6","class":"c.x.i.service.impl.SbSbxxServiceImpl","rest":"固件版本更新"}
{"@timestamp":"2025-07-28T02:00:00.010Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-6","class":"c.x.i.t.u.h.s.i.UnifyAccessServiceImpl","rest":"url[http://*************:8844/device/firmware/_query]参数[JetlinksDeviceFirewareReqModel(paging=true, parallelPager=true, pageSize=1, pageIndex=0)]"}
{"@timestamp":"2025-07-28T02:00:00.016Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-1","class":"c.x.i.m.S.selectByExample_COUNT","rest":"==>  Preparing: SELECT count(0) FROM sb_sbxx WHERE ((sczt = ? AND sblx IN (?, ?, ?, ?))) "}
{"@timestamp":"2025-07-28T02:00:00.016Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.m.SbSbxxMapper.selectByExample","rest":"==>  Preparing: SELECT sbxxbh,sbmc,sbxh,sbbm,sbpp,sbcs,sblx,sblxmc,jspc,jssj,sbccrq,sbazrq,sbzbq,sbwldz,macdz,ipdz,dkh,czxt,kzdkh,appbb,firmwarebb,xlh,ramdiskbb,sbzt,jhzt,sbmy,sczt,zxzt,sbzpid,sbqm,sbjcjg,zhzxsj,create_time,update_time,sbztw,sfydsb,osbb,battery,ntp_enable,ntp_source,ntp_interval,ext_status FROM sb_sbxx WHERE ( ( sczt = ? ) ) "}
{"@timestamp":"2025-07-28T02:00:00.016Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-5","class":"c.x.i.m.J.selectOneByExample","rest":"==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) "}
{"@timestamp":"2025-07-28T02:00:00.016Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-5","class":"c.x.i.m.J.selectOneByExample","rest":"==> Parameters: HISOME_subDevType(String)"}
{"@timestamp":"2025-07-28T02:00:00.017Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.m.SbSbxxMapper.selectByExample","rest":"==> Parameters: 0(String)"}
{"@timestamp":"2025-07-28T02:00:00.017Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-1","class":"c.x.i.m.S.selectByExample_COUNT","rest":"==> Parameters: 0(String), 170(String), 171(String), 172(String), 175(String)"}
{"@timestamp":"2025-07-28T02:00:00.023Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-5","class":"c.x.i.m.J.selectOneByExample","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-28T02:00:00.024Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-2","class":"c.x.i.m.KsKsjhMapper.selectByExample","rest":"==>  Preparing: SELECT ksjhbh,mc,kssj,jssj,xn,xq,sfmr,kszt,sbjm,sbkqsj,sbgbsj,cjlx,kslx,qysbbmd,wifiqy,cjsj,xgsj,scztw,kzqy,qydsxf,dsxfsj,dsxfzt,jkqdrs,dbms FROM ks_ksjh WHERE ( ( scztw = ? and qydsxf = ? and dsxfzt = ? ) ) "}
{"@timestamp":"2025-07-28T02:00:00.024Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-1","class":"c.x.i.m.S.selectByExample_COUNT","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-28T02:00:00.024Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-2","class":"c.x.i.m.KsKsjhMapper.selectByExample","rest":"==> Parameters: 0(String), 1(Integer), 0(Integer)"}
{"@timestamp":"2025-07-28T02:00:00.024Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-1","class":"c.x.i.t.u.h.s.i.UnifyAccessServiceImpl","rest":"url[http://*************:8844/remote/access/device/listByDeviceNames]参数[DeviceStatusReqModel(deviceNames=[])]"}
{"@timestamp":"2025-07-28T02:00:00.025Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.m.SbSbxxMapper.selectByExample","rest":"<==      Total: 16"}
{"@timestamp":"2025-07-28T02:00:00.027Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.t.u.h.s.i.UnifyAccessServiceImpl","rest":"url http://*************:8844/device/category?terms[0].column=parentId&terms[0].value=-5-"}
{"@timestamp":"2025-07-28T02:00:00.027Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-5","class":"c.x.i.m.S.selectBySubStatusAndType","rest":"==>  Preparing: SELECT xlh FROM sb_sbxx WHERE sczt = '0' AND sbztw = '0' AND sblx IN ( ? , ? ) "}
{"@timestamp":"2025-07-28T02:00:00.028Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-5","class":"c.x.i.m.S.selectBySubStatusAndType","rest":"==> Parameters: kcdzbp(String), ydzd(String)"}
{"@timestamp":"2025-07-28T02:00:00.031Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-2","class":"c.x.i.m.KsKsjhMapper.selectByExample","rest":"<==      Total: 0"}
{"@timestamp":"2025-07-28T02:00:00.031Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-2","class":"c.x.i.s.i.s.KsjhSjdsxfTaskServiceImpl","rest":"当前无下发任务！"}
{"@timestamp":"2025-07-28T02:00:00.031Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-10","class":"c.x.i.util.schedule.ScheduleJobExecutor","rest":"任务执行完毕，任务ID：23110110740401808639620095703040  总共耗时：24毫秒"}
{"@timestamp":"2025-07-28T02:00:00.031Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-2","class":"c.x.i.util.schedule.ScheduleJobExecutor","rest":"任务执行完毕，任务ID：24081610292802018092458168076288  总共耗时：22毫秒"}
{"@timestamp":"2025-07-28T02:00:00.032Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-2","class":"c.x.i.m.S.insertSelective","rest":"==>  Preparing: INSERT INTO schedule_job_log ( log_id,job_id,job_name,bean_name,params,result,times,create_time ) VALUES( ?,?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-28T02:00:00.032Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-10","class":"c.x.i.m.S.insertSelective","rest":"==>  Preparing: INSERT INTO schedule_job_log ( log_id,job_id,job_name,bean_name,result,times,create_time ) VALUES( ?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-28T02:00:00.033Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-2","class":"c.x.i.m.S.insertSelective","rest":"==> Parameters: 25072810000002268850028619527170(String), 24081610292802018092458168076288(String), ksjhSjdsxf(String), ksjhSjdsxfTaskService(String), (String), 0(Integer), 22(Integer), 2025-07-28 10:00:00.009(Timestamp)"}
{"@timestamp":"2025-07-28T02:00:00.033Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-10","class":"c.x.i.m.S.insertSelective","rest":"==> Parameters: 25072810000002268850028602749953(String), 23110110740401808639620095703040(String), jobOnlineStatus(String), onlineStatusTaskService(String), 0(Integer), 24(Integer), 2025-07-28 10:00:00.007(Timestamp)"}
{"@timestamp":"2025-07-28T02:00:00.034Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-5","class":"c.x.i.m.S.selectBySubStatusAndType","rest":"<==      Total: 3"}
{"@timestamp":"2025-07-28T02:00:00.034Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-5","class":"c.x.i.s.i.s.SbxxReportTaskServiceImpl","rest":"设备信息上报，上报类型：[kcdzbp, ydzd]，上报设备序列号：[testYdzdXlh001, testYdzdXlh002, testYdzd004]"}
{"@timestamp":"2025-07-28T02:00:00.036Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-5","class":"c.x.i.m.S.getDevicePlaceRelation","rest":"==>  Preparing: SELECT sb_sbxx.xlh AS sbxlh, sb_sbxx.sbmc AS sbmc, sb_sbxx.sblx AS sblb, sb_sbxx.sbzbq AS sbzbjzsj, sb_sbxx.jssj AS cgsj, sb_sbxx.sbcs AS sbcsm, sb_sbxx.sbpp AS sbpp, sb_sbxx.sbzt AS ywzt, sb_sbxx.ipdz AS sbip, sb_sbxx.sfydsb AS sfydsb, csgx.csbh AS csdm, sb_sbxx.sbmy AS sbmy FROM sb_sbxx INNER JOIN sb_sbcsgx csgx ON sb_sbxx.sbxxbh = csgx.sbbh WHERE sb_sbxx.sczt = '0' AND sb_sbxx.sbzt != '3' AND sb_sbxx.xlh IN ( ? , ? , ? ) "}
{"@timestamp":"2025-07-28T02:00:00.036Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-5","class":"c.x.i.m.S.getDevicePlaceRelation","rest":"==> Parameters: testYdzdXlh001(String), testYdzdXlh002(String), testYdzd004(String)"}
{"@timestamp":"2025-07-28T02:00:00.043Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-5","class":"c.x.i.m.S.getDevicePlaceRelation","rest":"<==      Total: 0"}
{"@timestamp":"2025-07-28T02:00:00.044Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-5","class":"c.x.i.m.CsXxjbxxMapper.selectByExample","rest":"==>  Preparing: SELECT xxdm,xxmc,zzjgm,xxdz,xzqhm FROM cs_xxjbxx "}
{"@timestamp":"2025-07-28T02:00:00.044Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-5","class":"c.x.i.m.CsXxjbxxMapper.selectByExample","rest":"==> Parameters: "}
{"@timestamp":"2025-07-28T02:00:00.051Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-5","class":"c.x.i.m.CsXxjbxxMapper.selectByExample","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-28T02:00:00.051Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-5","class":"c.x.i.service.impl.SbSbxxServiceImpl","rest":"没有需要上报的设备"}
{"@timestamp":"2025-07-28T02:00:00.051Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-5","class":"c.x.i.util.schedule.ScheduleJobExecutor","rest":"任务执行完毕，任务ID：23112811035901828218591246338048  总共耗时：42毫秒"}
{"@timestamp":"2025-07-28T02:00:00.053Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-5","class":"c.x.i.m.S.insertSelective","rest":"==>  Preparing: INSERT INTO schedule_job_log ( log_id,job_id,job_name,bean_name,params,result,times,create_time ) VALUES( ?,?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-28T02:00:00.053Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-6","class":"c.x.i.t.u.h.s.i.UnifyAccessServiceImpl","rest":"发送给接入返回结果result=JetlinksWrapper(message=success, status=200, code=200, timestamp=1753667998900, result=JetlinksPageRespModel(pageIndex=0, pageSize=1, total=22, data=[JetlinksDeviceFirewareRespModel(id=00024700113e, deviceName=办公室超脑, productId=HISOME-DX000, version=2025.05.20.1807)]))"}
{"@timestamp":"2025-07-28T02:00:00.053Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-5","class":"c.x.i.m.S.insertSelective","rest":"==> Parameters: 25072810000002268850028619527169(String), 23112811035901828218591246338048(String), 设备信息上报(String), sbxxReportTaskService(String), (String), 0(Integer), 42(Integer), 2025-07-28 10:00:00.009(Timestamp)"}
{"@timestamp":"2025-07-28T02:00:00.053Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-6","class":"c.x.i.t.u.h.s.i.UnifyAccessServiceImpl","rest":"url[http://*************:8844/device/firmware/_query]参数[JetlinksDeviceFirewareReqModel(paging=true, parallelPager=true, pageSize=1000, pageIndex=0)]"}
{"@timestamp":"2025-07-28T02:00:00.086Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-1","class":"c.x.i.t.u.h.s.i.UnifyAccessServiceImpl","rest":"发送给接入返回结果result=Wrapper(code=200, message=success, result={deviceList=[{deviceName=00024700113e, status=0}, {deviceName=00024720d16a, status=0}, {deviceName=1231241157, status=0}, {deviceName=AB1602853, status=1}, {deviceName=ATN240303000100YT55YBF, status=0}, {deviceName=CENCGW100_S01MA00BBA000D1000E, status=0}, {deviceName=CENCGW100_SIVb770d1a6a1ec5b44, status=1}, {deviceName=cillum, status=0}, {deviceName=ddcdb3ac5810283f, status=0}, {deviceName=DENCGW829_C04MB10BDJ027590, status=1}, {deviceName=DENCGW829_C04MB10BDJ07548e, status=0}, {deviceName=FC1836988, status=1}, {deviceName=FC1836989, status=1}, {deviceName=G04754406, status=1}, {deviceName=G16733827, status=0}, {deviceName=G16733836, status=1}, {deviceName=ipsumadk, status=0}, {deviceName=KEP20240707001, status=1}, {deviceName=PID388E2207007544, status=0}, {deviceName=S30SZA2023140181, status=0}, {deviceName=test0012, status=0}, {deviceName=test01-xlh, status=0}, {deviceName=test091903, status=0}, {deviceName=testYdzd004, status=0}, {deviceName=testYdzdXlh001, status=0}, {deviceName=testYdzdXlh002, status=0}], totalRows=26})"}
{"@timestamp":"2025-07-28T02:00:00.088Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-1","class":"c.x.i.m.S.updateByExampleSelective","rest":"==>  Preparing: UPDATE sb_sbxx SET zxzt = ?,zhzxsj = ?,ext_status = ? WHERE ( ( xlh in ( ? , ? , ? , ? , ? , ? , ? , ? ) ) ) "}
{"@timestamp":"2025-07-28T02:00:00.088Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-1","class":"c.x.i.m.S.updateByExampleSelective","rest":"==> Parameters: 1(String), 2025-07-28 10:00:00.087(Timestamp), 0(Integer), AB1602853(String), CENCGW100_SIVb770d1a6a1ec5b44(String), DENCGW829_C04MB10BDJ027590(String), FC1836988(String), FC1836989(String), G04754406(String), G16733836(String), KEP20240707001(String)"}
{"@timestamp":"2025-07-28T02:00:00.099Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.t.u.h.s.i.UnifyAccessServiceImpl","rest":"发送给接入返回结果result=JetlinksWrapper(message=success, status=200, code=200, timestamp=1753667998939, result=[DeviceCategoryRespModel(id=AIEdgeServer, parentId=-5-, key=AIEdgeServer, name=边缘计算服务器, productId=HISOME-DX000, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1747805356879, productName=汉尚多模态超脑), DeviceCategoryRespModel(id=cheating_prevention, parentId=-5-, key=CheatingPrevention, name=作弊防控设备, productId=HISOME-RRS3000, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1655261812143, productName=3536侦测服务器), DeviceCategoryRespModel(id=digital_hd_matrix, parentId=-5-, key=digital_hd_matrix, name=数字高清矩阵, productId=null, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1692077761801, productName=null), DeviceCategoryRespModel(id=ejjksb, parentId=-5-, key=ejjksb, name=耳机监考设备, productId=CENC_HEADPHONE, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1739411557430, productName=头戴式耳机), DeviceCategoryRespModel(id=hdd_box, parentId=-5-, key=hdd_box, name=视频存储盒子, productId=HISOME-HDD4000, sortIndex=1, level=2, creatorId=1199596756811550720, createTime=1680154724527, productName=视频数据存储终端), DeviceCategoryRespModel(id=hskpjmwxpb, parentId=-5-, key=hskpjmwxpb, name=汉尚考培加密无线平板, productId=null, sortIndex=1, level=1, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1740365256141, productName=null), DeviceCategoryRespModel(id=ipc, parentId=-5-, key=ipc, name=网络摄像机, productId=HISOME-IPC, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1691550353555, productName=IPC通用摄像机), DeviceCategoryRespModel(id=kcdzbp, parentId=-5-, key=kcdzbp, name=考场电子班牌, productId=PROD-DZBP, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1697418220328, productName=电子班牌), DeviceCategoryRespModel(id=kcwg, parentId=-5-, key=kcwg, name=考场网关, productId=CENCGW100_C, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1661844529888, productName=智能教室网关), DeviceCategoryRespModel(id=kdwg, parentId=-5-, key=kdwg, name=考点网关, productId=CENCGW100_S, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1661844519215, productName=智能校级网关), DeviceCategoryRespModel(id=lhsjkydzd, parentId=-5-, key=lhsjkydzd, name=理化生机考移动终端, productId=HISOME-PAD-101, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1740365218610, productName=汉尚理化生机考移动终端), DeviceCategoryRespModel(id=nvr, parentId=-5-, key=nvr, name=数字硬盘录像机, productId=HISOME-NVR4000, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1687656114753, productName=数字硬盘录像机), DeviceCategoryRespModel(id=sip, parentId=-5-, key=sip, name=SIP路由分发服务器, productId=SIP8000, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1689045772782, productName=SIP路由分发服务器), DeviceCategoryRespModel(id=tzsxj, parentId=-5-, key=tzsxj, name=同轴摄像机, productId=null, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1691552091707, productName=null), DeviceCategoryRespModel(id=ydzd, parentId=-5-, key=ydzd, name=移动终端, productId=HISOME_ANDROID, sortIndex=1, level=1, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1661844790208, productName=身份核验移动终端)])"}
{"@timestamp":"2025-07-28T02:00:00.099Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.service.impl.SbSbxxServiceImpl","rest":""}
{"@timestamp":"2025-07-28T02:00:00.100Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-6","class":"c.x.i.t.u.h.s.i.UnifyAccessServiceImpl","rest":"发送给接入返回结果result=JetlinksWrapper(message=success, status=200, code=200, timestamp=1753667998943, result=JetlinksPageRespModel(pageIndex=0, pageSize=1000, total=22, data=[JetlinksDeviceFirewareRespModel(id=00024700113e, deviceName=办公室超脑, productId=HISOME-DX000, version=2025.05.20.1807), JetlinksDeviceFirewareRespModel(id=00024720d16a, deviceName=36路办公室超脑, productId=HISOME-DX000, version=2025.05.24.2002), JetlinksDeviceFirewareRespModel(id=AB1602853, deviceName=考场电子班牌, productId=PROD-DZBP, version=1.5.6), JetlinksDeviceFirewareRespModel(id=ATN240303000100YT55YBF, deviceName=ATN240303000100YT55YBF, productId=HISOME_ANDROID, version=MI.T0.LP4X.4G#2.1.3_test3), JetlinksDeviceFirewareRespModel(id=ddcdb3ac5810283f, deviceName=ddcdb3ac5810283f, productId=HISOME_ANDROID, version=MI.T0.LP4X.4G#2.1.8), JetlinksDeviceFirewareRespModel(id=DENCGW829_C04MB10BDJ027590, deviceName=柴行楼教学楼103_DENCGW829_C04MB10BDJ027590, productId=CENCGW100_C, version=2025.01.27), JetlinksDeviceFirewareRespModel(id=DENCGW829_C04MB10BDJ07548e, deviceName=柴行楼柴行楼zq002_DENCGW829_C04MB10BDJ07548e, productId=CENCGW100_C, version=2025.03.18), JetlinksDeviceFirewareRespModel(id=FC1836988, deviceName=DI思源楼1201考务FC1836988, productId=PROD-DZBP, version=1.3.3), JetlinksDeviceFirewareRespModel(id=FC1836989, deviceName=DI思源楼1203考场FC1836989, productId=PROD-DZBP, version=1.5.6), JetlinksDeviceFirewareRespModel(id=G04754401, deviceName=G04754401, productId=PROD-DZBP, version=1.1), JetlinksDeviceFirewareRespModel(id=G04754402, deviceName=G04754402, productId=PROD-DZBP, version=1.1), JetlinksDeviceFirewareRespModel(id=G04754403, deviceName=G04754403, productId=PROD-DZBP, version=1.1), JetlinksDeviceFirewareRespModel(id=G04754404, deviceName=G04754404, productId=PROD-DZBP, version=1.1), JetlinksDeviceFirewareRespModel(id=G04754405, deviceName=G04754405, productId=PROD-DZBP, version=1.1), JetlinksDeviceFirewareRespModel(id=G04754406, deviceName=致远楼致远楼2105_G04754406, productId=PROD-DZBP, version=1.5.6_c), JetlinksDeviceFirewareRespModel(id=G04754407, deviceName=G04754407, productId=PROD-DZBP, version=1.1), JetlinksDeviceFirewareRespModel(id=G04754408, deviceName=G04754408, productId=PROD-DZBP, version=1.1), JetlinksDeviceFirewareRespModel(id=G04754409, deviceName=G04754409, productId=PROD-DZBP, version=1.1), JetlinksDeviceFirewareRespModel(id=G16733827, deviceName=志勤楼教学楼207_G16733827, productId=PROD-DZBP, version=1.1.5), JetlinksDeviceFirewareRespModel(id=G16733836, deviceName=电子班牌测试, productId=PROD-DZBP, version=1.3.3_c), JetlinksDeviceFirewareRespModel(id=KEP20240707001, deviceName=黄河交通学院_身份核验移动终端2, productId=HISOME_ANDROID, version=20241224-1122#2.1.8), JetlinksDeviceFirewareRespModel(id=PID388E2207007544, deviceName=像素设备, productId=PROD-DZBP, version=1.1.6)]))"}
{"@timestamp":"2025-07-28T02:00:00.101Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-6","class":"c.x.i.m.S.updateByExampleSelective","rest":"==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) "}
{"@timestamp":"2025-07-28T02:00:00.102Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-6","class":"c.x.i.m.S.updateByExampleSelective","rest":"==> Parameters: 2025.05.20.1807(String), 2025-07-28 10:00:00.101(Timestamp), (String), 0(Integer), 0(String), 00024700113e(String)"}
{"@timestamp":"2025-07-28T02:00:00.105Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.t.u.h.s.i.UnifyAccessServiceImpl","rest":"Emqx消息桥接查询订阅 url=http://*************:8844/remote/access/emqx/subscribers/list"}
{"@timestamp":"2025-07-28T02:00:00.106Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-1","class":"c.x.i.m.S.updateByExampleSelective","rest":"<==    Updates: 40"}
{"@timestamp":"2025-07-28T02:00:00.107Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-1","class":"c.x.i.m.S.updateByExampleSelective","rest":"==>  Preparing: UPDATE sb_sbxx SET zxzt = ?,ext_status = ? WHERE ( ( xlh in ( ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? ) ) ) "}
{"@timestamp":"2025-07-28T02:00:00.107Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-1","class":"c.x.i.m.S.updateByExampleSelective","rest":"==> Parameters: 0(String), 0(Integer), 00024700113e(String), 00024720d16a(String), 1231241157(String), ATN240303000100YT55YBF(String), CENCGW100_S01MA00BBA000D1000E(String), cillum(String), ddcdb3ac5810283f(String), DENCGW829_C04MB10BDJ07548e(String), G16733827(String), ipsumadk(String), PID388E2207007544(String), S30SZA2023140181(String), test0012(String), test01-xlh(String), test091903(String), testYdzd004(String), testYdzdXlh001(String), testYdzdXlh002(String)"}
{"@timestamp":"2025-07-28T02:00:00.117Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-6","class":"c.x.i.m.S.updateByExampleSelective","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-28T02:00:00.117Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-6","class":"c.x.i.m.S.updateByExampleSelective","rest":"==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) "}
{"@timestamp":"2025-07-28T02:00:00.118Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-6","class":"c.x.i.m.S.updateByExampleSelective","rest":"==> Parameters: 2025.05.24.2002(String), 2025-07-28 10:00:00.117(Timestamp), (String), 0(Integer), 0(String), 00024720d16a(String)"}
{"@timestamp":"2025-07-28T02:00:00.128Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-1","class":"c.x.i.m.S.updateByExampleSelective","rest":"<==    Updates: 31"}
{"@timestamp":"2025-07-28T02:00:00.128Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-1","class":"c.x.i.service.impl.SbSbxxServiceImpl","rest":"getCssbZxzt - 开始获取场所设备在线状态."}
{"@timestamp":"2025-07-28T02:00:00.129Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-1","class":"c.x.i.m.SbSbxxMapper.getCssbZxzt_COUNT","rest":"==>  Preparing: SELECT count(0) FROM sb_sbxx sb LEFT JOIN sb_sbcsgx gx ON sb.sbxxbh = gx.sbbh WHERE sb.sczt = '0' AND sb.sblx = '171' AND gx.csbh IS NOT NULL AND sb.xlh IS NOT NULL "}
{"@timestamp":"2025-07-28T02:00:00.129Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-1","class":"c.x.i.m.SbSbxxMapper.getCssbZxzt_COUNT","rest":"==> Parameters: "}
{"@timestamp":"2025-07-28T02:00:00.131Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-6","class":"c.x.i.m.S.updateByExampleSelective","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-28T02:00:00.131Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-6","class":"c.x.i.m.S.updateByExampleSelective","rest":"==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) "}
{"@timestamp":"2025-07-28T02:00:00.132Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-6","class":"c.x.i.m.S.updateByExampleSelective","rest":"==> Parameters: 1.5.6(String), 2025-07-28 10:00:00.131(Timestamp), (String), 0(Integer), 0(String), AB1602853(String)"}
{"@timestamp":"2025-07-28T02:00:00.135Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.t.u.h.s.i.UnifyAccessServiceImpl","rest":"Emqx消息桥接查询订阅 result=Wrapper(code=200, message=success, result=EmqxSubscribersListRespModel(subscribers=[/HISOME_ANDROID/testYdzdXlh001/function/invoke, /CENCGW100_C/DENCGW829_C04MB10BDJ027590/function/invoke, /HISOME_ANDROID/testYdzd004/function/invoke, /PROD-DZBP/test0012/function/invoke, /PROD-DZBP/FC1836988/function/invoke, /HISOME_ANDROID/ddcdb3ac5810283f/function/invoke, /PROD-DZBP/G04754406/properties/read, /PROD-DZBP/FC1836989/function/invoke, /HISOME_ANDROID/KEP20240707001/function/invoke, /PROD-DZBP/AB1602853/function/invoke, /HISOME_ANDROID/testYdzdXlh002/function/invoke, /CENCGW100_S/CENCGW100_SIV1c9f717de4acfc99/function/invoke, /CENCGW100_S/CENCGW100_SIVb33ad3ede4acfc99/function/invoke, /PROD-DZBP/AB1602853/properties/read, /HISOME_ANDROID/ATN240303000100YT55YBF/function/invoke, /PROD-DZBP/G16733836/function/invoke, /HISOME_ANDROID/S30SZA2023140181/function/invoke, /CENCGW100_C/DENCGW829_C04MB10BDJ07548e/function/invoke, /PROD-DZBP/G04754406/function/invoke, /CENCGW100_S/CENCGW100_SIVb770d1a6a1ec5b44/function/invoke]))"}
{"@timestamp":"2025-07-28T02:00:00.135Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.util.schedule.ScheduleJobExecutor","rest":"任务执行完毕，任务ID：23110111540401808639620095703040  总共耗时：126毫秒"}
{"@timestamp":"2025-07-28T02:00:00.135Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-1","class":"c.x.i.m.SbSbxxMapper.getCssbZxzt_COUNT","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-28T02:00:00.136Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.m.S.insertSelective","rest":"==>  Preparing: INSERT INTO schedule_job_log ( log_id,job_id,job_name,bean_name,result,times,create_time ) VALUES( ?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-28T02:00:00.136Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-1","class":"c.x.i.m.KsKsccMapper.selectByExample","rest":"==>  Preparing: SELECT bh,ksjhbh,ccm,kmm,ccmc,kmmc,kssj,jssj,yxcdsj,kssbqksj,ksrcsj,zc,kspch,cjsj,xgsj,xn,xq,scztw FROM ks_kscc WHERE ( ( scztw = ? and kssj <= ? and jssj >= ? ) ) "}
{"@timestamp":"2025-07-28T02:00:00.136Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-7","class":"c.x.i.m.S.insertSelective","rest":"==> Parameters: 25072810000002268850028619527168(String), 23110111540401808639620095703040(String), jobSubscribeMqtt(String), subscriberDevInfoToMqttTaskService(String), 0(Integer), 126(Integer), 2025-07-28 10:00:00.009(Timestamp)"}
{"@timestamp":"2025-07-28T02:00:00.136Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-1","class":"c.x.i.m.KsKsccMapper.selectByExample","rest":"==> Parameters: 0(String), 2025-07-28 10:00:00.136(Timestamp), 2025-07-28 10:00:00.136(Timestamp)"}
{"@timestamp":"2025-07-28T02:00:00.144Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-1","class":"c.x.i.m.KsKsccMapper.selectByExample","rest":"<==      Total: 0"}
{"@timestamp":"2025-07-28T02:00:00.145Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-1","class":"c.x.i.service.impl.SbSbxxServiceImpl","rest":"getCssbZxzt - 获取场所设备在线状态. [OK]"}
{"@timestamp":"2025-07-28T02:00:00.145Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-1","class":"c.x.i.service.impl.SbSbxxServiceImpl","rest":"========设备在线状态更新完成========="}
{"@timestamp":"2025-07-28T02:00:00.145Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-1","class":"c.x.i.util.schedule.ScheduleJobExecutor","rest":"任务执行完毕，任务ID：23110110440401808639620095703040  总共耗时：138毫秒"}
{"@timestamp":"2025-07-28T02:00:00.145Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-1","class":"c.x.i.m.S.insertSelective","rest":"==>  Preparing: INSERT INTO schedule_job_log ( log_id,job_id,job_name,bean_name,result,times,create_time ) VALUES( ?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-28T02:00:00.145Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-1","class":"c.x.i.m.S.insertSelective","rest":"==> Parameters: 25072810000002268850028602749952(String), 23110110440401808639620095703040(String), jobSbzxztGx(String), sbzxztCxAndGxTaskService(String), 0(Integer), 138(Integer), 2025-07-28 10:00:00.007(Timestamp)"}
{"@timestamp":"2025-07-28T02:00:00.147Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-6","class":"c.x.i.m.S.updateByExampleSelective","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-28T02:00:00.147Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-6","class":"c.x.i.m.S.updateByExampleSelective","rest":"==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) "}
{"@timestamp":"2025-07-28T02:00:00.148Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-6","class":"c.x.i.m.S.updateByExampleSelective","rest":"==> Parameters: 2.1.3_test3(String), 2025-07-28 10:00:00.147(Timestamp), MI.T0.LP4X.4G(String), 0(Integer), 0(String), ATN240303000100YT55YBF(String)"}
{"@timestamp":"2025-07-28T02:00:00.163Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-6","class":"c.x.i.m.S.updateByExampleSelective","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-28T02:00:00.164Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-6","class":"c.x.i.m.S.updateByExampleSelective","rest":"==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) "}
{"@timestamp":"2025-07-28T02:00:00.164Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-6","class":"c.x.i.m.S.updateByExampleSelective","rest":"==> Parameters: 2.1.8(String), 2025-07-28 10:00:00.163(Timestamp), MI.T0.LP4X.4G(String), 0(Integer), 0(String), ddcdb3ac5810283f(String)"}
{"@timestamp":"2025-07-28T02:00:00.179Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-6","class":"c.x.i.m.S.updateByExampleSelective","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-28T02:00:00.181Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-6","class":"c.x.i.m.S.updateByExampleSelective","rest":"==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) "}
{"@timestamp":"2025-07-28T02:00:00.181Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-6","class":"c.x.i.m.S.updateByExampleSelective","rest":"==> Parameters: 2025.01.27(String), 2025-07-28 10:00:00.179(Timestamp), (String), 0(Integer), 0(String), DENCGW829_C04MB10BDJ027590(String)"}
{"@timestamp":"2025-07-28T02:00:00.195Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-6","class":"c.x.i.m.S.updateByExampleSelective","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-28T02:00:00.195Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-6","class":"c.x.i.m.S.updateByExampleSelective","rest":"==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) "}
{"@timestamp":"2025-07-28T02:00:00.196Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-6","class":"c.x.i.m.S.updateByExampleSelective","rest":"==> Parameters: 2025.03.18(String), 2025-07-28 10:00:00.195(Timestamp), (String), 0(Integer), 0(String), DENCGW829_C04MB10BDJ07548e(String)"}
{"@timestamp":"2025-07-28T02:00:00.210Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-6","class":"c.x.i.m.S.updateByExampleSelective","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-28T02:00:00.211Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-6","class":"c.x.i.m.S.updateByExampleSelective","rest":"==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) "}
{"@timestamp":"2025-07-28T02:00:00.211Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-6","class":"c.x.i.m.S.updateByExampleSelective","rest":"==> Parameters: 1.3.3(String), 2025-07-28 10:00:00.21(Timestamp), (String), 0(Integer), 0(String), FC1836988(String)"}
{"@timestamp":"2025-07-28T02:00:00.226Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-6","class":"c.x.i.m.S.updateByExampleSelective","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-28T02:00:00.227Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-6","class":"c.x.i.m.S.updateByExampleSelective","rest":"==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) "}
{"@timestamp":"2025-07-28T02:00:00.227Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-6","class":"c.x.i.m.S.updateByExampleSelective","rest":"==> Parameters: 1.5.6(String), 2025-07-28 10:00:00.227(Timestamp), (String), 0(Integer), 0(String), FC1836989(String)"}
{"@timestamp":"2025-07-28T02:00:00.242Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-6","class":"c.x.i.m.S.updateByExampleSelective","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-28T02:00:00.243Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-6","class":"c.x.i.m.S.updateByExampleSelective","rest":"==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) "}
{"@timestamp":"2025-07-28T02:00:00.243Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-6","class":"c.x.i.m.S.updateByExampleSelective","rest":"==> Parameters: 1.1(String), 2025-07-28 10:00:00.242(Timestamp), (String), 0(Integer), 0(String), G04754401(String)"}
{"@timestamp":"2025-07-28T02:00:00.256Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-6","class":"c.x.i.m.S.updateByExampleSelective","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-28T02:00:00.257Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-6","class":"c.x.i.m.S.updateByExampleSelective","rest":"==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) "}
{"@timestamp":"2025-07-28T02:00:00.257Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-6","class":"c.x.i.m.S.updateByExampleSelective","rest":"==> Parameters: 1.1(String), 2025-07-28 10:00:00.257(Timestamp), (String), 0(Integer), 0(String), G04754402(String)"}
{"@timestamp":"2025-07-28T02:00:00.270Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-6","class":"c.x.i.m.S.updateByExampleSelective","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-28T02:00:00.271Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-6","class":"c.x.i.m.S.updateByExampleSelective","rest":"==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) "}
{"@timestamp":"2025-07-28T02:00:00.271Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-6","class":"c.x.i.m.S.updateByExampleSelective","rest":"==> Parameters: 1.1(String), 2025-07-28 10:00:00.27(Timestamp), (String), 0(Integer), 0(String), G04754403(String)"}
{"@timestamp":"2025-07-28T02:00:00.283Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-6","class":"c.x.i.m.S.updateByExampleSelective","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-28T02:00:00.285Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-6","class":"c.x.i.m.S.updateByExampleSelective","rest":"==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) "}
{"@timestamp":"2025-07-28T02:00:00.286Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-6","class":"c.x.i.m.S.updateByExampleSelective","rest":"==> Parameters: 1.1(String), 2025-07-28 10:00:00.283(Timestamp), (String), 0(Integer), 0(String), G04754404(String)"}
{"@timestamp":"2025-07-28T02:00:00.299Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-6","class":"c.x.i.m.S.updateByExampleSelective","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-28T02:00:00.300Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-6","class":"c.x.i.m.S.updateByExampleSelective","rest":"==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) "}
{"@timestamp":"2025-07-28T02:00:00.300Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-6","class":"c.x.i.m.S.updateByExampleSelective","rest":"==> Parameters: 1.1(String), 2025-07-28 10:00:00.299(Timestamp), (String), 0(Integer), 0(String), G04754405(String)"}
{"@timestamp":"2025-07-28T02:00:00.312Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-6","class":"c.x.i.m.S.updateByExampleSelective","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-28T02:00:00.313Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-6","class":"c.x.i.m.S.updateByExampleSelective","rest":"==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) "}
{"@timestamp":"2025-07-28T02:00:00.314Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-6","class":"c.x.i.m.S.updateByExampleSelective","rest":"==> Parameters: 1.5.6_c(String), 2025-07-28 10:00:00.313(Timestamp), (String), 0(Integer), 0(String), G04754406(String)"}
{"@timestamp":"2025-07-28T02:00:00.329Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-6","class":"c.x.i.m.S.updateByExampleSelective","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-28T02:00:00.330Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-6","class":"c.x.i.m.S.updateByExampleSelective","rest":"==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) "}
{"@timestamp":"2025-07-28T02:00:00.330Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-6","class":"c.x.i.m.S.updateByExampleSelective","rest":"==> Parameters: 1.1(String), 2025-07-28 10:00:00.329(Timestamp), (String), 0(Integer), 0(String), G04754407(String)"}
{"@timestamp":"2025-07-28T02:00:00.343Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-6","class":"c.x.i.m.S.updateByExampleSelective","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-28T02:00:00.344Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-6","class":"c.x.i.m.S.updateByExampleSelective","rest":"==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) "}
{"@timestamp":"2025-07-28T02:00:00.344Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-6","class":"c.x.i.m.S.updateByExampleSelective","rest":"==> Parameters: 1.1(String), 2025-07-28 10:00:00.343(Timestamp), (String), 0(Integer), 0(String), G04754408(String)"}
{"@timestamp":"2025-07-28T02:00:00.356Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-6","class":"c.x.i.m.S.updateByExampleSelective","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-28T02:00:00.357Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-6","class":"c.x.i.m.S.updateByExampleSelective","rest":"==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) "}
{"@timestamp":"2025-07-28T02:00:00.357Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-6","class":"c.x.i.m.S.updateByExampleSelective","rest":"==> Parameters: 1.1(String), 2025-07-28 10:00:00.356(Timestamp), (String), 0(Integer), 0(String), G04754409(String)"}
{"@timestamp":"2025-07-28T02:00:00.368Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-6","class":"c.x.i.m.S.updateByExampleSelective","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-28T02:00:00.368Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-6","class":"c.x.i.m.S.updateByExampleSelective","rest":"==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) "}
{"@timestamp":"2025-07-28T02:00:00.368Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-6","class":"c.x.i.m.S.updateByExampleSelective","rest":"==> Parameters: 1.1.5(String), 2025-07-28 10:00:00.368(Timestamp), (String), 0(Integer), 0(String), G16733827(String)"}
{"@timestamp":"2025-07-28T02:00:00.381Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-6","class":"c.x.i.m.S.updateByExampleSelective","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-28T02:00:00.382Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-6","class":"c.x.i.m.S.updateByExampleSelective","rest":"==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) "}
{"@timestamp":"2025-07-28T02:00:00.382Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-6","class":"c.x.i.m.S.updateByExampleSelective","rest":"==> Parameters: 1.3.3_c(String), 2025-07-28 10:00:00.381(Timestamp), (String), 0(Integer), 0(String), G16733836(String)"}
{"@timestamp":"2025-07-28T02:00:00.397Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-6","class":"c.x.i.m.S.updateByExampleSelective","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-28T02:00:00.398Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-6","class":"c.x.i.m.S.updateByExampleSelective","rest":"==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) "}
{"@timestamp":"2025-07-28T02:00:00.398Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-6","class":"c.x.i.m.S.updateByExampleSelective","rest":"==> Parameters: 2.1.8(String), 2025-07-28 10:00:00.397(Timestamp), 20241224-1122(String), 0(Integer), 0(String), KEP20240707001(String)"}
{"@timestamp":"2025-07-28T02:00:00.413Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-6","class":"c.x.i.m.S.updateByExampleSelective","rest":"<==    Updates: 1"}
{"@timestamp":"2025-07-28T02:00:00.415Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-6","class":"c.x.i.m.S.updateByExampleSelective","rest":"==>  Preparing: UPDATE sb_sbxx SET firmwarebb = ?,update_time = ?,osbb = ?,ext_status = ? WHERE ( ( sczt = ? and xlh = ? ) ) "}
{"@timestamp":"2025-07-28T02:00:00.415Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-6","class":"c.x.i.m.S.updateByExampleSelective","rest":"==> Parameters: 1.1.6(String), 2025-07-28 10:00:00.414(Timestamp), (String), 0(Integer), 0(String), PID388E2207007544(String)"}
{"@timestamp":"2025-07-28T02:00:00.428Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-6","class":"c.x.i.m.S.updateByExampleSelective","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-28T02:00:00.429Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-6","class":"c.x.i.service.impl.SbSbxxServiceImpl","rest":"固件版本更新10条"}
{"@timestamp":"2025-07-28T02:00:00.429Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-6","class":"c.x.i.service.impl.SbSbxxServiceImpl","rest":"固件版本更新 OK, 共计更新10条"}
{"@timestamp":"2025-07-28T02:00:00.429Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-6","class":"c.x.i.util.schedule.ScheduleJobExecutor","rest":"任务执行完毕，任务ID：2502130940211339531603881230336  总共耗时：419毫秒"}
{"@timestamp":"2025-07-28T02:00:00.430Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-6","class":"c.x.i.m.S.insertSelective","rest":"==>  Preparing: INSERT INTO schedule_job_log ( log_id,job_id,job_name,bean_name,params,result,times,create_time ) VALUES( ?,?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-28T02:00:00.430Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-6","class":"c.x.i.m.S.insertSelective","rest":"==> Parameters: 25072810000002268850028619527171(String), 2502130940211339531603881230336(String), 设备固件版本更新(String), deviceVersionUpdateTaskService(String), (String), 0(Integer), 419(Integer), 2025-07-28 10:00:00.009(Timestamp)"}
{"@timestamp":"2025-07-28T02:00:01.481Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"MQTT Rec: identityVerify_test01","class":"o.s.i.m.outbound.MqttPahoMessageHandler","rest":"Lost connection; will attempt reconnect on next request"}
{"@timestamp":"2025-07-28T02:05:00.006Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-9","class":"c.x.i.util.schedule.ScheduleJobExecutor","rest":"任务准备执行，任务ID：24081610292802018092458168076288"}
{"@timestamp":"2025-07-28T02:05:00.014Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-9","class":"c.x.i.m.KsKsjhMapper.selectByExample","rest":"==>  Preparing: SELECT ksjhbh,mc,kssj,jssj,xn,xq,sfmr,kszt,sbjm,sbkqsj,sbgbsj,cjlx,kslx,qysbbmd,wifiqy,cjsj,xgsj,scztw,kzqy,qydsxf,dsxfsj,dsxfzt,jkqdrs,dbms FROM ks_ksjh WHERE ( ( scztw = ? and qydsxf = ? and dsxfzt = ? ) ) "}
{"@timestamp":"2025-07-28T02:05:00.014Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-9","class":"c.x.i.m.KsKsjhMapper.selectByExample","rest":"==> Parameters: 0(String), 1(Integer), 0(Integer)"}
{"@timestamp":"2025-07-28T02:05:00.022Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-9","class":"c.x.i.m.KsKsjhMapper.selectByExample","rest":"<==      Total: 0"}
{"@timestamp":"2025-07-28T02:05:00.023Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-9","class":"c.x.i.s.i.s.KsjhSjdsxfTaskServiceImpl","rest":"当前无下发任务！"}
{"@timestamp":"2025-07-28T02:05:00.023Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-9","class":"c.x.i.util.schedule.ScheduleJobExecutor","rest":"任务执行完毕，任务ID：24081610292802018092458168076288  总共耗时：17毫秒"}
{"@timestamp":"2025-07-28T02:05:00.023Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-9","class":"c.x.i.m.S.insertSelective","rest":"==>  Preparing: INSERT INTO schedule_job_log ( log_id,job_id,job_name,bean_name,params,result,times,create_time ) VALUES( ?,?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-28T02:05:00.024Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-9","class":"c.x.i.m.S.insertSelective","rest":"==> Parameters: 25072810050002268852545176761344(String), 24081610292802018092458168076288(String), ksjhSjdsxf(String), ksjhSjdsxfTaskService(String), (String), 0(Integer), 17(Integer), 2025-07-28 10:05:00.006(Timestamp)"}
{"@timestamp":"2025-07-28T02:06:12.457Z","severity":"INFO","service":"eeip-standalone-service","trace":"84edec9770691793","span":"84edec9770691793","parent":"","exportable":"false","pid":"15324","thread":"http-nio-8888-exec-1","class":"c.x.i.facade.manager.CxtjController","rest":"收到请求开始：[紧急呼叫查询][/manager/identityverify/cxtj/jjhjcx]reqModel:JjhjCxReq[ksjhbh=<null>,kcbh=<null>,clzt=<null>,startTime=<null>,endTime=<null>,pageNum=1,pageSize=10,msgId=<null>,transChannel=<null>,channelSerialNo=<null>,channelDate=<null>,channelTime=<null>,token=<null>,loginToken=<null>,transCode=<null>,clientIp=<null>,merchantId=<null>,appPackage=<null>]"}
{"@timestamp":"2025-07-28T02:06:12.503Z","severity":"INFO","service":"eeip-standalone-service","trace":"84edec9770691793","span":"84edec9770691793","parent":"","exportable":"false","pid":"15324","thread":"http-nio-8888-exec-1","class":"c.x.i.s.impl.KsKwMessageServiceImpl","rest":"紧急呼叫查询开始，查询条件：JjhjCxDTO[ksjhbh=<null>,kcbh=<null>,clzt=<null>,startTime=<null>,endTime=<null>,pageNum=1,pageSize=10]"}
{"@timestamp":"2025-07-28T02:06:12.506Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"84edec9770691793","span":"84edec9770691793","parent":"","exportable":"false","pid":"15324","thread":"http-nio-8888-exec-1","class":"c.x.i.m.K.selectJjhjList_COUNT","rest":"==>  Preparing: SELECT count(0) FROM ks_kw_message msg LEFT JOIN ks_kw_message reply ON msg.ksjhbh = reply.ksjhbh AND msg.id = reply.replay_id AND reply.sczt = '0' WHERE msg.sczt = '0' AND msg.msg_type = '2000' AND msg.origin_app_type = 'HYAPP' AND msg.operate = '0' "}
{"@timestamp":"2025-07-28T02:06:12.506Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"84edec9770691793","span":"84edec9770691793","parent":"","exportable":"false","pid":"15324","thread":"http-nio-8888-exec-1","class":"c.x.i.m.K.selectJjhjList_COUNT","rest":"==> Parameters: "}
{"@timestamp":"2025-07-28T02:06:12.526Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"84edec9770691793","span":"84edec9770691793","parent":"","exportable":"false","pid":"15324","thread":"http-nio-8888-exec-1","class":"c.x.i.m.K.selectJjhjList_COUNT","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-28T02:06:12.527Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"84edec9770691793","span":"84edec9770691793","parent":"","exportable":"false","pid":"15324","thread":"http-nio-8888-exec-1","class":"c.x.i.m.KsKwMessageMapper.selectJjhjList","rest":"==>  Preparing: SELECT msg.id, DATE_FORMAT(msg.send_time, '%Y-%m-%d %H:%i:%s') as fbsj, msg.origin_kcbh as kcbh, msg.comment as jjhjnr, reply.comment as hfnr, DATE_FORMAT(reply.send_time, '%Y-%m-%d %H:%i:%s') as hfsj, CASE WHEN reply.id IS NULL THEN '0' ELSE '1' END as clzt FROM ks_kw_message msg LEFT JOIN ks_kw_message reply ON msg.ksjhbh = reply.ksjhbh AND msg.id = reply.replay_id AND reply.sczt = '0' WHERE msg.sczt = '0' AND msg.msg_type = '2000' AND msg.origin_app_type = 'HYAPP' AND msg.operate = '0' ORDER BY msg.send_time DESC LIMIT ? "}
{"@timestamp":"2025-07-28T02:06:12.527Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"84edec9770691793","span":"84edec9770691793","parent":"","exportable":"false","pid":"15324","thread":"http-nio-8888-exec-1","class":"c.x.i.m.KsKwMessageMapper.selectJjhjList","rest":"==> Parameters: 10(Integer)"}
{"@timestamp":"2025-07-28T02:06:12.536Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"84edec9770691793","span":"84edec9770691793","parent":"","exportable":"false","pid":"15324","thread":"http-nio-8888-exec-1","class":"c.x.i.m.KsKwMessageMapper.selectJjhjList","rest":"<==      Total: 10"}
{"@timestamp":"2025-07-28T02:06:12.536Z","severity":"INFO","service":"eeip-standalone-service","trace":"84edec9770691793","span":"84edec9770691793","parent":"","exportable":"false","pid":"15324","thread":"http-nio-8888-exec-1","class":"c.x.i.s.impl.KsKwMessageServiceImpl","rest":"紧急呼叫查询结束，共查询到53条记录"}
{"@timestamp":"2025-07-28T02:06:12.561Z","severity":"INFO","service":"eeip-standalone-service","trace":"84edec9770691793","span":"84edec9770691793","parent":"","exportable":"false","pid":"15324","thread":"http-nio-8888-exec-1","class":"c.x.i.facade.manager.CxtjController","rest":"处理请求结束：[紧急呼叫查询][/manager/identityverify/cxtj/jjhjcx]reqModel:JjhjCxReq[ksjhbh=<null>,kcbh=<null>,clzt=<null>,startTime=<null>,endTime=<null>,pageNum=1,pageSize=10,msgId=<null>,transChannel=<null>,channelSerialNo=<null>,channelDate=<null>,channelTime=<null>,token=<null>,loginToken=<null>,transCode=<null>,clientIp=<null>,merchantId=<null>,appPackage=<null>],respModel:JjhjCxResp[data=Page{count=true, pageNum=1, pageSize=10, startRow=0, endRow=10, total=53, pages=6, reasonable=true, pageSizeZero=false}[JjhjCxVO[id=2507071451291391793755551432704,fbsj=2025-07-07 14:51:29,kcbh=003,007,jjhjnr=混合考场情况,hfnr=已收到，正派人处理,hfsj=2025-07-07 14:52:14,clzt=1], JjhjCxVO[id=2507071142321391746204223668224,fbsj=2025-07-07 11:42:32,kcbh=003,007,jjhjnr=电子班牌有误,hfnr=<null>,hfsj=<null>,clzt=0], JjhjCxVO[id=2507062126101391530691837034496,fbsj=2025-07-06 21:26:09,kcbh=003,007,jjhjnr=电子班牌有误,hfnr=已收到，正派人处理,hfsj=2025-07-06 21:27:24,clzt=1], JjhjCxVO[id=2505271725281376974603104026624,fbsj=2025-05-27 17:25:29,kcbh=003,jjhjnr=电子班牌有误,hfnr=收到，设备有问题，可先进行人工核验。保持考场秩序,hfsj=2025-05-27 17:25:43,clzt=1], JjhjCxVO[id=24122709074502114446502591008768,fbsj=2024-12-27 09:07:45,kcbh=001,jjhjnr=电子班牌有误,hfnr=<null>,hfsj=<null>,clzt=0], JjhjCxVO[id=24111914244302087064562724573184,fbsj=2024-11-19 14:24:42,kcbh=003,jjhjnr=混合考场情况,hfnr=<null>,hfsj=<null>,clzt=0], JjhjCxVO[id=24111914244302087064562816978944,fbsj=2024-11-19 14:24:42,kcbh=003,jjhjnr=混合考场情况,hfnr=<null>,hfsj=<null>,clzt=0], JjhjCxVO[id=24111914241302087064306008002560,fbsj=2024-11-19 14:24:12,kcbh=003,jjhjnr=其它,hfnr=<null>,hfsj=<null>,clzt=0], JjhjCxVO[id=24111914241302087064306167517184,fbsj=2024-11-19 14:24:12,kcbh=003,jjhjnr=其它,hfnr=<null>,hfsj=<null>,clzt=0], JjhjCxVO[id=24111914391602087071879906004992,fbsj=2024-11-19 14:10:16,kcbh=003,jjhjnr=其它,hfnr=<null>,hfsj=<null>,clzt=0]],totalRows=53,pageNum=1,pageSize=10,msgId=<null>,transChannel=<null>,channelSerialNo=<null>,transCode=<null>,respDate=<null>,respTime=<null>,ext=<null>]"}
{"@timestamp":"2025-07-28T02:06:47.862Z","severity":"INFO","service":"eeip-standalone-service","trace":"e5d61a7221a07993","span":"eb01aa526e99004f","parent":"764eae2b6402482e","exportable":"false","pid":"15324","thread":"ThreadPoolTaskExecutor-2","class":"c.x.b.u.m.service.impl.MqttMsgRevHandler","rest":"GenericMessage [payload={\"data\":{\"OPERATION\":\"NOTIFY_DEVEVENT_YDZDBATTERY\",\"TIMESTAMP\":1753668406586,\"Data\":{\"devType\":\"172\",\"sbcsbh\":\"K3307820091100001\",\"eventTime\":\"1753668406586\",\"batteryVal\":100,\"eventType\":\"YDZDBATTERY\",\"sn\":\"KEP20240707001\"}},\"sign\":\"\",\"messageId\":\"batteryVal_1753668406586_KEP20240707001\",\"timestamp\":1753668406586,\"token\":\"ee98d86d3ccc48a2a366770e8ffb7567\"}, headers={mqtt_receivedRetained=false, spanTraceId=e5d61a7221a07993, spanId=e5d61a7221a07993, nativeHeaders={spanTraceId=[e5d61a7221a07993], spanId=[e5d61a7221a07993], spanSampled=[0]}, mqtt_duplicate=false, id=df401ffb-384a-f427-ee06-e4d428a376e4, spanSampled=0, mqtt_receivedTopic=/HISOME_ANDROID/KEP20240707001/event/REPORT_DEVEVENT, mqtt_receivedQos=1}]"}
{"@timestamp":"2025-07-28T02:06:47.864Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"ThreadPoolTaskExecutor-2","class":"c.x.a.service.impl.BizMqttMsgRevHandler","rest":"topic[/HISOME_ANDROID/KEP20240707001/event/REPORT_DEVEVENT] 没有方法可以处理这个主题..."}
{"@timestamp":"2025-07-28T02:06:47.864Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"ThreadPoolTaskExecutor-5","class":"c.x.r.s.impl.BasicMqttMsgRevHandler","rest":"topic[/HISOME_ANDROID/KEP20240707001/event/REPORT_DEVEVENT] 没有方法可以处理这个主题..."}
{"@timestamp":"2025-07-28T02:06:47.864Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"ThreadPoolTaskExecutor-4","class":"c.x.i.t.u.m.s.i.IdentityVerifyMqttMsgRevHandler","rest":"judgeDeviceIdExist:KEP20240707001"}
{"@timestamp":"2025-07-28T02:06:47.864Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"ThreadPoolTaskExecutor-4","class":"c.x.i.s.i.m.MqttReportDeveventEvantHandler","rest":"处理mqtt上报设备事件消息:BaseOperationReq(operation=NOTIFY_DEVEVENT_YDZDBATTERY, timestamp=1753668406586, data={\"devType\":\"172\",\"sbcsbh\":\"K3307820091100001\",\"eventTime\":\"1753668406586\",\"batteryVal\":100,\"eventType\":\"YDZDBATTERY\",\"sn\":\"KEP20240707001\"})"}
{"@timestamp":"2025-07-28T02:06:47.926Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"ThreadPoolTaskExecutor-4","class":"c.x.i.m.S.updateZxztAndNtpByXlh","rest":"==>  Preparing: update sb_sbxx SET zxzt = ?, battery = ?, update_time = now() where xlh = ? "}
{"@timestamp":"2025-07-28T02:06:47.926Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"ThreadPoolTaskExecutor-4","class":"c.x.i.m.S.updateZxztAndNtpByXlh","rest":"==> Parameters: 1(String), 100(String), KEP20240707001(String)"}
{"@timestamp":"2025-07-28T02:06:48.052Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"ThreadPoolTaskExecutor-4","class":"c.x.i.m.S.updateZxztAndNtpByXlh","rest":"<==    Updates: 6"}
{"@timestamp":"2025-07-28T02:06:48.052Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"ThreadPoolTaskExecutor-4","class":"c.x.i.m.SbSbxxMapper.selectByExample","rest":"==>  Preparing: SELECT sbxxbh,sbmc,sbxh,sbbm,sbpp,sbcs,sblx,sblxmc,jspc,jssj,sbccrq,sbazrq,sbzbq,sbwldz,macdz,ipdz,dkh,czxt,kzdkh,appbb,firmwarebb,xlh,ramdiskbb,sbzt,jhzt,sbmy,sczt,zxzt,sbzpid,sbqm,sbjcjg,zhzxsj,create_time,update_time,sbztw,sfydsb,osbb,battery,ntp_enable,ntp_source,ntp_interval,ext_status FROM sb_sbxx WHERE ( ( sczt = ? and xlh = ? ) ) "}
{"@timestamp":"2025-07-28T02:06:48.052Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"ThreadPoolTaskExecutor-4","class":"c.x.i.m.SbSbxxMapper.selectByExample","rest":"==> Parameters: 0(String), KEP20240707001(String)"}
{"@timestamp":"2025-07-28T02:06:48.096Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"ThreadPoolTaskExecutor-4","class":"c.x.i.m.SbSbxxMapper.selectByExample","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-28T02:06:48.096Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"ThreadPoolTaskExecutor-4","class":"c.x.i.s.i.m.MqttReportDeveventEvantHandler","rest":"上报电量大于阈值, 不记录日志"}
{"@timestamp":"2025-07-28T02:06:55.761Z","severity":"INFO","service":"eeip-standalone-service","trace":"3a962092feb915b0","span":"3a962092feb915b0","parent":"","exportable":"false","pid":"15324","thread":"http-nio-8888-exec-4","class":"c.x.i.facade.manager.CxtjController","rest":"收到请求开始：[紧急呼叫查询][/manager/identityverify/cxtj/jjhjcx]reqModel:JjhjCxReq[ksjhbh=2507211439231396864138952572928,kcbh=<null>,clzt=<null>,startTime=<null>,endTime=<null>,pageNum=1,pageSize=10,msgId=<null>,transChannel=<null>,channelSerialNo=<null>,channelDate=<null>,channelTime=<null>,token=<null>,loginToken=<null>,transCode=<null>,clientIp=<null>,merchantId=<null>,appPackage=<null>]"}
{"@timestamp":"2025-07-28T02:06:55.780Z","severity":"INFO","service":"eeip-standalone-service","trace":"3a962092feb915b0","span":"3a962092feb915b0","parent":"","exportable":"false","pid":"15324","thread":"http-nio-8888-exec-4","class":"c.x.i.s.impl.KsKwMessageServiceImpl","rest":"紧急呼叫查询开始，查询条件：JjhjCxDTO[ksjhbh=2507211439231396864138952572928,kcbh=<null>,clzt=<null>,startTime=<null>,endTime=<null>,pageNum=1,pageSize=10]"}
{"@timestamp":"2025-07-28T02:06:55.781Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"3a962092feb915b0","span":"3a962092feb915b0","parent":"","exportable":"false","pid":"15324","thread":"http-nio-8888-exec-4","class":"c.x.i.m.K.selectJjhjList_COUNT","rest":"==>  Preparing: SELECT count(0) FROM ks_kw_message msg LEFT JOIN ks_kw_message reply ON msg.ksjhbh = reply.ksjhbh AND msg.id = reply.replay_id AND reply.sczt = '0' WHERE msg.sczt = '0' AND msg.msg_type = '2000' AND msg.origin_app_type = 'HYAPP' AND msg.operate = '0' AND msg.ksjhbh = ? "}
{"@timestamp":"2025-07-28T02:06:55.781Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"3a962092feb915b0","span":"3a962092feb915b0","parent":"","exportable":"false","pid":"15324","thread":"http-nio-8888-exec-4","class":"c.x.i.m.K.selectJjhjList_COUNT","rest":"==> Parameters: 2507211439231396864138952572928(String)"}
{"@timestamp":"2025-07-28T02:06:55.790Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"3a962092feb915b0","span":"3a962092feb915b0","parent":"","exportable":"false","pid":"15324","thread":"http-nio-8888-exec-4","class":"c.x.i.m.K.selectJjhjList_COUNT","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-28T02:06:55.790Z","severity":"INFO","service":"eeip-standalone-service","trace":"3a962092feb915b0","span":"3a962092feb915b0","parent":"","exportable":"false","pid":"15324","thread":"http-nio-8888-exec-4","class":"c.x.i.s.impl.KsKwMessageServiceImpl","rest":"紧急呼叫查询结束，共查询到0条记录"}
{"@timestamp":"2025-07-28T02:06:55.814Z","severity":"INFO","service":"eeip-standalone-service","trace":"3a962092feb915b0","span":"3a962092feb915b0","parent":"","exportable":"false","pid":"15324","thread":"http-nio-8888-exec-4","class":"c.x.i.facade.manager.CxtjController","rest":"处理请求结束：[紧急呼叫查询][/manager/identityverify/cxtj/jjhjcx]reqModel:JjhjCxReq[ksjhbh=2507211439231396864138952572928,kcbh=<null>,clzt=<null>,startTime=<null>,endTime=<null>,pageNum=1,pageSize=10,msgId=<null>,transChannel=<null>,channelSerialNo=<null>,channelDate=<null>,channelTime=<null>,token=<null>,loginToken=<null>,transCode=<null>,clientIp=<null>,merchantId=<null>,appPackage=<null>],respModel:JjhjCxResp[data=Page{count=true, pageNum=1, pageSize=10, startRow=0, endRow=10, total=0, pages=0, reasonable=true, pageSizeZero=false}[],totalRows=0,pageNum=1,pageSize=10,msgId=<null>,transChannel=<null>,channelSerialNo=<null>,transCode=<null>,respDate=<null>,respTime=<null>,ext=<null>]"}
{"@timestamp":"2025-07-28T02:07:00.010Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-4","class":"c.x.i.util.schedule.ScheduleJobExecutor","rest":"任务准备执行，任务ID：23110110740401808639620095703040"}
{"@timestamp":"2025-07-28T02:07:00.017Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-4","class":"c.x.i.util.schedule.ScheduleJobExecutor","rest":"任务执行完毕，任务ID：23110110740401808639620095703040  总共耗时：7毫秒"}
{"@timestamp":"2025-07-28T02:07:00.019Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-4","class":"c.x.i.m.S.insertSelective","rest":"==>  Preparing: INSERT INTO schedule_job_log ( log_id,job_id,job_name,bean_name,result,times,create_time ) VALUES( ?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-28T02:07:00.019Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-4","class":"c.x.i.m.S.insertSelective","rest":"==> Parameters: 25072810070002268853551843275776(String), 23110110740401808639620095703040(String), jobOnlineStatus(String), onlineStatusTaskService(String), 0(Integer), 7(Integer), 2025-07-28 10:07:00.01(Timestamp)"}
{"@timestamp":"2025-07-28T02:07:01.476Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"MQTT Rec: identityVerify_test01","class":"o.s.i.m.outbound.MqttPahoMessageHandler","rest":"Lost connection; will attempt reconnect on next request"}
{"@timestamp":"2025-07-28T02:10:00.003Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-8","class":"c.x.i.util.schedule.ScheduleJobExecutor","rest":"任务准备执行，任务ID：23110111540401808639620095703040"}
{"@timestamp":"2025-07-28T02:10:00.003Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-3","class":"c.x.i.util.schedule.ScheduleJobExecutor","rest":"任务准备执行，任务ID：24081610292802018092458168076288"}
{"@timestamp":"2025-07-28T02:10:00.011Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-8","class":"c.x.i.m.SbSbxxMapper.selectByExample","rest":"==>  Preparing: SELECT sbxxbh,sbmc,sbxh,sbbm,sbpp,sbcs,sblx,sblxmc,jspc,jssj,sbccrq,sbazrq,sbzbq,sbwldz,macdz,ipdz,dkh,czxt,kzdkh,appbb,firmwarebb,xlh,ramdiskbb,sbzt,jhzt,sbmy,sczt,zxzt,sbzpid,sbqm,sbjcjg,zhzxsj,create_time,update_time,sbztw,sfydsb,osbb,battery,ntp_enable,ntp_source,ntp_interval,ext_status FROM sb_sbxx WHERE ( ( sczt = ? ) ) "}
{"@timestamp":"2025-07-28T02:10:00.011Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-8","class":"c.x.i.m.SbSbxxMapper.selectByExample","rest":"==> Parameters: 0(String)"}
{"@timestamp":"2025-07-28T02:10:00.011Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-3","class":"c.x.i.m.KsKsjhMapper.selectByExample","rest":"==>  Preparing: SELECT ksjhbh,mc,kssj,jssj,xn,xq,sfmr,kszt,sbjm,sbkqsj,sbgbsj,cjlx,kslx,qysbbmd,wifiqy,cjsj,xgsj,scztw,kzqy,qydsxf,dsxfsj,dsxfzt,jkqdrs,dbms FROM ks_ksjh WHERE ( ( scztw = ? and qydsxf = ? and dsxfzt = ? ) ) "}
{"@timestamp":"2025-07-28T02:10:00.011Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-3","class":"c.x.i.m.KsKsjhMapper.selectByExample","rest":"==> Parameters: 0(String), 1(Integer), 0(Integer)"}
{"@timestamp":"2025-07-28T02:10:00.021Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-3","class":"c.x.i.m.KsKsjhMapper.selectByExample","rest":"<==      Total: 0"}
{"@timestamp":"2025-07-28T02:10:00.021Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-3","class":"c.x.i.s.i.s.KsjhSjdsxfTaskServiceImpl","rest":"当前无下发任务！"}
{"@timestamp":"2025-07-28T02:10:00.021Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-3","class":"c.x.i.util.schedule.ScheduleJobExecutor","rest":"任务执行完毕，任务ID：24081610292802018092458168076288  总共耗时：18毫秒"}
{"@timestamp":"2025-07-28T02:10:00.022Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-3","class":"c.x.i.m.S.insertSelective","rest":"==>  Preparing: INSERT INTO schedule_job_log ( log_id,job_id,job_name,bean_name,params,result,times,create_time ) VALUES( ?,?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-28T02:10:00.022Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-3","class":"c.x.i.m.S.insertSelective","rest":"==> Parameters: 25072810100002268855061733995521(String), 24081610292802018092458168076288(String), ksjhSjdsxf(String), ksjhSjdsxfTaskService(String), (String), 0(Integer), 18(Integer), 2025-07-28 10:10:00.003(Timestamp)"}
{"@timestamp":"2025-07-28T02:10:00.022Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-8","class":"c.x.i.m.SbSbxxMapper.selectByExample","rest":"<==      Total: 16"}
{"@timestamp":"2025-07-28T02:10:00.022Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-8","class":"c.x.i.t.u.h.s.i.UnifyAccessServiceImpl","rest":"url http://*************:8844/device/category?terms[0].column=parentId&terms[0].value=-5-"}
{"@timestamp":"2025-07-28T02:10:00.113Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-8","class":"c.x.i.t.u.h.s.i.UnifyAccessServiceImpl","rest":"发送给接入返回结果result=JetlinksWrapper(message=success, status=200, code=200, timestamp=1753668598946, result=[DeviceCategoryRespModel(id=AIEdgeServer, parentId=-5-, key=AIEdgeServer, name=边缘计算服务器, productId=HISOME-DX000, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1747805356879, productName=汉尚多模态超脑), DeviceCategoryRespModel(id=cheating_prevention, parentId=-5-, key=CheatingPrevention, name=作弊防控设备, productId=HISOME-RRS3000, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1655261812143, productName=3536侦测服务器), DeviceCategoryRespModel(id=digital_hd_matrix, parentId=-5-, key=digital_hd_matrix, name=数字高清矩阵, productId=null, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1692077761801, productName=null), DeviceCategoryRespModel(id=ejjksb, parentId=-5-, key=ejjksb, name=耳机监考设备, productId=CENC_HEADPHONE, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1739411557430, productName=头戴式耳机), DeviceCategoryRespModel(id=hdd_box, parentId=-5-, key=hdd_box, name=视频存储盒子, productId=HISOME-HDD4000, sortIndex=1, level=2, creatorId=1199596756811550720, createTime=1680154724527, productName=视频数据存储终端), DeviceCategoryRespModel(id=hskpjmwxpb, parentId=-5-, key=hskpjmwxpb, name=汉尚考培加密无线平板, productId=null, sortIndex=1, level=1, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1740365256141, productName=null), DeviceCategoryRespModel(id=ipc, parentId=-5-, key=ipc, name=网络摄像机, productId=HISOME-IPC, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1691550353555, productName=IPC通用摄像机), DeviceCategoryRespModel(id=kcdzbp, parentId=-5-, key=kcdzbp, name=考场电子班牌, productId=PROD-DZBP, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1697418220328, productName=电子班牌), DeviceCategoryRespModel(id=kcwg, parentId=-5-, key=kcwg, name=考场网关, productId=CENCGW100_C, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1661844529888, productName=智能教室网关), DeviceCategoryRespModel(id=kdwg, parentId=-5-, key=kdwg, name=考点网关, productId=CENCGW100_S, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1661844519215, productName=智能校级网关), DeviceCategoryRespModel(id=lhsjkydzd, parentId=-5-, key=lhsjkydzd, name=理化生机考移动终端, productId=HISOME-PAD-101, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1740365218610, productName=汉尚理化生机考移动终端), DeviceCategoryRespModel(id=nvr, parentId=-5-, key=nvr, name=数字硬盘录像机, productId=HISOME-NVR4000, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1687656114753, productName=数字硬盘录像机), DeviceCategoryRespModel(id=sip, parentId=-5-, key=sip, name=SIP路由分发服务器, productId=SIP8000, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1689045772782, productName=SIP路由分发服务器), DeviceCategoryRespModel(id=tzsxj, parentId=-5-, key=tzsxj, name=同轴摄像机, productId=null, sortIndex=1, level=2, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1691552091707, productName=null), DeviceCategoryRespModel(id=ydzd, parentId=-5-, key=ydzd, name=移动终端, productId=HISOME_ANDROID, sortIndex=1, level=1, creatorId=098f6bcd4621d373cade4e832627b4f6, createTime=1661844790208, productName=身份核验移动终端)])"}
{"@timestamp":"2025-07-28T02:10:00.113Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-8","class":"c.x.i.service.impl.SbSbxxServiceImpl","rest":""}
{"@timestamp":"2025-07-28T02:10:00.120Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-8","class":"c.x.i.t.u.h.s.i.UnifyAccessServiceImpl","rest":"Emqx消息桥接查询订阅 url=http://*************:8844/remote/access/emqx/subscribers/list"}
{"@timestamp":"2025-07-28T02:10:00.145Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-8","class":"c.x.i.t.u.h.s.i.UnifyAccessServiceImpl","rest":"Emqx消息桥接查询订阅 result=Wrapper(code=200, message=success, result=EmqxSubscribersListRespModel(subscribers=[/HISOME_ANDROID/testYdzdXlh001/function/invoke, /CENCGW100_C/DENCGW829_C04MB10BDJ027590/function/invoke, /HISOME_ANDROID/testYdzd004/function/invoke, /PROD-DZBP/test0012/function/invoke, /PROD-DZBP/FC1836988/function/invoke, /HISOME_ANDROID/ddcdb3ac5810283f/function/invoke, /PROD-DZBP/G04754406/properties/read, /PROD-DZBP/FC1836989/function/invoke, /HISOME_ANDROID/KEP20240707001/function/invoke, /PROD-DZBP/AB1602853/function/invoke, /HISOME_ANDROID/testYdzdXlh002/function/invoke, /CENCGW100_S/CENCGW100_SIV1c9f717de4acfc99/function/invoke, /CENCGW100_S/CENCGW100_SIVb33ad3ede4acfc99/function/invoke, /PROD-DZBP/AB1602853/properties/read, /HISOME_ANDROID/ATN240303000100YT55YBF/function/invoke, /PROD-DZBP/G16733836/function/invoke, /HISOME_ANDROID/S30SZA2023140181/function/invoke, /CENCGW100_C/DENCGW829_C04MB10BDJ07548e/function/invoke, /PROD-DZBP/G04754406/function/invoke, /CENCGW100_S/CENCGW100_SIVb770d1a6a1ec5b44/function/invoke]))"}
{"@timestamp":"2025-07-28T02:10:00.145Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-8","class":"c.x.i.util.schedule.ScheduleJobExecutor","rest":"任务执行完毕，任务ID：23110111540401808639620095703040  总共耗时：142毫秒"}
{"@timestamp":"2025-07-28T02:10:00.146Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-8","class":"c.x.i.m.S.insertSelective","rest":"==>  Preparing: INSERT INTO schedule_job_log ( log_id,job_id,job_name,bean_name,result,times,create_time ) VALUES( ?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-28T02:10:00.146Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-8","class":"c.x.i.m.S.insertSelective","rest":"==> Parameters: 25072810100002268855061733995520(String), 23110111540401808639620095703040(String), jobSubscribeMqtt(String), subscriberDevInfoToMqttTaskService(String), 0(Integer), 142(Integer), 2025-07-28 10:10:00.003(Timestamp)"}
{"@timestamp":"2025-07-28T02:14:00.003Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-10","class":"c.x.i.util.schedule.ScheduleJobExecutor","rest":"任务准备执行，任务ID：23110110740401808639620095703040"}
{"@timestamp":"2025-07-28T02:14:00.010Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-10","class":"c.x.i.util.schedule.ScheduleJobExecutor","rest":"任务执行完毕，任务ID：23110110740401808639620095703040  总共耗时：7毫秒"}
{"@timestamp":"2025-07-28T02:14:00.017Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-10","class":"c.x.i.m.S.insertSelective","rest":"==>  Preparing: INSERT INTO schedule_job_log ( log_id,job_id,job_name,bean_name,result,times,create_time ) VALUES( ?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-28T02:14:00.017Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-10","class":"c.x.i.m.S.insertSelective","rest":"==> Parameters: 25072810140002268857074999915520(String), 23110110740401808639620095703040(String), jobOnlineStatus(String), onlineStatusTaskService(String), 0(Integer), 7(Integer), 2025-07-28 10:14:00.003(Timestamp)"}
{"@timestamp":"2025-07-28T02:14:01.482Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"MQTT Rec: identityVerify_test01","class":"o.s.i.m.outbound.MqttPahoMessageHandler","rest":"Lost connection; will attempt reconnect on next request"}
{"@timestamp":"2025-07-28T02:14:50.891Z","severity":"INFO","service":"eeip-standalone-service","trace":"9582f7195e37a62e","span":"9582f7195e37a62e","parent":"","exportable":"false","pid":"15324","thread":"http-nio-8888-exec-2","class":"c.x.i.facade.manager.CxtjController","rest":"收到请求开始：[紧急呼叫查询][/manager/identityverify/cxtj/jjhjcx]reqModel:JjhjCxReq[ksjhbh=2507211439231396864138952572928,kcbh=<null>,clzt=<null>,startTime=<null>,endTime=<null>,pageNum=1,pageSize=10,msgId=<null>,transChannel=<null>,channelSerialNo=<null>,channelDate=<null>,channelTime=<null>,token=<null>,loginToken=<null>,transCode=<null>,clientIp=<null>,merchantId=<null>,appPackage=<null>]"}
{"@timestamp":"2025-07-28T02:14:50.904Z","severity":"INFO","service":"eeip-standalone-service","trace":"9582f7195e37a62e","span":"9582f7195e37a62e","parent":"","exportable":"false","pid":"15324","thread":"http-nio-8888-exec-2","class":"c.x.i.s.impl.KsKwMessageServiceImpl","rest":"紧急呼叫查询开始，查询条件：JjhjCxDTO[ksjhbh=2507211439231396864138952572928,kcbh=<null>,clzt=<null>,startTime=<null>,endTime=<null>,pageNum=1,pageSize=10]"}
{"@timestamp":"2025-07-28T02:14:50.906Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"9582f7195e37a62e","span":"9582f7195e37a62e","parent":"","exportable":"false","pid":"15324","thread":"http-nio-8888-exec-2","class":"c.x.i.m.K.selectJjhjList_COUNT","rest":"==>  Preparing: SELECT count(0) FROM ks_kw_message msg LEFT JOIN ks_kw_message reply ON msg.ksjhbh = reply.ksjhbh AND msg.id = reply.replay_id AND reply.sczt = '0' WHERE msg.sczt = '0' AND msg.msg_type = '2000' AND msg.origin_app_type = 'HYAPP' AND msg.operate = '0' AND msg.ksjhbh = ? "}
{"@timestamp":"2025-07-28T02:14:50.906Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"9582f7195e37a62e","span":"9582f7195e37a62e","parent":"","exportable":"false","pid":"15324","thread":"http-nio-8888-exec-2","class":"c.x.i.m.K.selectJjhjList_COUNT","rest":"==> Parameters: 2507211439231396864138952572928(String)"}
{"@timestamp":"2025-07-28T02:14:50.916Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"9582f7195e37a62e","span":"9582f7195e37a62e","parent":"","exportable":"false","pid":"15324","thread":"http-nio-8888-exec-2","class":"c.x.i.m.K.selectJjhjList_COUNT","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-28T02:14:50.916Z","severity":"INFO","service":"eeip-standalone-service","trace":"9582f7195e37a62e","span":"9582f7195e37a62e","parent":"","exportable":"false","pid":"15324","thread":"http-nio-8888-exec-2","class":"c.x.i.s.impl.KsKwMessageServiceImpl","rest":"紧急呼叫查询结束，共查询到0条记录"}
{"@timestamp":"2025-07-28T02:14:50.941Z","severity":"INFO","service":"eeip-standalone-service","trace":"9582f7195e37a62e","span":"9582f7195e37a62e","parent":"","exportable":"false","pid":"15324","thread":"http-nio-8888-exec-2","class":"c.x.i.facade.manager.CxtjController","rest":"处理请求结束：[紧急呼叫查询][/manager/identityverify/cxtj/jjhjcx]reqModel:JjhjCxReq[ksjhbh=2507211439231396864138952572928,kcbh=<null>,clzt=<null>,startTime=<null>,endTime=<null>,pageNum=1,pageSize=10,msgId=<null>,transChannel=<null>,channelSerialNo=<null>,channelDate=<null>,channelTime=<null>,token=<null>,loginToken=<null>,transCode=<null>,clientIp=<null>,merchantId=<null>,appPackage=<null>],respModel:JjhjCxResp[data=Page{count=true, pageNum=1, pageSize=10, startRow=0, endRow=10, total=0, pages=0, reasonable=true, pageSizeZero=false}[],totalRows=0,pageNum=1,pageSize=10,msgId=<null>,transChannel=<null>,channelSerialNo=<null>,transCode=<null>,respDate=<null>,respTime=<null>,ext=<null>]"}
{"@timestamp":"2025-07-28T02:15:00.003Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-2","class":"c.x.i.util.schedule.ScheduleJobExecutor","rest":"任务准备执行，任务ID：24081610292802018092458168076288"}
{"@timestamp":"2025-07-28T02:15:00.004Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-2","class":"c.x.i.m.KsKsjhMapper.selectByExample","rest":"==>  Preparing: SELECT ksjhbh,mc,kssj,jssj,xn,xq,sfmr,kszt,sbjm,sbkqsj,sbgbsj,cjlx,kslx,qysbbmd,wifiqy,cjsj,xgsj,scztw,kzqy,qydsxf,dsxfsj,dsxfzt,jkqdrs,dbms FROM ks_ksjh WHERE ( ( scztw = ? and qydsxf = ? and dsxfzt = ? ) ) "}
{"@timestamp":"2025-07-28T02:15:00.005Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-2","class":"c.x.i.m.KsKsjhMapper.selectByExample","rest":"==> Parameters: 0(String), 1(Integer), 0(Integer)"}
{"@timestamp":"2025-07-28T02:15:00.014Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-2","class":"c.x.i.m.KsKsjhMapper.selectByExample","rest":"<==      Total: 0"}
{"@timestamp":"2025-07-28T02:15:00.014Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-2","class":"c.x.i.s.i.s.KsjhSjdsxfTaskServiceImpl","rest":"当前无下发任务！"}
{"@timestamp":"2025-07-28T02:15:00.014Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-2","class":"c.x.i.util.schedule.ScheduleJobExecutor","rest":"任务执行完毕，任务ID：24081610292802018092458168076288  总共耗时：11毫秒"}
{"@timestamp":"2025-07-28T02:15:00.014Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-2","class":"c.x.i.m.S.insertSelective","rest":"==>  Preparing: INSERT INTO schedule_job_log ( log_id,job_id,job_name,bean_name,params,result,times,create_time ) VALUES( ?,?,?,?,?,?,?,? ) "}
{"@timestamp":"2025-07-28T02:15:00.015Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"quartzScheduler_Worker-2","class":"c.x.i.m.S.insertSelective","rest":"==> Parameters: 25072810150002268857578316395520(String), 24081610292802018092458168076288(String), ksjhSjdsxf(String), ksjhSjdsxfTaskService(String), (String), 0(Integer), 11(Integer), 2025-07-28 10:15:00.003(Timestamp)"}
{"@timestamp":"2025-07-28T02:15:52.014Z","severity":"INFO","service":"eeip-standalone-service","trace":"1b7f4eba129627ef","span":"1b7f4eba129627ef","parent":"","exportable":"false","pid":"15324","thread":"http-nio-8888-exec-6","class":"c.x.i.facade.manager.CxtjController","rest":"收到请求开始：[紧急呼叫查询][/manager/identityverify/cxtj/jjhjcx]reqModel:JjhjCxReq[ksjhbh=2507211439231396864138952572928,kcbh=<null>,clzt=<null>,startTime=<null>,endTime=<null>,pageNum=1,pageSize=10,msgId=<null>,transChannel=<null>,channelSerialNo=<null>,channelDate=<null>,channelTime=<null>,token=<null>,loginToken=<null>,transCode=<null>,clientIp=<null>,merchantId=<null>,appPackage=<null>]"}
{"@timestamp":"2025-07-28T02:15:52.028Z","severity":"INFO","service":"eeip-standalone-service","trace":"1b7f4eba129627ef","span":"1b7f4eba129627ef","parent":"","exportable":"false","pid":"15324","thread":"http-nio-8888-exec-6","class":"c.x.i.s.impl.KsKwMessageServiceImpl","rest":"紧急呼叫查询开始，查询条件：JjhjCxDTO[ksjhbh=2507211439231396864138952572928,kcbh=<null>,clzt=<null>,startTime=<null>,endTime=<null>,pageNum=1,pageSize=10]"}
{"@timestamp":"2025-07-28T02:15:52.030Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"1b7f4eba129627ef","span":"1b7f4eba129627ef","parent":"","exportable":"false","pid":"15324","thread":"http-nio-8888-exec-6","class":"c.x.i.m.K.selectJjhjList_COUNT","rest":"==>  Preparing: SELECT count(0) FROM ks_kw_message msg LEFT JOIN ks_kw_message reply ON msg.ksjhbh = reply.ksjhbh AND msg.id = reply.replay_id AND reply.sczt = '0' WHERE msg.sczt = '0' AND msg.msg_type = '2000' AND msg.origin_app_type = 'HYAPP' AND msg.operate = '0' AND msg.ksjhbh = ? "}
{"@timestamp":"2025-07-28T02:15:52.030Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"1b7f4eba129627ef","span":"1b7f4eba129627ef","parent":"","exportable":"false","pid":"15324","thread":"http-nio-8888-exec-6","class":"c.x.i.m.K.selectJjhjList_COUNT","rest":"==> Parameters: 2507211439231396864138952572928(String)"}
{"@timestamp":"2025-07-28T02:15:52.037Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"1b7f4eba129627ef","span":"1b7f4eba129627ef","parent":"","exportable":"false","pid":"15324","thread":"http-nio-8888-exec-6","class":"c.x.i.m.K.selectJjhjList_COUNT","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-28T02:15:52.037Z","severity":"INFO","service":"eeip-standalone-service","trace":"1b7f4eba129627ef","span":"1b7f4eba129627ef","parent":"","exportable":"false","pid":"15324","thread":"http-nio-8888-exec-6","class":"c.x.i.s.impl.KsKwMessageServiceImpl","rest":"紧急呼叫查询结束，共查询到0条记录"}
{"@timestamp":"2025-07-28T02:15:52.063Z","severity":"INFO","service":"eeip-standalone-service","trace":"1b7f4eba129627ef","span":"1b7f4eba129627ef","parent":"","exportable":"false","pid":"15324","thread":"http-nio-8888-exec-6","class":"c.x.i.facade.manager.CxtjController","rest":"处理请求结束：[紧急呼叫查询][/manager/identityverify/cxtj/jjhjcx]reqModel:JjhjCxReq[ksjhbh=2507211439231396864138952572928,kcbh=<null>,clzt=<null>,startTime=<null>,endTime=<null>,pageNum=1,pageSize=10,msgId=<null>,transChannel=<null>,channelSerialNo=<null>,channelDate=<null>,channelTime=<null>,token=<null>,loginToken=<null>,transCode=<null>,clientIp=<null>,merchantId=<null>,appPackage=<null>],respModel:JjhjCxResp[data=Page{count=true, pageNum=1, pageSize=10, startRow=0, endRow=10, total=0, pages=0, reasonable=true, pageSizeZero=false}[],totalRows=0,pageNum=1,pageSize=10,msgId=<null>,transChannel=<null>,channelSerialNo=<null>,transCode=<null>,respDate=<null>,respTime=<null>,ext=<null>]"}
{"@timestamp":"2025-07-28T02:16:42.105Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"Thread-113","class":"o.s.c.n.e.s.EurekaServiceRegistry","rest":"Unregistering application EEIP-STANDALONE-SERVICE with eureka with status DOWN"}
{"@timestamp":"2025-07-28T02:16:46.140Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"Thread-113","class":"o.s.i.m.i.MqttPahoMessageDrivenChannelAdapter","rest":"stopped inbound"}
{"@timestamp":"2025-07-28T02:16:46.140Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"Thread-113","class":"o.s.i.g.GatewayProxyFactoryBean$MethodInvocationGateway","rest":"stopped mqttGateway"}
{"@timestamp":"2025-07-28T02:16:46.141Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"Thread-113","class":"o.s.i.gateway.GatewayProxyFactoryBean","rest":"stopped mqttGateway"}
{"@timestamp":"2025-07-28T02:16:46.141Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"Thread-113","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel"}
{"@timestamp":"2025-07-28T02:16:46.142Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"Thread-113","class":"o.s.i.channel.PublishSubscribeChannel","rest":"Channel 'application-1.errorChannel' has 0 subscriber(s)."}
{"@timestamp":"2025-07-28T02:16:46.142Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"Thread-113","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"stopped _org.springframework.integration.errorLogger"}
{"@timestamp":"2025-07-28T02:16:46.142Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"Thread-113","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"Removing {message-handler:mqttConfig.mqttOutbound.serviceActivator} as a subscriber to the 'mqttOutboundChannel' channel"}
{"@timestamp":"2025-07-28T02:16:46.142Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"Thread-113","class":"o.s.integration.channel.ExecutorChannel","rest":"Channel 'application-1.mqttOutboundChannel' has 0 subscriber(s)."}
{"@timestamp":"2025-07-28T02:16:46.142Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"Thread-113","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"stopped mqttConfig.mqttOutbound.serviceActivator"}
{"@timestamp":"2025-07-28T02:16:46.142Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"Thread-113","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"Removing {message-handler:mqttConfig.handler.serviceActivator} as a subscriber to the 'mqttInputChannel' channel"}
{"@timestamp":"2025-07-28T02:16:46.142Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"Thread-113","class":"o.s.integration.channel.ExecutorChannel","rest":"Channel 'application-1.mqttInputChannel' has 0 subscriber(s)."}
{"@timestamp":"2025-07-28T02:16:46.142Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"Thread-113","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"stopped mqttConfig.handler.serviceActivator"}
{"@timestamp":"2025-07-28T02:16:50.248Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"Thread-113","class":"o.s.s.concurrent.ThreadPoolTaskScheduler","rest":"Shutting down ExecutorService 'taskScheduler'"}
{"@timestamp":"2025-07-28T02:16:50.248Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"Thread-113","class":"o.s.s.quartz.SchedulerFactoryBean","rest":"Shutting down Quartz Scheduler"}
{"@timestamp":"2025-07-28T02:16:50.250Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15324","thread":"Thread-113","class":"o.s.s.concurrent.ThreadPoolTaskExecutor","rest":"Shutting down ExecutorService"}
{"@timestamp":"2025-07-28T02:17:03.437Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"35440","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$f195d098] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-28T02:17:03.918Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"35440","thread":"main","class":"o.s.c.c.c.ConfigServicePropertySourceLocator","rest":"Fetching config from server at : http://localhost:8888"}
{"@timestamp":"2025-07-28T02:17:06.060Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"35440","thread":"main","class":"o.s.c.c.c.ConfigServicePropertySourceLocator","rest":"Connect Timeout Exception on Url - http://localhost:8888. Will be trying the next url if available"}
{"@timestamp":"2025-07-28T02:17:06.062Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"35440","thread":"main","class":"o.s.c.c.c.ConfigServicePropertySourceLocator","rest":"Could not locate PropertySource: I/O error on GET request for \"http://localhost:8888/eeip-standalone-service/default\": Connection refused: connect; nested exception is java.net.ConnectException: Connection refused: connect"}
{"@timestamp":"2025-07-28T02:17:06.065Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"35440","thread":"main","class":"c.x.standalone.StandaloneApplication","rest":"The following profiles are active: alone"}
{"@timestamp":"2025-07-28T02:17:11.654Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"35440","thread":"main","class":"o.s.d.r.c.RepositoryConfigurationDelegate","rest":"Multiple Spring Data modules found, entering strict repository configuration mode!"}
{"@timestamp":"2025-07-28T02:17:11.658Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"35440","thread":"main","class":"o.s.d.r.c.RepositoryConfigurationDelegate","rest":"Bootstrapping Spring Data Redis repositories in DEFAULT mode."}
{"@timestamp":"2025-07-28T02:17:11.942Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"35440","thread":"main","class":"o.s.d.r.c.RepositoryConfigurationDelegate","rest":"Finished Spring Data repository scanning in 260ms. Found 0 Redis repository interfaces."}
{"@timestamp":"2025-07-28T02:17:12.136Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"35440","thread":"main","class":"o.s.boot.actuate.endpoint.EndpointId","rest":"Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format."}
{"@timestamp":"2025-07-28T02:17:12.356Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"35440","thread":"main","class":"o.s.boot.actuate.endpoint.EndpointId","rest":"Endpoint ID 'hystrix.stream' contains invalid characters, please migrate to a valid format."}
{"@timestamp":"2025-07-28T02:17:12.841Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"35440","thread":"main","class":"o.s.cloud.context.scope.GenericScope","rest":"BeanFactory id=5b395cf1-ddbf-331b-876f-aa2c7020b748"}
{"@timestamp":"2025-07-28T02:17:12.866Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"35440","thread":"main","class":"o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor","rest":"No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created."}
{"@timestamp":"2025-07-28T02:17:12.873Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"35440","thread":"main","class":"o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor","rest":"No bean named 'taskScheduler' has been explicitly defined. Therefore, a default ThreadPoolTaskScheduler will be created."}
{"@timestamp":"2025-07-28T02:17:12.887Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"35440","thread":"main","class":"o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor","rest":"No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created."}
{"@timestamp":"2025-07-28T02:17:12.981Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"35440","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'configProperties' of type [com.xcwlkj.core.config.properties.ConfigProperties$$EnhancerBySpringCGLIB$$3273ad31] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-28T02:17:12.983Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"35440","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'asyncTaskExecutorConfiguration' of type [com.xcwlkj.core.config.AsyncTaskExecutorConfiguration$$EnhancerBySpringCGLIB$$eba5f261] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-28T02:17:13.077Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"35440","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$cb3f65c0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-28T02:17:13.404Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"35440","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$d57bcd9b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-28T02:17:13.465Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"35440","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'redisConfig' of type [com.xcwlkj.standalone.config.RedisConfig$$EnhancerBySpringCGLIB$$b11eab18] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-28T02:17:13.513Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"35440","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'appConfig' of type [com.xcwlkj.standalone.config.AppConfig$$EnhancerBySpringCGLIB$$ed3fdcbe] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-28T02:17:13.548Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"35440","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'integrationDisposableAutoCreatedBeans' of type [org.springframework.integration.config.annotation.Disposables] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-28T02:17:13.593Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"35440","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'com.xcwlkj.biz.core.feignclient.FeignClientConfig' of type [com.xcwlkj.biz.core.feignclient.FeignClientConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-28T02:17:13.604Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"35440","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.integration.config.IntegrationManagementConfiguration' of type [org.springframework.integration.config.IntegrationManagementConfiguration$$EnhancerBySpringCGLIB$$406d88ca] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-28T02:17:13.648Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"35440","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$f195d098] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-28T02:17:14.249Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"35440","thread":"main","class":"o.s.b.w.embedded.tomcat.TomcatWebServer","rest":"Tomcat initialized with port(s): 8888 (http)"}
{"@timestamp":"2025-07-28T02:17:14.400Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"35440","thread":"main","class":"o.s.web.context.ContextLoader","rest":"Root WebApplicationContext: initialization completed in 8310 ms"}
{"@timestamp":"2025-07-28T02:17:17.807Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"35440","thread":"main","class":"o.s.b.a.e.web.ServletEndpointRegistrar","rest":"Registered '/actuator/hystrix.stream' to hystrix.stream-actuator-endpoint"}
{"@timestamp":"2025-07-28T02:17:17.904Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"35440","thread":"main","class":"o.s.s.concurrent.ThreadPoolTaskExecutor","rest":"Initializing ExecutorService"}
{"@timestamp":"2025-07-28T02:17:18.012Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"35440","thread":"main","class":"o.s.s.concurrent.ThreadPoolTaskExecutor","rest":"Initializing ExecutorService"}
{"@timestamp":"2025-07-28T02:17:18.114Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"35440","thread":"main","class":"o.s.s.concurrent.ThreadPoolTaskExecutor","rest":"Initializing ExecutorService"}
{"@timestamp":"2025-07-28T02:17:18.118Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"35440","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsServerUrl=http://************:8811"}
{"@timestamp":"2025-07-28T02:17:18.120Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"35440","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsChannel=ZJKSZHPT"}
{"@timestamp":"2025-07-28T02:17:18.120Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"35440","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsAppId=e046e7c7e7fbf1f1a4183e00c76e0182"}
{"@timestamp":"2025-07-28T02:17:18.120Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"35440","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsAppSecret=6d0dc6d02af7bab1f1ed39a7baadbb55"}
{"@timestamp":"2025-07-28T02:17:19.102Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"35440","thread":"main","class":"c.x.core.interceptor.SqlLogInterceptor","rest":"[打印SQL拦截器创建]noticeTime=5.0秒"}
{"@timestamp":"2025-07-28T02:17:21.826Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"35440","thread":"main","class":"c.x.c.c.AsyncTaskExecutorConfiguration","rest":"Creating Async Task Executor"}
{"@timestamp":"2025-07-28T02:17:21.828Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"35440","thread":"main","class":"o.s.s.concurrent.ThreadPoolTaskExecutor","rest":"Initializing ExecutorService"}
{"@timestamp":"2025-07-28T02:17:23.812Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"35440","thread":"main","class":"com.xcwlkj.pubc.config.SmsConfig","rest":"SMS Bean IAcsClient Start"}
{"@timestamp":"2025-07-28T02:17:23.825Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"35440","thread":"main","class":"com.xcwlkj.pubc.config.SmsConfig","rest":"加载SMS Bean IAcsClient OK"}
