<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xcwlkj.identityverify.mapper.UtilsMapper">

	<select id="tableContains" resultType="java.lang.String">
		select table_name from information_schema.tables
			where table_schema= #{dbName} and table_name = #{tableName}
	</select>

	<select id="filterTables" resultType="java.lang.String">
		select table_name from information_schema.tables
			where table_schema= #{dbName}
			<if test="tables !=null and tables.size > 0">
				AND table_name in
				<foreach collection="tables" item="item" index="index" open="(" close=")" separator=",">
					#{item}
				</foreach>
			</if>
	</select>

	<!-- 更新表结构 -->
	<update id="alterTable" parameterType="java.lang.String">
		ALTER TABLE ${param.tableName} add COLUMN ${param.columnName} ${param.columnType} COMMENT #{param.columnRemark};
	</update>

	<update id="alterColumn" parameterType="java.lang.String">
		ALTER TABLE ${param.tableName} modify COLUMN ${param.columnName} ${param.columnType} COMMENT #{param.columnRemark};
	</update>

    <update id="createCsDzgh">
		CREATE TABLE IF NOT EXISTS `cs_dzgh` (
		`id` varchar(32) NOT NULL,
		`cslx` varchar(32) DEFAULT NULL COMMENT 'KD(考点) KC(考场)',
		`type` varchar(32) DEFAULT NULL COMMENT '类型KCWG(考场网关)',
		`classify` varchar(32) DEFAULT NULL COMMENT '分类DEVCFG(设备配置)',
		`key` varchar(32) DEFAULT NULL COMMENT 'key值（IP:ip地址,NETMASK:掩码,GATEWAY:网关IP地址,IPRANGE:ip范围,PCNUM:主机数）',
		`value` varchar(128) DEFAULT NULL COMMENT 'value值',
		`create_time` datetime DEFAULT NULL COMMENT '创建时间',
		`update_time` datetime DEFAULT NULL COMMENT '更新时间',
		`status` decimal(10,0) DEFAULT NULL COMMENT '1-启用 0-不启用',
		`sort` decimal(10,0) DEFAULT NULL COMMENT '排序',
		`jsh` varchar(32) DEFAULT NULL COMMENT '教室号',
		PRIMARY KEY (`id`),
		KEY `index_dzgh_1` (`jsh`,`value`)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8
	</update>
	<update id="createSbIpdfp">
		CREATE TABLE IF NOT EXISTS `sb_ipdfp` (
		`id` varchar(32) NOT NULL,
		`ipstart` varchar(32) DEFAULT NULL COMMENT 'IP起',
		`ipend` varchar(32) DEFAULT NULL COMMENT 'IP尾',
		`zwym` varchar(32) DEFAULT NULL COMMENT '子网掩码',
		`wg` varchar(32) DEFAULT NULL COMMENT '网关',
		`create_time` datetime DEFAULT NULL,
		`update_time` datetime DEFAULT NULL,
		PRIMARY KEY (`id`)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
	</update>
	<update id="createSbSbjcb">
		CREATE TABLE IF NOT EXISTS `sb_sbjcb` (
		`id` varchar(32) NOT NULL,
		`sbxxbh` varchar(32) DEFAULT NULL COMMENT '设备信息编号',
		`ip` varchar(32) DEFAULT NULL COMMENT 'ip',
		`xlh` varchar(128) DEFAULT NULL COMMENT '序列号',
		`detect_type` varchar(10) DEFAULT NULL COMMENT '检测类型1-ping 2-mqtt',
		`detect_result` varchar(10) DEFAULT NULL COMMENT '检测结果1-成功  -1001-ping失败  -2001-mqtt超时 -9999-其他错误',
		`detect_desc` varchar(255) DEFAULT NULL COMMENT '检测描述',
		`detect_exception` text COMMENT '检测异常',
		`detect_time` timestamp NULL DEFAULT NULL COMMENT '检测时间',
		PRIMARY KEY (`id`)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
	</update>

	<!-- 查询列是否存在 -->
	<select id="findColumnExist" resultType="java.lang.Integer">
		SELECT
			 COUNT(1)
		FROM
			information_schema. COLUMNS
		WHERE
			table_schema = #{dbName}
		AND table_name = #{tableName}
		AND column_name= #{columnName}
	</select>

	<update id="createCmdTable">
		create table biz_send_command
		(
			id                varchar(32)  not null
				primary key,
			cmd_type          varchar(10)  null comment '任务类型 0-配置端口转发,1-接入配置参数，2-NTP配置参数，3-设备重启',
			cmd_progress      varchar(10)  null comment '进度',
			complete          int(10)      null comment '完成状态 -1-失败 0-开始 1-进行中 2-完成',
			cmd_progress_desc varchar(255) null comment '任务描述',
			dept_id           varchar(32)  null comment '部门id',
			dept_name         varchar(128) null comment '部门名称',
			dept_type         varchar(10)  null comment '部门类型',
			cmd_param         longtext     null comment '任务参数',
			create_time       datetime     null comment '创建时间',
			update_time       datetime     null comment '更新时间'
		)
			ENGINE=InnoDB DEFAULT charset = utf8
	</update>

	<update id="createCmdDevTable">
		create table biz_send_command_dev
		(
			cmd_id varchar(32)  not null comment '命令ID',
			sbxlh  varchar(128) not null comment '设备序列号',
			jgbh   varchar(64)  null comment '机构编号',
			jglx   varchar(32)  null comment '机构类型 ksgljg-考试管理机构 kd-考点 kc-考场',
			zhsbsj datetime     null comment '最后上报时间',
			xfzt   int          null comment '-2-设备不在线 -1-超时 0-失败 1-下发中 2-成功',
			zxsj   datetime     null comment '执行时间',
			primary key (cmd_id, sbxlh)
		)
			ENGINE=InnoDB DEFAULT charset = utf8
	</update>
	<update id="createKsBmxxKstzz">
		CREATE TABLE IF NOT EXISTS `ks_bmxx_kstzz` (
			 `ksid` varchar(32) NOT NULL COMMENT '考生唯一标志号',
			 `ksjhbh` varchar(32) NOT NULL COMMENT '考试计划标志号',
			 `ksh` varchar(20) DEFAULT NULL COMMENT '考生号',
			 `sfzjhm` varchar(20) NOT NULL COMMENT '身份证件号',
			 `kszpid` varchar(128) DEFAULT NULL COMMENT '考生照片ID',
			 `tzzzt` varchar(2) DEFAULT NULL COMMENT '0-未获取到特征值；1-已获取到特征值；2-获取特征值失败',
			 `tzzid` varchar(128) DEFAULT NULL COMMENT '特征值ID',
			 `tzzhqsj` datetime DEFAULT NULL COMMENT '特征值获取时间',
			 `create_time` datetime DEFAULT NULL COMMENT '创建时间',
			 `update_time` datetime DEFAULT NULL COMMENT '更新时间',
			 `tzzfs` varchar(1) DEFAULT NULL COMMENT '特征值方式',
			 `bmh` varchar(32) DEFAULT NULL COMMENT '报名号',
			 PRIMARY KEY (`ksid`) USING BTREE
		) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='考生报名特征值'
	</update>
	<update id="createKsYdsbSbkszs">
		CREATE TABLE IF NOT EXISTS `ks_ydsb_sbkszs` (
		  `id` varchar(32) NOT NULL,
		  `ksjhbh` varchar(32) NOT NULL COMMENT '考试计划编号',
		  `sbxlh` varchar(64) NOT NULL COMMENT '设备序列号',
		  `sbsj` datetime DEFAULT NULL COMMENT '上报时间',
		  `kdmc` varchar(128) DEFAULT NULL COMMENT '考点名称',
		  `sbkszs` int(10) DEFAULT NULL COMMENT '上报考生总数=入场人数+缺考人数',
		  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
		  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
		  `ccm` varchar(10) DEFAULT NULL COMMENT '场次码',
		  `kcbh` varchar(32) DEFAULT NULL COMMENT '考场编号',
		  `ljkcbh` varchar(32) DEFAULT NULL COMMENT '逻辑考场编号',
		  `sblx` varchar(10) DEFAULT NULL COMMENT '上报类型 BZ-标准 FB-非标',
		  PRIMARY KEY (`id`),
		  KEY `ks_ydsb_sbkszs__index_1` (`ksjhbh`,`ccm`,`sbxlh`,`kcbh`,`ljkcbh`)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8
	</update>
	<update id="createSbsj">
		create table if not exists sb_sbsj
		(
			id               varchar(32) charset utf8  not null
				primary key,
			sbxlh            varchar(128) charset utf8 not null comment '设备序列号',
			sblx             varchar(32) charset utf8  not null comment 'kdwg考点网关 kcwg考场网关 ydzd移动终端',
			event_type       varchar(16) charset utf8  not null comment 'ONLINE-上线事件 USB-usb事件 WIFI-wifi事件 YDZDBATTERY-移动终端电池事件',
			event_time       datetime                  not null comment '事件时间',
			csbh             varchar(32) charset utf8  null comment '场所编号',
			event_detail     text charset utf8         null comment '事件详情',
			create_time      datetime                  null comment '创建时间',
			update_time      datetime                  null comment '更新时间',
			status           varchar(4) charset utf8   null comment '状态',
			`repeat`         varchar(4) charset utf8   null comment '是否重复',
			repeat_count     int(10)                   null comment '重复次数 ',
			`desc`           text charset utf8         null comment '描述',
			param1           varchar(64) charset utf8  null comment '扩展1',
			param2           varchar(64) charset utf8  null comment '扩展2',
			param3           varchar(64) charset utf8  null comment '扩展3',
			event_time_start datetime                  null comment '事件开始时间',
			event_time_end   datetime                  null comment '事件结束时间'
		) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='设备事件'
	</update>
	<update id="createBizCsxxExt">
		create table if not exists biz_csxx_ext
		(
			CSBH             varchar(64)  not null comment '场所编号'
				primary key,
			SBXLH            varchar(128) null comment '设备序列号',
			YDZD_APP_VERSION varchar(128) null comment '移动终端app版本',
			ZHSBSJ           datetime     null comment '最后上报时间',
			XFZT             int          null comment '0-失败 1-下发中 2-成功'
		) ENGINE=InnoDB DEFAULT CHARSET=utf8
	</update>
	<update id="createBizFsgjTask">
		create table if not exists biz_fsgj_task
		(
			ID              varchar(32)  not null
				primary key,
			NAME            varchar(128) null comment '任务名称',
			T_TYPE          varchar(10)  null comment '任务类型 1-移动终端APP',
			T_PROGRESS      varchar(10)  null comment '进度',
			COMPLETE        int(10)      null comment '完成状态 -1-失败 0-开始 1-进行中 2-完成',
			T_PROGRESS_DESC varchar(255) null comment '任务描述',
			T_PARAM         longtext     null comment '任务参数'
		) ENGINE=InnoDB DEFAULT CHARSET=utf8
	</update>
	<update id="createBizFsgjTaskJg">
		create table if not exists biz_fsgj_task_jg
		(
			ID      varchar(32) not null
				primary key,
			TASK_ID varchar(32) null comment '任务id',
			CSBH    varchar(64) null comment '场所编号',
			SBXLH   varchar(64) null comment '设备序列号'
		) ENGINE=InnoDB DEFAULT CHARSET=utf8
	</update>
	<update id="createKsKkwMsg">
		CREATE TABLE IF NOT EXISTS `ks_kkw_msg` (
									  `ID` varchar(32) NOT NULL,
									  `KSJHBH` varchar(32) NOT NULL COMMENT '考试计划编号',
									  `CCM` varchar(16) NOT NULL COMMENT '场次码',
									  `BZHKDID` varchar(32) DEFAULT NULL COMMENT '标准化考点id',
									  `BZHKCID` varchar(32) DEFAULT NULL COMMENT '标准化考场id',
									  `SN` varchar(128) DEFAULT NULL COMMENT '设备序列号',
									  `SCZT` varchar(6) DEFAULT NULL COMMENT '删除状态 0-正常  1-删除',
									  `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
									  `UPDATE_TIME` datetime DEFAULT NULL COMMENT '更新时间',
									  `KS_ZKZH` varchar(128) DEFAULT NULL COMMENT '考生准考证号',
									  `KS_BPZWH` varchar(32) DEFAULT NULL COMMENT '考生编排座位号',
									  `KS_SJZWH` varchar(32) DEFAULT NULL COMMENT '考生实际座位号',
									  `KS_KKW` varchar(32) DEFAULT NULL COMMENT '空考位',
									  `ZCQSWZM` varchar(16) DEFAULT NULL COMMENT '座次起始位置码',
									  `ZWBJFSM` varchar(16) DEFAULT NULL COMMENT '座位布局方式码',
									  `ZWPLFSM` varchar(16) DEFAULT NULL COMMENT '座位排列方式码',
									  `LJKCH` varchar(32) DEFAULT NULL COMMENT '逻辑考场号',
									  `KCH` varchar(32) DEFAULT NULL COMMENT '考场号',
									  `DEV_TYPE` varchar(16) DEFAULT NULL COMMENT '设备类型',
									  `RCBZ` varchar(4) DEFAULT NULL COMMENT '入场备注0-默认 1-误识别 2-坐错他人位置 3-实际未参加考试 4-他人坐错位置 5-人工  6-缺考（空位）7-无编排',
									  `SJYXJ` int(10) DEFAULT NULL COMMENT '数据优先级 数据优先级 10-普通考场（入场上报）20-考场空位上报 30-备用考场上报入场',
									  `TIMESTAMP` datetime DEFAULT NULL COMMENT '时间戳',
									  `YZFS` varchar(10) DEFAULT NULL COMMENT '验证方式',
									  `YZJG` varchar(10) DEFAULT NULL COMMENT '验证结果',
									  `SFRC` varchar(1) DEFAULT NULL COMMENT '是否入场',
									  `RCSJ` varchar(17) DEFAULT NULL COMMENT '入场时间',
									  `RCSJFZ` varchar(12) DEFAULT NULL COMMENT '入场时间分组',
									  `RGYZJG` varchar(10) DEFAULT NULL COMMENT '人工验证结果 1-通过 0-不通过',
									  `SJLY` varchar(10) DEFAULT NULL COMMENT '数据来源  web - 来自于 平台的页面 ；pad  - 来自于 核验终端',
									  `CZSJ` datetime DEFAULT NULL COMMENT '操作时间',
									  PRIMARY KEY (`ID`)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8
	</update>
</mapper>