<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xcwlkj.identityverify.mapper.KsKkwMsgMapper">
	<resultMap id="BaseResultMap" type="com.xcwlkj.identityverify.model.domain.KsKkwMsg">
        <id column="ID" jdbcType="VARCHAR" property="id" />
        <result column="KSJHBH" jdbcType="VARCHAR" property="ksjhbh" />
        <result column="CCM" jdbcType="VARCHAR" property="ccm" />
        <result column="BZHKDID" jdbcType="VARCHAR" property="bzhkdid" />
        <result column="BZHKCID" jdbcType="VARCHAR" property="bzhkcid" />
        <result column="SN" jdbcType="VARCHAR" property="sn" />
        <result column="SCZT" jdbcType="VARCHAR" property="sczt" />
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
        <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="KS_ZKZH" jdbcType="VARCHAR" property="ksZkzh" />
        <result column="KS_BPZWH" jdbcType="VARCHAR" property="ksBpzwh" />
        <result column="KS_SJZWH" jdbcType="VARCHAR" property="ksSjzwh" />
        <result column="KS_KKW" jdbcType="VARCHAR" property="ksKkw" />
        <result column="ZCQSWZM" jdbcType="VARCHAR" property="zcqswzm" />
        <result column="ZWBJFSM" jdbcType="VARCHAR" property="zwbjfsm" />
        <result column="ZWPLFSM" jdbcType="VARCHAR" property="zwplfsm" />
        <result column="LJKCH" jdbcType="VARCHAR" property="ljkch" />
        <result column="KCH" jdbcType="VARCHAR" property="kch" />
        <result column="DEV_TYPE" jdbcType="VARCHAR" property="devType" />
        <result column="RCBZ" jdbcType="VARCHAR" property="rcbz" />
        <result column="SJYXJ" jdbcType="INTEGER" property="sjyxj" />
        <result column="TIMESTAMP" jdbcType="TIMESTAMP" property="timestamp" />
        <result column="YZFS" jdbcType="VARCHAR" property="yzfs" />
        <result column="YZJG" jdbcType="VARCHAR" property="yzjg" />
        <result column="SFRC" jdbcType="VARCHAR" property="sfrc" />
        <result column="RCSJ" jdbcType="VARCHAR" property="rcsj" />
        <result column="RCSJFZ" jdbcType="VARCHAR" property="rcsjfz" />
        <result column="RGYZJG" jdbcType="VARCHAR" property="rgyzjg" />
        <result column="SJLY" jdbcType="VARCHAR" property="sjly" />
        <result column="CZSJ" jdbcType="TIMESTAMP" property="czsj" />
	</resultMap>

	<!-- 列信息 -->
	<sql id="Base_Column_List">
        ID,
        KSJHBH,
        CCM,
        BZHKDID,
        BZHKCID,
        SN,
        SCZT,
        CREATE_TIME,
        UPDATE_TIME,
        KS_ZKZH,
        KS_BPZWH,
        KS_SJZWH,
        KS_KKW,
        ZCQSWZM,
        ZWBJFSM,
        ZWPLFSM,
        LJKCH,
        KCH,
        DEV_TYPE,
        RCBZ,
        SJYXJ,
        TIMESTAMP,
        YZFS,
        YZJG,
        SFRC,
        RCSJ,
        RCSJFZ,
        RGYZJG,
        SJLY,
        CZSJ
	</sql>

	<!-- where条件 -->
	<sql id="Base_Where_Condition">
        <if test="id != null and id != ''">
            AND ID = #{id,jdbcType=VARCHAR}
        </if>
        <if test="ksjhbh != null and ksjhbh != ''">
            AND KSJHBH = #{ksjhbh,jdbcType=VARCHAR}
        </if>
        <if test="ccm != null and ccm != ''">
            AND CCM = #{ccm,jdbcType=VARCHAR}
        </if>
        <if test="bzhkdid != null and bzhkdid != ''">
            AND BZHKDID = #{bzhkdid,jdbcType=VARCHAR}
        </if>
        <if test="bzhkcid != null and bzhkcid != ''">
            AND BZHKCID = #{bzhkcid,jdbcType=VARCHAR}
        </if>
        <if test="sn != null and sn != ''">
            AND SN = #{sn,jdbcType=VARCHAR}
        </if>
        <if test="sczt != null and sczt != ''">
            AND SCZT = #{sczt,jdbcType=VARCHAR}
        </if>
        <if test="ksZkzh != null and ksZkzh != ''">
            AND KS_ZKZH = #{ksZkzh,jdbcType=VARCHAR}
        </if>
        <if test="ksBpzwh != null and ksBpzwh != ''">
            AND KS_BPZWH = #{ksBpzwh,jdbcType=VARCHAR}
        </if>
        <if test="ksSjzwh != null and ksSjzwh != ''">
            AND KS_SJZWH = #{ksSjzwh,jdbcType=VARCHAR}
        </if>
        <if test="ksKkw != null and ksKkw != ''">
            AND KS_KKW = #{ksKkw,jdbcType=VARCHAR}
        </if>
        <if test="ljkch != null and ljkch != ''">
            AND LJKCH = #{ljkch,jdbcType=VARCHAR}
        </if>
        <if test="kch != null and kch != ''">
            AND KCH = #{kch,jdbcType=VARCHAR}
        </if>
        <if test="devType != null and devType != ''">
            AND DEV_TYPE = #{devType,jdbcType=VARCHAR}
        </if>
        <if test="rcbz != null and rcbz != ''">
            AND RCBZ = #{rcbz,jdbcType=VARCHAR}
        </if>
        <if test="sjyxj != null">
            AND SJYXJ = #{sjyxj,jdbcType=INTEGER}
        </if>
        <if test="yzfs != null and yzfs != ''">
            AND YZFS = #{yzfs,jdbcType=VARCHAR}
        </if>
        <if test="yzjg != null and yzjg != ''">
            AND YZJG = #{yzjg,jdbcType=VARCHAR}
        </if>
        <if test="sfrc != null and sfrc != ''">
            AND SFRC = #{sfrc,jdbcType=VARCHAR}
        </if>
        <if test="sjly != null and sjly != ''">
            AND SJLY = #{sjly,jdbcType=VARCHAR}
        </if>
	</sql>

</mapper>
