package com.xcwlkj.identityverify.service.impl;

import com.google.common.collect.Lists;
import com.xcwlkj.identityverify.mapper.UtilsMapper;
import com.xcwlkj.identityverify.model.dto.TableParam;
import com.xcwlkj.identityverify.service.UtilsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.sql.DataSource;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Slf4j
@Service("idvUtilsService")
public class UtilsServiceImpl implements UtilsService {

    @Autowired
    private UtilsMapper utilsMapper;
    @Autowired
    @Qualifier("dataSource")
    private DataSource dataSource;

    @Override
    public boolean tableContains(String dbName, String tableName) {
        List<String> list = utilsMapper.tableContains(dbName, tableName);
        if (!CollectionUtils.isEmpty(list)) {
            return true;
        }
        return false;
    }

    @Override
    public Integer findColumnExist(String dbName, String tableName, String columnName) {
        return utilsMapper.findColumnExist(dbName, tableName, columnName);
    }

    @Override
    public Integer alterTable(TableParam param) {
        return utilsMapper.alterTable(param);
    }

    @Override
    public void hsDbInit() {
        try {
            String dbUrl = dataSource.getConnection().getMetaData().getURL();
            String dbName = StringUtils.substringBetween(StringUtils.substringAfter(dbUrl,"jdbc:mysql://"),"/","?");
            log.info("dbName:[{}]",dbName);
            alterKsjhTb(dbName);
            alterKsJkryRcxx(dbName);
            utilsMapper.createCsDzgh();
            utilsMapper.createSbIpdfp();
            utilsMapper.createSbSbjcb();
            utilsMapper.createKsBmxxKstzz();
            utilsMapper.createKsYdsbSbkszs();
            utilsMapper.createBizCsxxExt();
            utilsMapper.createBizFsgjTask();
            utilsMapper.createBizFsgjTaskJg();
            utilsMapper.createKsKkwMsg();
            initCmdTable(dbName);
            alterSbxx(dbName);
            utilsMapper.createSbsj();
            alterKsKsrcxx(dbName);
            alterKsYdsbSbkszs(dbName);
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }

    private void alterKsYdsbSbkszs(String dbName) {
        List<TableParam> tableParamList = new ArrayList<>();
        String ks_ydsb_sbkszsTb = "ks_ydsb_sbkszs";

        TableParam tableParamReportFlag = new TableParam();
        tableParamReportFlag.setTableName(ks_ydsb_sbkszsTb);
        tableParamReportFlag.setColumnName("report_flag");
        tableParamReportFlag.setColumnType("int(4) default 0");
        tableParamReportFlag.setColumnRemark("上报标志 0-未上报 1-已上报");

        tableParamList.add(tableParamReportFlag);

        TableParam tableParamReportTime = new TableParam();
        tableParamReportTime.setTableName(ks_ydsb_sbkszsTb);
        tableParamReportTime.setColumnName("report_time");
        tableParamReportTime.setColumnType("datetime");
        tableParamReportTime.setColumnRemark("上报时间");
        tableParamList.add(tableParamReportTime);

        for (TableParam tableParam : tableParamList) {
            int exist = utilsMapper.findColumnExist(dbName, ks_ydsb_sbkszsTb, tableParam.getColumnName());
            if (exist <= 0) {
                utilsMapper.alterTable(tableParam);
                log.info("数据库dbName:[{}] tbName:[{}] columName:[{}]变更", dbName, ks_ydsb_sbkszsTb, tableParam.getColumnName());
            }
        }

    }

    private void alterKsjhTb(String dbName){
        List<TableParam> tableParamList = Lists.newArrayList();
        String ksjhTb = "ks_ksjh";

        TableParam tableParamQydsxf = new TableParam();
        tableParamQydsxf.setTableName(ksjhTb);
        tableParamQydsxf.setColumnName("qydsxf");
        tableParamQydsxf.setColumnType("int(4) default 0 ");
        tableParamQydsxf.setColumnRemark("启用定时下发 0-不启用 1-启用");

        TableParam tableParamDsxfsj = new TableParam();
        tableParamDsxfsj.setTableName(ksjhTb);
        tableParamDsxfsj.setColumnName("dsxfsj");
        tableParamDsxfsj.setColumnType("datetime");
        tableParamDsxfsj.setColumnRemark("定时下发时间");

        TableParam tableParamDsxfzt = new TableParam();
        tableParamDsxfzt.setTableName(ksjhTb);
        tableParamDsxfzt.setColumnName("dsxfzt");
        tableParamDsxfzt.setColumnType("int(4) default 0 ");
        tableParamDsxfzt.setColumnRemark("0-未下发 1-已下发");

        TableParam tableParamPackJkrybpsj = new TableParam();
        tableParamPackJkrybpsj.setTableName(ksjhTb);
        tableParamPackJkrybpsj.setColumnName("pack_jkrybpsj");
        tableParamPackJkrybpsj.setColumnType("varchar(1) default '0' ");
        tableParamPackJkrybpsj.setColumnRemark("打包监考人员基编排据 1-启用 0-否");

        TableParam tableParamPackJkryjcsj = new TableParam();
        tableParamPackJkryjcsj.setTableName(ksjhTb);
        tableParamPackJkryjcsj.setColumnName("pack_jkryjcsj");
        tableParamPackJkryjcsj.setColumnType("varchar(1) default '0' ");
        tableParamPackJkryjcsj.setColumnRemark("打包监考人员基础数据 1-启用 0-否");

        TableParam tableParamJkqdrs = new TableParam();
        tableParamJkqdrs.setTableName(ksjhTb);
        tableParamJkqdrs.setColumnName("jkqdrs");
        tableParamJkqdrs.setColumnType("int(4) default 0 ");
        tableParamJkqdrs.setColumnRemark("监考签到人数");

        TableParam tableParamDbms = new TableParam();
        tableParamDbms.setTableName(ksjhTb);
        tableParamDbms.setColumnName("dbms");
        tableParamDbms.setColumnType("int(4) default 0 ");
        tableParamDbms.setColumnRemark("对比模式 1-1比1 2-1比N");

        TableParam tableParamCjlx= new TableParam();
        tableParamCjlx.setTableName(ksjhTb);
        tableParamCjlx.setColumnName("cjlx");
        tableParamCjlx.setColumnType("varchar(10) default '0' ");
        tableParamCjlx.setColumnRemark("创建类型0-校级平台建立 1-上级导入");
        tableParamList.add(tableParamCjlx);

        tableParamList.add(tableParamQydsxf);
        tableParamList.add(tableParamDsxfsj);
        tableParamList.add(tableParamDsxfzt);
        tableParamList.add(tableParamPackJkrybpsj);
        tableParamList.add(tableParamPackJkryjcsj);
        tableParamList.add(tableParamJkqdrs);
        tableParamList.add(tableParamDbms);

        for(TableParam tableParam:tableParamList){
            int exist = utilsMapper.findColumnExist(dbName,ksjhTb,tableParam.getColumnName());
            if(exist<=0){
                utilsMapper.alterTable(tableParam);
                log.info("数据库dbName:[{}] tbName:[{}] columName:[{}]变更",dbName,ksjhTb,tableParam.getColumnName());
            }
        }
    }

    private void alterKsJkryRcxx(String dbName) {
        List<TableParam> tableParamList = Lists.newArrayList();
        String ksJkryRcxxTb = "ks_jkry_rcxx";

        TableParam tableParamJklsxh = new TableParam();
        tableParamJklsxh.setTableName(ksJkryRcxxTb);
        tableParamJklsxh.setColumnName("jklsxh");
        tableParamJklsxh.setColumnType("varchar(4)");
        tableParamJklsxh.setColumnRemark("监考老师序号1-甲 2-乙 3-丙");

        tableParamList.add(tableParamJklsxh);
        TableParam tableParamXsd = new TableParam();
        tableParamXsd.setTableName(ksJkryRcxxTb);
        tableParamXsd.setColumnName("xsd");
        tableParamXsd.setColumnType("varchar(32)");
        tableParamXsd.setColumnRemark("相似度");
        tableParamList.add(tableParamXsd);

        TableParam tableParamSbzt = new TableParam();
        tableParamSbzt.setTableName(ksJkryRcxxTb);
        tableParamSbzt.setColumnName("sbzt");
        tableParamSbzt.setColumnType("varchar(4)");
        tableParamSbzt.setColumnRemark("上报状态  0-未上报   1-已上报   -1-上报失败");
        tableParamList.add(tableParamSbzt);

        TableParam tableParamSbsj = new TableParam();
        tableParamSbsj.setTableName(ksJkryRcxxTb);
        tableParamSbsj.setColumnName("sbsj");
        tableParamSbsj.setColumnType("datetime");
        tableParamSbsj.setColumnRemark("最近一次上报的时间");
        tableParamList.add(tableParamSbsj);

        TableParam tableParamTbzt = new TableParam();
        tableParamTbzt.setTableName(ksJkryRcxxTb);
        tableParamTbzt.setColumnName("tbzt");
        tableParamTbzt.setColumnType("varchar(4)");
        tableParamTbzt.setColumnRemark("同步状态  0-未上报   1-已上报   -1-上报失败");
        tableParamList.add(tableParamTbzt);

        TableParam tableParamRgyzjg = new TableParam();
        tableParamRgyzjg.setTableName(ksJkryRcxxTb);
        tableParamRgyzjg.setColumnName("rgyzjg");
        tableParamRgyzjg.setColumnType("varchar(10)");
        tableParamRgyzjg.setColumnRemark("人工验证结果 1-通过 0-不通过");
        tableParamList.add(tableParamRgyzjg);

        for (TableParam tableParam : tableParamList) {
            int exist = utilsMapper.findColumnExist(dbName, ksJkryRcxxTb, tableParam.getColumnName());
            if (exist <= 0) {
                utilsMapper.alterTable(tableParam);
                log.info("数据库dbName:[{}] tbName:[{}] columName:[{}]变更", dbName, ksJkryRcxxTb, tableParam.getColumnName());
            }
        }
    }

    private void initCmdTable(String dbName) {
        List<String> filterTables = utilsMapper.filterTables(dbName, Arrays.asList("biz_send_command", "biz_send_command_dev"));
        if (!filterTables.contains("biz_send_command")) {
            utilsMapper.createCmdTable();
        }
        if (!filterTables.contains("biz_send_command_dev")) {
            utilsMapper.createCmdDevTable();
        }
    }

    private void alterSbxx(String dbName) {
        List<TableParam> tableParamList = Lists.newArrayList();
        String sbxxTb = "sb_sbxx";

        TableParam tableParamOsbb = new TableParam();
        tableParamOsbb.setTableName(sbxxTb);
        tableParamOsbb.setColumnName("osbb");
        tableParamOsbb.setColumnType("varchar(64)");
        tableParamOsbb.setColumnRemark("os版本");

        tableParamList.add(tableParamOsbb);

        TableParam tableParamNtpEnable = new TableParam();
        tableParamNtpEnable.setTableName(sbxxTb);
        tableParamNtpEnable.setColumnName("ntp_enable");
        tableParamNtpEnable.setColumnType("int(4) default 0");
        tableParamNtpEnable.setColumnRemark("ntp是否启用 1-启用 0-禁用");

        tableParamList.add(tableParamNtpEnable);

        TableParam tableParamNtpSource = new TableParam();
        tableParamNtpSource.setTableName(sbxxTb);
        tableParamNtpSource.setColumnName("ntp_source");
        tableParamNtpSource.setColumnType("varchar(128)");
        tableParamNtpSource.setColumnRemark("ntp源");

        tableParamList.add(tableParamNtpSource);

        TableParam tableParamNtpInterval = new TableParam();
        tableParamNtpInterval.setTableName(sbxxTb);
        tableParamNtpInterval.setColumnName("ntp_interval");
        tableParamNtpInterval.setColumnType("varchar(64)");
        tableParamNtpInterval.setColumnRemark("间隔时间");

        tableParamList.add(tableParamNtpInterval);

        for (TableParam tableParam : tableParamList) {
            int exist = utilsMapper.findColumnExist(dbName, sbxxTb, tableParam.getColumnName());
            if (exist <= 0) {
                utilsMapper.alterTable(tableParam);
                log.info("数据库dbName:[{}] tbName:[{}] columName:[{}]变更", dbName, sbxxTb, tableParam.getColumnName());
            }
        }
    }

    private void alterKsKsrcxx(String dbName) {
        List<TableParam> tableParamList = Lists.newArrayList();
        String ksKsrcxxTb = "ks_ksrcxx";

        TableParam tableParamJklsxh = new TableParam();
        tableParamJklsxh.setTableName(ksKsrcxxTb);
        tableParamJklsxh.setColumnName("tbzt");
        tableParamJklsxh.setColumnType("varchar(2)");
        tableParamJklsxh.setColumnRemark("同步状态");

        tableParamList.add(tableParamJklsxh);


        for (TableParam tableParam : tableParamList) {
            int exist = utilsMapper.findColumnExist(dbName, ksKsrcxxTb, tableParam.getColumnName());
            if (exist == 1) {
                utilsMapper.alterColumn(tableParam);
                log.info("数据库dbName:[{}] tbName:[{}] columName:[{}]变更", dbName, ksKsrcxxTb, tableParam.getColumnName());
            }
        }

        List<TableParam> paramCreateList = new ArrayList<>();
        TableParam tableParamXsd = new TableParam();

        tableParamXsd.setTableName(ksKsrcxxTb);
        tableParamXsd.setColumnName("xsd");
        tableParamXsd.setColumnType("varchar(32)");
        tableParamXsd.setColumnRemark("相似度");

        paramCreateList.add(tableParamXsd);

        TableParam sc_sfzp = new TableParam();
        sc_sfzp.setTableName(ksKsrcxxTb);
        sc_sfzp.setColumnName("sc_sfzp");
        sc_sfzp.setColumnType("varchar(128)");
        sc_sfzp.setColumnRemark("上传身份证照片");

        paramCreateList.add(sc_sfzp);

        TableParam sc_rlzp = new TableParam();
        sc_rlzp.setTableName(ksKsrcxxTb);
        sc_rlzp.setColumnName("sc_rlzp");
        sc_rlzp.setColumnType("varchar(128)");
        sc_rlzp.setColumnRemark("上传人脸照片");

        paramCreateList.add(sc_rlzp);

        for (TableParam tableParam : paramCreateList) {
            int exist = utilsMapper.findColumnExist(dbName, ksKsrcxxTb, tableParam.getColumnName());
            if (exist <= 0) {
                utilsMapper.alterTable(tableParam);
                log.info("数据库dbName:[{}] tbName:[{}] columName:[{}]变更", dbName, ksKsrcxxTb, tableParam.getColumnName());
            }
        }
    }
}
