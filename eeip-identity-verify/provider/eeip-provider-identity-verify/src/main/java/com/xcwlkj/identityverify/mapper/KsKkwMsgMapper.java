/**
 * xcwlkj.com Inc.
 * Copyright (c) 2025-2035 All Rights Reserved.
 */
package com.xcwlkj.identityverify.mapper;

import com.xcwlkj.identityverify.config.commonMapper.CustomMapper;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;
import com.xcwlkj.identityverify.model.domain.KsKkwMsg;



/**
 * 空考位消息数据库操作
 * <AUTHOR>
 * @version $Id: KsKkwMsgMapper.java, v 0.1 2025年07月28日 xcwlkj.com Exp $
 */
@Mapper
@Repository
public interface KsKkwMsgMapper extends CustomMapper<KsKkwMsg> {


}
