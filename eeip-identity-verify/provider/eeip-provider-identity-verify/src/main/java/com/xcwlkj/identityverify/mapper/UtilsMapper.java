package com.xcwlkj.identityverify.mapper;

import com.xcwlkj.identityverify.model.dto.TableParam;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository("idvUtilsMapper")
public interface UtilsMapper {

    public List<String> tableContains(@Param("dbName") String dbName, @Param("tableName")String tableName);

    public List<String> filterTables(@Param("dbName") String dbName,@Param("tables")List<String> tables);
    //获取列是否存在
    public Integer findColumnExist(@Param("dbName") String dbName, @Param("tableName")String tableName, @Param("columnName")String columnName);

    public Integer alterTable(@Param("param") TableParam param);

    public Integer alterColumn(@Param("param") TableParam param);

    public Integer createCsDzgh();

    public Integer createSbIpdfp();

    public Integer createSbSbjcb();

    Integer createKsBmxxKstzz();

    Integer createKsYdsbSbkszs();

    int createCmdTable();

    int createCmdDevTable();

    int createSbsj();

    int createBizCsxxExt();

    int createBizFsgjTask();

    int createBizFsgjTaskJg();

    void createKsKkwMsg();
}
