/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2035 All Rights Reserved.
 */
package com.xcwlkj.identityverify.service.impl;

import com.xcwlkj.model.enums.ScztEnum;
import com.xcwlkj.util.IdGenerateUtil;
import com.xcwlkj.util.StringUtil;
import org.springframework.stereotype.Service;

import com.xcwlkj.identityverify.model.domain.KsKkwMsg;

import com.xcwlkj.identityverify.mapper.KsKkwMsgMapper;
import com.xcwlkj.identityverify.service.KsKkwMsgService;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;


/**
 * 空考位消息服务实现
 * <AUTHOR>
 * @version $Id: KsKkwMsgServiceImpl.java, v 0.1 2025年07月28日 xcwlkj.com Exp $
 */
@Service("ksKkwMsgService")
public class KsKkwMsgServiceImpl extends BaseServiceImpl<KsKkwMsgMapper, KsKkwMsg> implements KsKkwMsgService {

    @Resource
    private KsKkwMsgMapper modelMapper;

    @Override
    public List<KsKkwMsg> findByKsjhbhAndCcm(String ksjhbh, String ccm) {
        Example example = new Example(KsKkwMsg.class);
        example.createCriteria()
                .andEqualTo("ksjhbh", ksjhbh)
                .andEqualTo("ccm", ccm)
                .andEqualTo("sczt", ScztEnum.NOTDEL.getCode());
        return modelMapper.selectByExample(example);
    }

    @Override
    public List<KsKkwMsg> findByKsjhbhAndCcmAndKch(String ksjhbh, String ccm, String kch) {
        Example example = new Example(KsKkwMsg.class);
        example.createCriteria()
                .andEqualTo("ksjhbh", ksjhbh)
                .andEqualTo("ccm", ccm)
                .andEqualTo("kch", kch)
                .andEqualTo("sczt", ScztEnum.NOTDEL.getCode());
        return modelMapper.selectByExample(example);
    }

    @Override
    public void insertKkwMsg(KsKkwMsg ksKkwMsg) {
        if (StringUtil.isEmpty(ksKkwMsg.getId())) {
            ksKkwMsg.setId(IdGenerateUtil.generateId());
        }
        if (ksKkwMsg.getCreateTime() == null) {
            ksKkwMsg.setCreateTime(new Date());
        }
        if (ksKkwMsg.getUpdateTime() == null) {
            ksKkwMsg.setUpdateTime(new Date());
        }
        if (StringUtil.isEmpty(ksKkwMsg.getSczt())) {
            ksKkwMsg.setSczt(ScztEnum.NOTDEL.getCode());
        }
        modelMapper.insertSelective(ksKkwMsg);
    }

    @Override
    public List<KsKkwMsg> findBySn(String sn) {
        Example example = new Example(KsKkwMsg.class);
        example.createCriteria()
                .andEqualTo("sn", sn)
                .andEqualTo("sczt", ScztEnum.NOTDEL.getCode());
        return modelMapper.selectByExample(example);
    }

    @Override
    public List<KsKkwMsg> findByZkzh(String zkzh) {
        Example example = new Example(KsKkwMsg.class);
        example.createCriteria()
                .andEqualTo("ksZkzh", zkzh)
                .andEqualTo("sczt", ScztEnum.NOTDEL.getCode());
        return modelMapper.selectByExample(example);
    }
}
