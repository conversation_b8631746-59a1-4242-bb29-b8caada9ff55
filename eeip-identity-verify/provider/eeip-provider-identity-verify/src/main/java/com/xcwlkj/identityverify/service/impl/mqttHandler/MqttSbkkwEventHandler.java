package com.xcwlkj.identityverify.service.impl.mqttHandler;

import com.xcwlkj.identityverify.third.unifyaccess.mqtt.model.enums.MqttDevEventEnum;
import com.xcwlkj.identityverify.third.unifyaccess.mqtt.model.enums.MqttUnifyAccessEventHandlerEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 处理上报空考位
 */
@Slf4j
@Service
public class MqttSbkkwEventHandler extends MqttBaseEventHandler{

    @Override
    protected HanleResult doHanleEvent(String productId, Object req, String deviceId, MqttDevEventEnum devEventEnum, String payloadStr) {
        return null;
    }

    @Override
    protected MqttUnifyAccessEventHandlerEnum getEventHandlerType() {
        return MqttUnifyAccessEventHandlerEnum.SBKKW_EVENT_HANDLER;
    }
}
